// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alarm_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$alarmServiceHash() => r'b04dc892dae1d1dfe464bbcebe5a2a2b633db3da';

/// See also [alarmService].
@ProviderFor(alarmService)
final alarmServiceProvider = AutoDisposeProvider<AlarmService>.internal(
  alarmService,
  name: r'alarmServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$alarmServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AlarmServiceRef = AutoDisposeProviderRef<AlarmService>;
String _$alarmRepositoryHash() => r'bc4ce43ec06c27daaf851e25876f4aa6f5ac62c0';

/// See also [alarmRepository].
@ProviderFor(alarmRepository)
final alarmRepositoryProvider = AutoDisposeProvider<AlarmRepository>.internal(
  alarmRepository,
  name: r'alarmRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$alarmRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AlarmRepositoryRef = AutoDisposeProviderRef<AlarmRepository>;
String _$alarmSettingsHash() => r'f99fbdb0f2a3ff10148998de83b479a742ddab78';

/// See also [AlarmSettings].
@ProviderFor(AlarmSettings)
final alarmSettingsProvider =
    AutoDisposeNotifierProvider<AlarmSettings, AlarmSettingsModel>.internal(
      AlarmSettings.new,
      name: r'alarmSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$alarmSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AlarmSettings = AutoDisposeNotifier<AlarmSettingsModel>;
String _$alarmStateHash() => r'8516853bbad91c6645d0904120ddb3b2d1cc98b1';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$AlarmState
    extends BuildlessAutoDisposeAsyncNotifier<List<AlarmModel>> {
  late final String userId;

  FutureOr<List<AlarmModel>> build(String userId);
}

/// See also [AlarmState].
@ProviderFor(AlarmState)
const alarmStateProvider = AlarmStateFamily();

/// See also [AlarmState].
class AlarmStateFamily extends Family<AsyncValue<List<AlarmModel>>> {
  /// See also [AlarmState].
  const AlarmStateFamily();

  /// See also [AlarmState].
  AlarmStateProvider call(String userId) {
    return AlarmStateProvider(userId);
  }

  @override
  AlarmStateProvider getProviderOverride(
    covariant AlarmStateProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'alarmStateProvider';
}

/// See also [AlarmState].
class AlarmStateProvider
    extends AutoDisposeAsyncNotifierProviderImpl<AlarmState, List<AlarmModel>> {
  /// See also [AlarmState].
  AlarmStateProvider(String userId)
    : this._internal(
        () => AlarmState()..userId = userId,
        from: alarmStateProvider,
        name: r'alarmStateProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$alarmStateHash,
        dependencies: AlarmStateFamily._dependencies,
        allTransitiveDependencies: AlarmStateFamily._allTransitiveDependencies,
        userId: userId,
      );

  AlarmStateProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  FutureOr<List<AlarmModel>> runNotifierBuild(covariant AlarmState notifier) {
    return notifier.build(userId);
  }

  @override
  Override overrideWith(AlarmState Function() create) {
    return ProviderOverride(
      origin: this,
      override: AlarmStateProvider._internal(
        () => create()..userId = userId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<AlarmState, List<AlarmModel>>
  createElement() {
    return _AlarmStateProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AlarmStateProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AlarmStateRef on AutoDisposeAsyncNotifierProviderRef<List<AlarmModel>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _AlarmStateProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<AlarmState, List<AlarmModel>>
    with AlarmStateRef {
  _AlarmStateProviderElement(super.provider);

  @override
  String get userId => (origin as AlarmStateProvider).userId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
