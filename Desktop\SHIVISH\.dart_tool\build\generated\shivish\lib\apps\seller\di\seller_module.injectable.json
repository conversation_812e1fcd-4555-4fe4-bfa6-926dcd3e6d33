[{"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:shivish/apps/seller/di/seller_module.dart", "name": "SellerModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "firestore"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "firestore", "orderPosition": 0}, {"type": {"import": "package:shivish/shared/services/seller/seller_service.dart", "name": "SellerService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/shared/services/seller/seller_service.dart", "name": "SellerService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:shivish/apps/seller/di/seller_module.dart", "name": "SellerModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "sellerService"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "sellerService", "orderPosition": 0}, {"type": {"import": "package:shivish/apps/seller/notifiers/seller_notifier.dart", "name": "SellerNotifier", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/seller/notifiers/seller_notifier.dart", "name": "SellerNotifier", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:shivish/apps/seller/di/seller_module.dart", "name": "SellerModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "sellerNotifier"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "sellerNotifier", "orderPosition": 0}, {"type": {"import": "package:shivish/apps/seller/domain/repositories/seller_repository.dart", "name": "SellerRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/seller/data/repositories/seller_repository_impl.dart", "name": "SellerRepositoryImpl", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "moduleConfig": {"isAbstract": false, "isMethod": true, "type": {"import": "package:shivish/apps/seller/di/seller_module.dart", "name": "SellerModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "provideSellerRepository"}, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "firestore", "isFactoryParam": false, "isPositional": true}], "constructorName": "provideSellerRepository", "orderPosition": 0}]