// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MediaMetadataImpl _$$MediaMetadataImplFromJson(Map<String, dynamic> json) =>
    _$MediaMetadataImpl(
      id: json['id'] as String,
      fileName: json['fileName'] as String,
      fileExtension: json['fileExtension'] as String,
      mimeType: json['mimeType'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      duration: (json['duration'] as num?)?.toDouble(),
      codec: json['codec'] as String?,
      bitrate: json['bitrate'] as String?,
      resolution: json['resolution'] as String?,
      orientation: json['orientation'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$MediaMetadataImplToJson(_$MediaMetadataImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'fileName': instance.fileName,
      'fileExtension': instance.fileExtension,
      'mimeType': instance.mimeType,
      'fileSize': instance.fileSize,
      'width': instance.width,
      'height': instance.height,
      'duration': instance.duration,
      'codec': instance.codec,
      'bitrate': instance.bitrate,
      'resolution': instance.resolution,
      'orientation': instance.orientation,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

_$MediaVariantImpl _$$MediaVariantImplFromJson(Map<String, dynamic> json) =>
    _$MediaVariantImpl(
      id: json['id'] as String,
      quality: $enumDecode(_$MediaQualityEnumMap, json['quality']),
      url: json['url'] as String,
      path: json['path'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      duration: (json['duration'] as num?)?.toDouble(),
      codec: json['codec'] as String?,
      bitrate: json['bitrate'] as String?,
      resolution: json['resolution'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$MediaVariantImplToJson(_$MediaVariantImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'quality': _$MediaQualityEnumMap[instance.quality]!,
      'url': instance.url,
      'path': instance.path,
      'fileSize': instance.fileSize,
      'width': instance.width,
      'height': instance.height,
      'duration': instance.duration,
      'codec': instance.codec,
      'bitrate': instance.bitrate,
      'resolution': instance.resolution,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

const _$MediaQualityEnumMap = {
  MediaQuality.original: 0,
  MediaQuality.high: 1,
  MediaQuality.medium: 2,
  MediaQuality.low: 3,
  MediaQuality.thumbnail: 4,
};

_$MediaModelImpl _$$MediaModelImplFromJson(Map<String, dynamic> json) =>
    _$MediaModelImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      url: json['url'] as String,
      type: $enumDecode(_$MediaTypeEnumMap, json['type']),
      uploadedBy: json['uploadedBy'] as String,
      uploadedAt: DateTime.parse(json['uploadedAt'] as String),
      status: $enumDecode(_$MediaStatusEnumMap, json['status']),
      rejectionReason: json['rejectionReason'] as String?,
    );

Map<String, dynamic> _$$MediaModelImplToJson(_$MediaModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'url': instance.url,
      'type': _$MediaTypeEnumMap[instance.type]!,
      'uploadedBy': instance.uploadedBy,
      'uploadedAt': instance.uploadedAt.toIso8601String(),
      'status': _$MediaStatusEnumMap[instance.status]!,
      'rejectionReason': instance.rejectionReason,
    };

const _$MediaTypeEnumMap = {
  MediaType.audio: 'audio',
  MediaType.video: 'video',
  MediaType.image: 'image',
};

const _$MediaStatusEnumMap = {
  MediaStatus.pending: 'pending',
  MediaStatus.approved: 'approved',
  MediaStatus.rejected: 'rejected',
  MediaStatus.active: 'active',
  MediaStatus.processing: 'processing',
  MediaStatus.failed: 'failed',
  MediaStatus.deleted: 'deleted',
};
