// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_submission_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListSubmissionModelImpl _$$ListSubmissionModelImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionModelImpl(
  id: json['id'] as String,
  buyerId: json['buyerId'] as String,
  sellerId: json['sellerId'] as String,
  shoppingList: ShoppingListModel.fromJson(
    json['shoppingList'] as Map<String, dynamic>,
  ),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$ListSubmissionModelImplToJson(
  _$ListSubmissionModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'buyerId': instance.buyerId,
  'sellerId': instance.sellerId,
  'shoppingList': instance.shoppingList.toJson(),
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
  'metadata': instance.metadata,
};

_$ListSubmissionEventSubmittedImpl _$$ListSubmissionEventSubmittedImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionEventSubmittedImpl(
  submissionId: json['submissionId'] as String,
  buyerId: json['buyerId'] as String,
  sellerId: json['sellerId'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionEventSubmittedImplToJson(
  _$ListSubmissionEventSubmittedImpl instance,
) => <String, dynamic>{
  'submissionId': instance.submissionId,
  'buyerId': instance.buyerId,
  'sellerId': instance.sellerId,
  'timestamp': instance.timestamp.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionEventQuotedImpl _$$ListSubmissionEventQuotedImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionEventQuotedImpl(
  submissionId: json['submissionId'] as String,
  sellerId: json['sellerId'] as String,
  amount: (json['amount'] as num).toDouble(),
  timestamp: DateTime.parse(json['timestamp'] as String),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionEventQuotedImplToJson(
  _$ListSubmissionEventQuotedImpl instance,
) => <String, dynamic>{
  'submissionId': instance.submissionId,
  'sellerId': instance.sellerId,
  'amount': instance.amount,
  'timestamp': instance.timestamp.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionEventCounterOfferedImpl
_$$ListSubmissionEventCounterOfferedImplFromJson(Map<String, dynamic> json) =>
    _$ListSubmissionEventCounterOfferedImpl(
      submissionId: json['submissionId'] as String,
      buyerId: json['buyerId'] as String,
      amount: (json['amount'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$ListSubmissionEventCounterOfferedImplToJson(
  _$ListSubmissionEventCounterOfferedImpl instance,
) => <String, dynamic>{
  'submissionId': instance.submissionId,
  'buyerId': instance.buyerId,
  'amount': instance.amount,
  'timestamp': instance.timestamp.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionEventAcceptedImpl _$$ListSubmissionEventAcceptedImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionEventAcceptedImpl(
  submissionId: json['submissionId'] as String,
  actorId: json['actorId'] as String,
  amount: (json['amount'] as num).toDouble(),
  timestamp: DateTime.parse(json['timestamp'] as String),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionEventAcceptedImplToJson(
  _$ListSubmissionEventAcceptedImpl instance,
) => <String, dynamic>{
  'submissionId': instance.submissionId,
  'actorId': instance.actorId,
  'amount': instance.amount,
  'timestamp': instance.timestamp.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionEventRejectedImpl _$$ListSubmissionEventRejectedImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionEventRejectedImpl(
  submissionId: json['submissionId'] as String,
  actorId: json['actorId'] as String,
  reason: json['reason'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionEventRejectedImplToJson(
  _$ListSubmissionEventRejectedImpl instance,
) => <String, dynamic>{
  'submissionId': instance.submissionId,
  'actorId': instance.actorId,
  'reason': instance.reason,
  'timestamp': instance.timestamp.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionEventExpiredImpl _$$ListSubmissionEventExpiredImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionEventExpiredImpl(
  submissionId: json['submissionId'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionEventExpiredImplToJson(
  _$ListSubmissionEventExpiredImpl instance,
) => <String, dynamic>{
  'submissionId': instance.submissionId,
  'timestamp': instance.timestamp.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionEventCancelledImpl _$$ListSubmissionEventCancelledImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionEventCancelledImpl(
  submissionId: json['submissionId'] as String,
  actorId: json['actorId'] as String,
  reason: json['reason'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionEventCancelledImplToJson(
  _$ListSubmissionEventCancelledImpl instance,
) => <String, dynamic>{
  'submissionId': instance.submissionId,
  'actorId': instance.actorId,
  'reason': instance.reason,
  'timestamp': instance.timestamp.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionStatusPendingImpl _$$ListSubmissionStatusPendingImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionStatusPendingImpl(
  submittedAt: DateTime.parse(json['submittedAt'] as String),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionStatusPendingImplToJson(
  _$ListSubmissionStatusPendingImpl instance,
) => <String, dynamic>{
  'submittedAt': instance.submittedAt.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionStatusQuotedImpl _$$ListSubmissionStatusQuotedImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionStatusQuotedImpl(
  quotedAt: DateTime.parse(json['quotedAt'] as String),
  amount: (json['amount'] as num).toDouble(),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionStatusQuotedImplToJson(
  _$ListSubmissionStatusQuotedImpl instance,
) => <String, dynamic>{
  'quotedAt': instance.quotedAt.toIso8601String(),
  'amount': instance.amount,
  'runtimeType': instance.$type,
};

_$ListSubmissionStatusCounterOfferedImpl
_$$ListSubmissionStatusCounterOfferedImplFromJson(Map<String, dynamic> json) =>
    _$ListSubmissionStatusCounterOfferedImpl(
      counterOfferedAt: DateTime.parse(json['counterOfferedAt'] as String),
      amount: (json['amount'] as num).toDouble(),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$ListSubmissionStatusCounterOfferedImplToJson(
  _$ListSubmissionStatusCounterOfferedImpl instance,
) => <String, dynamic>{
  'counterOfferedAt': instance.counterOfferedAt.toIso8601String(),
  'amount': instance.amount,
  'runtimeType': instance.$type,
};

_$ListSubmissionStatusAcceptedImpl _$$ListSubmissionStatusAcceptedImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionStatusAcceptedImpl(
  acceptedAt: DateTime.parse(json['acceptedAt'] as String),
  amount: (json['amount'] as num).toDouble(),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionStatusAcceptedImplToJson(
  _$ListSubmissionStatusAcceptedImpl instance,
) => <String, dynamic>{
  'acceptedAt': instance.acceptedAt.toIso8601String(),
  'amount': instance.amount,
  'runtimeType': instance.$type,
};

_$ListSubmissionStatusRejectedImpl _$$ListSubmissionStatusRejectedImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionStatusRejectedImpl(
  rejectedAt: DateTime.parse(json['rejectedAt'] as String),
  reason: json['reason'] as String,
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionStatusRejectedImplToJson(
  _$ListSubmissionStatusRejectedImpl instance,
) => <String, dynamic>{
  'rejectedAt': instance.rejectedAt.toIso8601String(),
  'reason': instance.reason,
  'runtimeType': instance.$type,
};

_$ListSubmissionStatusExpiredImpl _$$ListSubmissionStatusExpiredImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionStatusExpiredImpl(
  expiredAt: DateTime.parse(json['expiredAt'] as String),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ListSubmissionStatusExpiredImplToJson(
  _$ListSubmissionStatusExpiredImpl instance,
) => <String, dynamic>{
  'expiredAt': instance.expiredAt.toIso8601String(),
  'runtimeType': instance.$type,
};

_$ListSubmissionStatusCancelledImpl
_$$ListSubmissionStatusCancelledImplFromJson(Map<String, dynamic> json) =>
    _$ListSubmissionStatusCancelledImpl(
      cancelledAt: DateTime.parse(json['cancelledAt'] as String),
      reason: json['reason'] as String,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$ListSubmissionStatusCancelledImplToJson(
  _$ListSubmissionStatusCancelledImpl instance,
) => <String, dynamic>{
  'cancelledAt': instance.cancelledAt.toIso8601String(),
  'reason': instance.reason,
  'runtimeType': instance.$type,
};
