// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UpiPaymentSettingsImpl _$$UpiPaymentSettingsImplFromJson(
  Map<String, dynamic> json,
) => _$UpiPaymentSettingsImpl(
  upiId: json['upiId'] as String,
  isVerified: json['isVerified'] as bool? ?? false,
  isEnabled: json['isEnabled'] as bool? ?? true,
  verifiedAt: json['verifiedAt'] == null
      ? null
      : DateTime.parse(json['verifiedAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$UpiPaymentSettingsImplToJson(
  _$UpiPaymentSettingsImpl instance,
) => <String, dynamic>{
  'upiId': instance.upiId,
  'isVerified': instance.isVerified,
  'isEnabled': instance.isEnabled,
  'verifiedAt': instance.verifiedAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};
