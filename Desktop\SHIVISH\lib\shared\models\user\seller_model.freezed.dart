// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

SellerModel _$SellerModelFromJson(Map<String, dynamic> json) {
  return _SellerModel.fromJson(json);
}

/// @nodoc
mixin _$SellerModel {
  String get id => throw _privateConstructorUsedError;
  String get businessName => throw _privateConstructorUsedError;
  String get businessAddress => throw _privateConstructorUsedError;
  @GeoPointConverter()
  GeoPoint get location => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  bool get isSuspended => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  String get profileImageUrl => throw _privateConstructorUsedError;
  String get businessLicenseUrl => throw _privateConstructorUsedError;
  String get taxRegistrationUrl => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  double get distance => throw _privateConstructorUsedError;
  int get activeOrders => throw _privateConstructorUsedError;
  int get maxOrders => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get deliveryTime => throw _privateConstructorUsedError;

  /// Serializes this SellerModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SellerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SellerModelCopyWith<SellerModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SellerModelCopyWith<$Res> {
  factory $SellerModelCopyWith(
    SellerModel value,
    $Res Function(SellerModel) then,
  ) = _$SellerModelCopyWithImpl<$Res, SellerModel>;
  @useResult
  $Res call({
    String id,
    String businessName,
    String businessAddress,
    @GeoPointConverter() GeoPoint location,
    bool isDeleted,
    bool isSuspended,
    bool isVerified,
    String profileImageUrl,
    String businessLicenseUrl,
    String taxRegistrationUrl,
    Map<String, dynamic> metadata,
    double distance,
    int activeOrders,
    int maxOrders,
    double rating,
    int deliveryTime,
  });
}

/// @nodoc
class _$SellerModelCopyWithImpl<$Res, $Val extends SellerModel>
    implements $SellerModelCopyWith<$Res> {
  _$SellerModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SellerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessName = null,
    Object? businessAddress = null,
    Object? location = null,
    Object? isDeleted = null,
    Object? isSuspended = null,
    Object? isVerified = null,
    Object? profileImageUrl = null,
    Object? businessLicenseUrl = null,
    Object? taxRegistrationUrl = null,
    Object? metadata = null,
    Object? distance = null,
    Object? activeOrders = null,
    Object? maxOrders = null,
    Object? rating = null,
    Object? deliveryTime = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            businessName: null == businessName
                ? _value.businessName
                : businessName // ignore: cast_nullable_to_non_nullable
                      as String,
            businessAddress: null == businessAddress
                ? _value.businessAddress
                : businessAddress // ignore: cast_nullable_to_non_nullable
                      as String,
            location: null == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as GeoPoint,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isSuspended: null == isSuspended
                ? _value.isSuspended
                : isSuspended // ignore: cast_nullable_to_non_nullable
                      as bool,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            profileImageUrl: null == profileImageUrl
                ? _value.profileImageUrl
                : profileImageUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            businessLicenseUrl: null == businessLicenseUrl
                ? _value.businessLicenseUrl
                : businessLicenseUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            taxRegistrationUrl: null == taxRegistrationUrl
                ? _value.taxRegistrationUrl
                : taxRegistrationUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            distance: null == distance
                ? _value.distance
                : distance // ignore: cast_nullable_to_non_nullable
                      as double,
            activeOrders: null == activeOrders
                ? _value.activeOrders
                : activeOrders // ignore: cast_nullable_to_non_nullable
                      as int,
            maxOrders: null == maxOrders
                ? _value.maxOrders
                : maxOrders // ignore: cast_nullable_to_non_nullable
                      as int,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            deliveryTime: null == deliveryTime
                ? _value.deliveryTime
                : deliveryTime // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SellerModelImplCopyWith<$Res>
    implements $SellerModelCopyWith<$Res> {
  factory _$$SellerModelImplCopyWith(
    _$SellerModelImpl value,
    $Res Function(_$SellerModelImpl) then,
  ) = __$$SellerModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String businessName,
    String businessAddress,
    @GeoPointConverter() GeoPoint location,
    bool isDeleted,
    bool isSuspended,
    bool isVerified,
    String profileImageUrl,
    String businessLicenseUrl,
    String taxRegistrationUrl,
    Map<String, dynamic> metadata,
    double distance,
    int activeOrders,
    int maxOrders,
    double rating,
    int deliveryTime,
  });
}

/// @nodoc
class __$$SellerModelImplCopyWithImpl<$Res>
    extends _$SellerModelCopyWithImpl<$Res, _$SellerModelImpl>
    implements _$$SellerModelImplCopyWith<$Res> {
  __$$SellerModelImplCopyWithImpl(
    _$SellerModelImpl _value,
    $Res Function(_$SellerModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SellerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessName = null,
    Object? businessAddress = null,
    Object? location = null,
    Object? isDeleted = null,
    Object? isSuspended = null,
    Object? isVerified = null,
    Object? profileImageUrl = null,
    Object? businessLicenseUrl = null,
    Object? taxRegistrationUrl = null,
    Object? metadata = null,
    Object? distance = null,
    Object? activeOrders = null,
    Object? maxOrders = null,
    Object? rating = null,
    Object? deliveryTime = null,
  }) {
    return _then(
      _$SellerModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        businessName: null == businessName
            ? _value.businessName
            : businessName // ignore: cast_nullable_to_non_nullable
                  as String,
        businessAddress: null == businessAddress
            ? _value.businessAddress
            : businessAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        location: null == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as GeoPoint,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isSuspended: null == isSuspended
            ? _value.isSuspended
            : isSuspended // ignore: cast_nullable_to_non_nullable
                  as bool,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        profileImageUrl: null == profileImageUrl
            ? _value.profileImageUrl
            : profileImageUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        businessLicenseUrl: null == businessLicenseUrl
            ? _value.businessLicenseUrl
            : businessLicenseUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        taxRegistrationUrl: null == taxRegistrationUrl
            ? _value.taxRegistrationUrl
            : taxRegistrationUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        distance: null == distance
            ? _value.distance
            : distance // ignore: cast_nullable_to_non_nullable
                  as double,
        activeOrders: null == activeOrders
            ? _value.activeOrders
            : activeOrders // ignore: cast_nullable_to_non_nullable
                  as int,
        maxOrders: null == maxOrders
            ? _value.maxOrders
            : maxOrders // ignore: cast_nullable_to_non_nullable
                  as int,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        deliveryTime: null == deliveryTime
            ? _value.deliveryTime
            : deliveryTime // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SellerModelImpl implements _SellerModel {
  const _$SellerModelImpl({
    required this.id,
    required this.businessName,
    required this.businessAddress,
    @GeoPointConverter() required this.location,
    required this.isDeleted,
    required this.isSuspended,
    required this.isVerified,
    required this.profileImageUrl,
    required this.businessLicenseUrl,
    required this.taxRegistrationUrl,
    required final Map<String, dynamic> metadata,
    this.distance = 0.0,
    this.activeOrders = 0,
    this.maxOrders = 10,
    this.rating = 0.0,
    this.deliveryTime = 0,
  }) : _metadata = metadata;

  factory _$SellerModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SellerModelImplFromJson(json);

  @override
  final String id;
  @override
  final String businessName;
  @override
  final String businessAddress;
  @override
  @GeoPointConverter()
  final GeoPoint location;
  @override
  final bool isDeleted;
  @override
  final bool isSuspended;
  @override
  final bool isVerified;
  @override
  final String profileImageUrl;
  @override
  final String businessLicenseUrl;
  @override
  final String taxRegistrationUrl;
  final Map<String, dynamic> _metadata;
  @override
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  @JsonKey()
  final double distance;
  @override
  @JsonKey()
  final int activeOrders;
  @override
  @JsonKey()
  final int maxOrders;
  @override
  @JsonKey()
  final double rating;
  @override
  @JsonKey()
  final int deliveryTime;

  @override
  String toString() {
    return 'SellerModel(id: $id, businessName: $businessName, businessAddress: $businessAddress, location: $location, isDeleted: $isDeleted, isSuspended: $isSuspended, isVerified: $isVerified, profileImageUrl: $profileImageUrl, businessLicenseUrl: $businessLicenseUrl, taxRegistrationUrl: $taxRegistrationUrl, metadata: $metadata, distance: $distance, activeOrders: $activeOrders, maxOrders: $maxOrders, rating: $rating, deliveryTime: $deliveryTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SellerModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.businessName, businessName) ||
                other.businessName == businessName) &&
            (identical(other.businessAddress, businessAddress) ||
                other.businessAddress == businessAddress) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.isSuspended, isSuspended) ||
                other.isSuspended == isSuspended) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.businessLicenseUrl, businessLicenseUrl) ||
                other.businessLicenseUrl == businessLicenseUrl) &&
            (identical(other.taxRegistrationUrl, taxRegistrationUrl) ||
                other.taxRegistrationUrl == taxRegistrationUrl) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.distance, distance) ||
                other.distance == distance) &&
            (identical(other.activeOrders, activeOrders) ||
                other.activeOrders == activeOrders) &&
            (identical(other.maxOrders, maxOrders) ||
                other.maxOrders == maxOrders) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.deliveryTime, deliveryTime) ||
                other.deliveryTime == deliveryTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    businessName,
    businessAddress,
    location,
    isDeleted,
    isSuspended,
    isVerified,
    profileImageUrl,
    businessLicenseUrl,
    taxRegistrationUrl,
    const DeepCollectionEquality().hash(_metadata),
    distance,
    activeOrders,
    maxOrders,
    rating,
    deliveryTime,
  );

  /// Create a copy of SellerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SellerModelImplCopyWith<_$SellerModelImpl> get copyWith =>
      __$$SellerModelImplCopyWithImpl<_$SellerModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SellerModelImplToJson(this);
  }
}

abstract class _SellerModel implements SellerModel {
  const factory _SellerModel({
    required final String id,
    required final String businessName,
    required final String businessAddress,
    @GeoPointConverter() required final GeoPoint location,
    required final bool isDeleted,
    required final bool isSuspended,
    required final bool isVerified,
    required final String profileImageUrl,
    required final String businessLicenseUrl,
    required final String taxRegistrationUrl,
    required final Map<String, dynamic> metadata,
    final double distance,
    final int activeOrders,
    final int maxOrders,
    final double rating,
    final int deliveryTime,
  }) = _$SellerModelImpl;

  factory _SellerModel.fromJson(Map<String, dynamic> json) =
      _$SellerModelImpl.fromJson;

  @override
  String get id;
  @override
  String get businessName;
  @override
  String get businessAddress;
  @override
  @GeoPointConverter()
  GeoPoint get location;
  @override
  bool get isDeleted;
  @override
  bool get isSuspended;
  @override
  bool get isVerified;
  @override
  String get profileImageUrl;
  @override
  String get businessLicenseUrl;
  @override
  String get taxRegistrationUrl;
  @override
  Map<String, dynamic> get metadata;
  @override
  double get distance;
  @override
  int get activeOrders;
  @override
  int get maxOrders;
  @override
  double get rating;
  @override
  int get deliveryTime;

  /// Create a copy of SellerModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SellerModelImplCopyWith<_$SellerModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
