// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeliveryPersonModelImpl _$$DeliveryPersonModelImplFromJson(
  Map<String, dynamic> json,
) => _$DeliveryPersonModelImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  phone: json['phone'] as String,
  location: const GeoPointConverter().fromJson(
    json['location'] as Map<String, dynamic>,
  ),
  status: $enumDecode(_$DeliveryPersonStatusEnumMap, json['status']),
  isVerified: json['isVerified'] as bool,
  isActive: json['isActive'] as bool,
  rating: (json['rating'] as num).toDouble(),
  totalDeliveries: (json['totalDeliveries'] as num).toInt(),
  totalReviews: (json['totalReviews'] as num).toInt(),
  vehicleType: json['vehicleType'] as String,
  vehicleNumber: json['vehicleNumber'] as String,
  profileImage: json['profileImage'] as String?,
  aadharNumber: json['aadharNumber'] as String?,
  drivingLicense: json['drivingLicense'] as String?,
  isAadharVerified: json['isAadharVerified'] as bool? ?? false,
  isDrivingLicenseVerified: json['isDrivingLicenseVerified'] as bool? ?? false,
  activeDeliveries: (json['activeDeliveries'] as num?)?.toInt() ?? 0,
  maxActiveDeliveries: (json['maxActiveDeliveries'] as num?)?.toInt() ?? 5,
  currentEarnings: (json['currentEarnings'] as num?)?.toDouble() ?? 0.0,
  totalEarnings: (json['totalEarnings'] as num?)?.toDouble() ?? 0.0,
  serviceAreas:
      (json['serviceAreas'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  approvedBy: json['approvedBy'] as String?,
  approverRole: json['approverRole'] as String?,
  approvedAt: json['approvedAt'] == null
      ? null
      : DateTime.parse(json['approvedAt'] as String),
  lastLocationUpdate: json['lastLocationUpdate'] == null
      ? null
      : DateTime.parse(json['lastLocationUpdate'] as String),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  vehicleBrand: json['vehicleBrand'] as String?,
  vehicleModel: json['vehicleModel'] as String?,
  vehicleYear: json['vehicleYear'] as String?,
  vehicleColor: json['vehicleColor'] as String?,
  licensePlate: json['licensePlate'] as String?,
);

Map<String, dynamic> _$$DeliveryPersonModelImplToJson(
  _$DeliveryPersonModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'email': instance.email,
  'phone': instance.phone,
  'location': const GeoPointConverter().toJson(instance.location),
  'status': _$DeliveryPersonStatusEnumMap[instance.status]!,
  'isVerified': instance.isVerified,
  'isActive': instance.isActive,
  'rating': instance.rating,
  'totalDeliveries': instance.totalDeliveries,
  'totalReviews': instance.totalReviews,
  'vehicleType': instance.vehicleType,
  'vehicleNumber': instance.vehicleNumber,
  'profileImage': instance.profileImage,
  'aadharNumber': instance.aadharNumber,
  'drivingLicense': instance.drivingLicense,
  'isAadharVerified': instance.isAadharVerified,
  'isDrivingLicenseVerified': instance.isDrivingLicenseVerified,
  'activeDeliveries': instance.activeDeliveries,
  'maxActiveDeliveries': instance.maxActiveDeliveries,
  'currentEarnings': instance.currentEarnings,
  'totalEarnings': instance.totalEarnings,
  'serviceAreas': instance.serviceAreas,
  'metadata': instance.metadata,
  'approvedBy': instance.approvedBy,
  'approverRole': instance.approverRole,
  'approvedAt': instance.approvedAt?.toIso8601String(),
  'lastLocationUpdate': instance.lastLocationUpdate?.toIso8601String(),
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'vehicleBrand': instance.vehicleBrand,
  'vehicleModel': instance.vehicleModel,
  'vehicleYear': instance.vehicleYear,
  'vehicleColor': instance.vehicleColor,
  'licensePlate': instance.licensePlate,
};

const _$DeliveryPersonStatusEnumMap = {
  DeliveryPersonStatus.available: 'available',
  DeliveryPersonStatus.delivering: 'delivering',
  DeliveryPersonStatus.offline: 'offline',
  DeliveryPersonStatus.onBreak: 'on_break',
};
