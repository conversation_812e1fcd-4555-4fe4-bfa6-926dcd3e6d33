// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alarm_state_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$alarmStateNotifierHash() =>
    r'31a6f8df81c15a439a109c1a8e60ce63ed165e84';

/// See also [AlarmStateNotifier].
@ProviderFor(AlarmStateNotifier)
final alarmStateNotifierProvider =
    AutoDisposeNotifierProvider<
      AlarmStateNotifier,
      alarm_state.AlarmState
    >.internal(
      AlarmStateNotifier.new,
      name: r'alarmStateNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$alarmStateNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AlarmStateNotifier = AutoDisposeNotifier<alarm_state.AlarmState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
