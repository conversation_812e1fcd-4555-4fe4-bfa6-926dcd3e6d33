// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MediaMetadataImpl _$$MediaMetadataImplFromJson(Map<String, dynamic> json) =>
    _$MediaMetadataImpl(
      contentType: json['contentType'] as String?,
      size: (json['size'] as num?)?.toInt(),
      timeCreated: json['timeCreated'] == null
          ? null
          : DateTime.parse(json['timeCreated'] as String),
      updated: json['updated'] == null
          ? null
          : DateTime.parse(json['updated'] as String),
      md5Hash: json['md5Hash'] as String?,
      generation: json['generation'] as String?,
      metageneration: json['metageneration'] as String?,
    );

Map<String, dynamic> _$$MediaMetadataImplToJson(_$MediaMetadataImpl instance) =>
    <String, dynamic>{
      'contentType': instance.contentType,
      'size': instance.size,
      'timeCreated': instance.timeCreated?.toIso8601String(),
      'updated': instance.updated?.toIso8601String(),
      'md5Hash': instance.md5Hash,
      'generation': instance.generation,
      'metageneration': instance.metageneration,
    };
