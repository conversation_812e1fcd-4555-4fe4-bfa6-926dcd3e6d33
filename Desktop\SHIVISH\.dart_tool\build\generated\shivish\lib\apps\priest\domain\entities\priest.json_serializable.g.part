// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PriestImpl _$$PriestImplFromJson(Map<String, dynamic> json) => _$PriestImpl(
  id: json['id'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  name: json['name'] as String,
  email: json['email'] as String,
  phone: json['phone'] as String,
  experienceYears: (json['experienceYears'] as num).toInt(),
  specializations: (json['specializations'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  serviceAreas: (json['serviceAreas'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  availabilitySchedule: (json['availabilitySchedule'] as Map<String, dynamic>)
      .map(
        (k, e) =>
            MapEntry(k, (e as List<dynamic>).map((e) => e as String).toList()),
      ),
  certifications: (json['certifications'] as List<dynamic>)
      .map((e) => e as Map<String, dynamic>)
      .toList(),
);

Map<String, dynamic> _$$PriestImplToJson(_$PriestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'experienceYears': instance.experienceYears,
      'specializations': instance.specializations,
      'serviceAreas': instance.serviceAreas,
      'availabilitySchedule': instance.availabilitySchedule,
      'certifications': instance.certifications,
    };
