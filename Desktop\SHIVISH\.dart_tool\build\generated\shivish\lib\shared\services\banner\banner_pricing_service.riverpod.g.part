// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bannerPricingServiceHash() =>
    r'8319346de12428b9e30675741f5749a54b4cb106';

/// See also [bannerPricingService].
@ProviderFor(bannerPricingService)
final bannerPricingServiceProvider =
    AutoDisposeProvider<BannerPricingService>.internal(
      bannerPricingService,
      name: r'bannerPricingServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bannerPricingServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BannerPricingServiceRef = AutoDisposeProviderRef<BannerPricingService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
