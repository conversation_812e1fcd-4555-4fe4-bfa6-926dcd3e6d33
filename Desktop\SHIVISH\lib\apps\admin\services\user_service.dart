import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/models/user.dart';

@injectable
class UserService {
  final FirebaseFirestore _firestore;

  UserService(this._firestore);

  CollectionReference<Map<String, dynamic>> get _usersCollection =>
      _firestore.collection('users');

  Future<List<AuthUser>> getUsers() async {
    try {
      final snapshot = await _usersCollection.get();
      return snapshot.docs
          .map((doc) => AuthUser.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to load users: $e');
    }
  }

  Future<AuthUser> addUser(AuthUser AuthUser) async {
    try {
      final docRef = await _usersCollection.add(AuthUser.toJson());
      return AuthUser.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to add AuthUser: $e');
    }
  }

  Future<AuthUser> updateUser(AuthUser AuthUser) async {
    try {
      await _usersCollection.doc(AuthUser.id).update(AuthUser.toJson());
      return AuthUser;
    } catch (e) {
      throw Exception('Failed to update AuthUser: $e');
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      await _usersCollection.doc(userId).delete();
    } catch (e) {
      throw Exception('Failed to delete AuthUser: $e');
    }
  }
}
