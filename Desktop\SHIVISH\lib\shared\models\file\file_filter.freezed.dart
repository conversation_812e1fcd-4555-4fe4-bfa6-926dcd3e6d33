// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'file_filter.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

FileFilter _$FileFilterFromJson(Map<String, dynamic> json) {
  return _FileFilter.fromJson(json);
}

/// @nodoc
mixin _$FileFilter {
  List<String> get extensions => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  bool get showFavorites => throw _privateConstructorUsedError;
  String? get searchQuery => throw _privateConstructorUsedError;
  String? get extension => throw _privateConstructorUsedError;
  String? get mimeType => throw _privateConstructorUsedError;
  int? get minSize => throw _privateConstructorUsedError;
  int? get maxSize => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  FileSort get sort => throw _privateConstructorUsedError;

  /// Serializes this FileFilter to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FileFilterCopyWith<FileFilter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FileFilterCopyWith<$Res> {
  factory $FileFilterCopyWith(
    FileFilter value,
    $Res Function(FileFilter) then,
  ) = _$FileFilterCopyWithImpl<$Res, FileFilter>;
  @useResult
  $Res call({
    List<String> extensions,
    List<String> tags,
    bool showFavorites,
    String? searchQuery,
    String? extension,
    String? mimeType,
    int? minSize,
    int? maxSize,
    DateTime? startDate,
    DateTime? endDate,
    FileSort sort,
  });
}

/// @nodoc
class _$FileFilterCopyWithImpl<$Res, $Val extends FileFilter>
    implements $FileFilterCopyWith<$Res> {
  _$FileFilterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? extensions = null,
    Object? tags = null,
    Object? showFavorites = null,
    Object? searchQuery = freezed,
    Object? extension = freezed,
    Object? mimeType = freezed,
    Object? minSize = freezed,
    Object? maxSize = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? sort = null,
  }) {
    return _then(
      _value.copyWith(
            extensions: null == extensions
                ? _value.extensions
                : extensions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            showFavorites: null == showFavorites
                ? _value.showFavorites
                : showFavorites // ignore: cast_nullable_to_non_nullable
                      as bool,
            searchQuery: freezed == searchQuery
                ? _value.searchQuery
                : searchQuery // ignore: cast_nullable_to_non_nullable
                      as String?,
            extension: freezed == extension
                ? _value.extension
                : extension // ignore: cast_nullable_to_non_nullable
                      as String?,
            mimeType: freezed == mimeType
                ? _value.mimeType
                : mimeType // ignore: cast_nullable_to_non_nullable
                      as String?,
            minSize: freezed == minSize
                ? _value.minSize
                : minSize // ignore: cast_nullable_to_non_nullable
                      as int?,
            maxSize: freezed == maxSize
                ? _value.maxSize
                : maxSize // ignore: cast_nullable_to_non_nullable
                      as int?,
            startDate: freezed == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            endDate: freezed == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            sort: null == sort
                ? _value.sort
                : sort // ignore: cast_nullable_to_non_nullable
                      as FileSort,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FileFilterImplCopyWith<$Res>
    implements $FileFilterCopyWith<$Res> {
  factory _$$FileFilterImplCopyWith(
    _$FileFilterImpl value,
    $Res Function(_$FileFilterImpl) then,
  ) = __$$FileFilterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<String> extensions,
    List<String> tags,
    bool showFavorites,
    String? searchQuery,
    String? extension,
    String? mimeType,
    int? minSize,
    int? maxSize,
    DateTime? startDate,
    DateTime? endDate,
    FileSort sort,
  });
}

/// @nodoc
class __$$FileFilterImplCopyWithImpl<$Res>
    extends _$FileFilterCopyWithImpl<$Res, _$FileFilterImpl>
    implements _$$FileFilterImplCopyWith<$Res> {
  __$$FileFilterImplCopyWithImpl(
    _$FileFilterImpl _value,
    $Res Function(_$FileFilterImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? extensions = null,
    Object? tags = null,
    Object? showFavorites = null,
    Object? searchQuery = freezed,
    Object? extension = freezed,
    Object? mimeType = freezed,
    Object? minSize = freezed,
    Object? maxSize = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? sort = null,
  }) {
    return _then(
      _$FileFilterImpl(
        extensions: null == extensions
            ? _value._extensions
            : extensions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        showFavorites: null == showFavorites
            ? _value.showFavorites
            : showFavorites // ignore: cast_nullable_to_non_nullable
                  as bool,
        searchQuery: freezed == searchQuery
            ? _value.searchQuery
            : searchQuery // ignore: cast_nullable_to_non_nullable
                  as String?,
        extension: freezed == extension
            ? _value.extension
            : extension // ignore: cast_nullable_to_non_nullable
                  as String?,
        mimeType: freezed == mimeType
            ? _value.mimeType
            : mimeType // ignore: cast_nullable_to_non_nullable
                  as String?,
        minSize: freezed == minSize
            ? _value.minSize
            : minSize // ignore: cast_nullable_to_non_nullable
                  as int?,
        maxSize: freezed == maxSize
            ? _value.maxSize
            : maxSize // ignore: cast_nullable_to_non_nullable
                  as int?,
        startDate: freezed == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        endDate: freezed == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        sort: null == sort
            ? _value.sort
            : sort // ignore: cast_nullable_to_non_nullable
                  as FileSort,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FileFilterImpl implements _FileFilter {
  const _$FileFilterImpl({
    final List<String> extensions = const [],
    final List<String> tags = const [],
    this.showFavorites = false,
    this.searchQuery,
    this.extension,
    this.mimeType,
    this.minSize,
    this.maxSize,
    this.startDate,
    this.endDate,
    this.sort = FileSort.nameAsc,
  }) : _extensions = extensions,
       _tags = tags;

  factory _$FileFilterImpl.fromJson(Map<String, dynamic> json) =>
      _$$FileFilterImplFromJson(json);

  final List<String> _extensions;
  @override
  @JsonKey()
  List<String> get extensions {
    if (_extensions is EqualUnmodifiableListView) return _extensions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extensions);
  }

  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  @JsonKey()
  final bool showFavorites;
  @override
  final String? searchQuery;
  @override
  final String? extension;
  @override
  final String? mimeType;
  @override
  final int? minSize;
  @override
  final int? maxSize;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  @JsonKey()
  final FileSort sort;

  @override
  String toString() {
    return 'FileFilter(extensions: $extensions, tags: $tags, showFavorites: $showFavorites, searchQuery: $searchQuery, extension: $extension, mimeType: $mimeType, minSize: $minSize, maxSize: $maxSize, startDate: $startDate, endDate: $endDate, sort: $sort)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileFilterImpl &&
            const DeepCollectionEquality().equals(
              other._extensions,
              _extensions,
            ) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.showFavorites, showFavorites) ||
                other.showFavorites == showFavorites) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.extension, extension) ||
                other.extension == extension) &&
            (identical(other.mimeType, mimeType) ||
                other.mimeType == mimeType) &&
            (identical(other.minSize, minSize) || other.minSize == minSize) &&
            (identical(other.maxSize, maxSize) || other.maxSize == maxSize) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.sort, sort) || other.sort == sort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_extensions),
    const DeepCollectionEquality().hash(_tags),
    showFavorites,
    searchQuery,
    extension,
    mimeType,
    minSize,
    maxSize,
    startDate,
    endDate,
    sort,
  );

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FileFilterImplCopyWith<_$FileFilterImpl> get copyWith =>
      __$$FileFilterImplCopyWithImpl<_$FileFilterImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FileFilterImplToJson(this);
  }
}

abstract class _FileFilter implements FileFilter {
  const factory _FileFilter({
    final List<String> extensions,
    final List<String> tags,
    final bool showFavorites,
    final String? searchQuery,
    final String? extension,
    final String? mimeType,
    final int? minSize,
    final int? maxSize,
    final DateTime? startDate,
    final DateTime? endDate,
    final FileSort sort,
  }) = _$FileFilterImpl;

  factory _FileFilter.fromJson(Map<String, dynamic> json) =
      _$FileFilterImpl.fromJson;

  @override
  List<String> get extensions;
  @override
  List<String> get tags;
  @override
  bool get showFavorites;
  @override
  String? get searchQuery;
  @override
  String? get extension;
  @override
  String? get mimeType;
  @override
  int? get minSize;
  @override
  int? get maxSize;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  FileSort get sort;

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FileFilterImplCopyWith<_$FileFilterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
