// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'file_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

FileModel _$FileModelFromJson(Map<String, dynamic> json) {
  return _FileModel.fromJson(json);
}

/// @nodoc
mixin _$FileModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get path => throw _privateConstructorUsedError;
  String get extension => throw _privateConstructorUsedError;
  String get mimeType => throw _privateConstructorUsedError;
  int get size => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  bool get isFavorite => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  String? get thumbnailUrl => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this FileModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FileModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FileModelCopyWith<FileModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FileModelCopyWith<$Res> {
  factory $FileModelCopyWith(FileModel value, $Res Function(FileModel) then) =
      _$FileModelCopyWithImpl<$Res, FileModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String path,
    String extension,
    String mimeType,
    int size,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    bool isFavorite,
    List<String>? tags,
    String? thumbnailUrl,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$FileModelCopyWithImpl<$Res, $Val extends FileModel>
    implements $FileModelCopyWith<$Res> {
  _$FileModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FileModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? path = null,
    Object? extension = null,
    Object? mimeType = null,
    Object? size = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? isFavorite = null,
    Object? tags = freezed,
    Object? thumbnailUrl = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            path: null == path
                ? _value.path
                : path // ignore: cast_nullable_to_non_nullable
                      as String,
            extension: null == extension
                ? _value.extension
                : extension // ignore: cast_nullable_to_non_nullable
                      as String,
            mimeType: null == mimeType
                ? _value.mimeType
                : mimeType // ignore: cast_nullable_to_non_nullable
                      as String,
            size: null == size
                ? _value.size
                : size // ignore: cast_nullable_to_non_nullable
                      as int,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isFavorite: null == isFavorite
                ? _value.isFavorite
                : isFavorite // ignore: cast_nullable_to_non_nullable
                      as bool,
            tags: freezed == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            thumbnailUrl: freezed == thumbnailUrl
                ? _value.thumbnailUrl
                : thumbnailUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FileModelImplCopyWith<$Res>
    implements $FileModelCopyWith<$Res> {
  factory _$$FileModelImplCopyWith(
    _$FileModelImpl value,
    $Res Function(_$FileModelImpl) then,
  ) = __$$FileModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String path,
    String extension,
    String mimeType,
    int size,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    bool isFavorite,
    List<String>? tags,
    String? thumbnailUrl,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$FileModelImplCopyWithImpl<$Res>
    extends _$FileModelCopyWithImpl<$Res, _$FileModelImpl>
    implements _$$FileModelImplCopyWith<$Res> {
  __$$FileModelImplCopyWithImpl(
    _$FileModelImpl _value,
    $Res Function(_$FileModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FileModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? path = null,
    Object? extension = null,
    Object? mimeType = null,
    Object? size = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? isFavorite = null,
    Object? tags = freezed,
    Object? thumbnailUrl = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$FileModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        path: null == path
            ? _value.path
            : path // ignore: cast_nullable_to_non_nullable
                  as String,
        extension: null == extension
            ? _value.extension
            : extension // ignore: cast_nullable_to_non_nullable
                  as String,
        mimeType: null == mimeType
            ? _value.mimeType
            : mimeType // ignore: cast_nullable_to_non_nullable
                  as String,
        size: null == size
            ? _value.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isFavorite: null == isFavorite
            ? _value.isFavorite
            : isFavorite // ignore: cast_nullable_to_non_nullable
                  as bool,
        tags: freezed == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        thumbnailUrl: freezed == thumbnailUrl
            ? _value.thumbnailUrl
            : thumbnailUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FileModelImpl implements _FileModel {
  const _$FileModelImpl({
    required this.id,
    required this.name,
    required this.path,
    required this.extension,
    required this.mimeType,
    required this.size,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    this.isFavorite = false,
    final List<String>? tags,
    this.thumbnailUrl,
    final Map<String, dynamic>? metadata,
  }) : _tags = tags,
       _metadata = metadata;

  factory _$FileModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$FileModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String path;
  @override
  final String extension;
  @override
  final String mimeType;
  @override
  final int size;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  @JsonKey()
  final bool isFavorite;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? thumbnailUrl;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'FileModel(id: $id, name: $name, path: $path, extension: $extension, mimeType: $mimeType, size: $size, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, isFavorite: $isFavorite, tags: $tags, thumbnailUrl: $thumbnailUrl, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.path, path) || other.path == path) &&
            (identical(other.extension, extension) ||
                other.extension == extension) &&
            (identical(other.mimeType, mimeType) ||
                other.mimeType == mimeType) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.isFavorite, isFavorite) ||
                other.isFavorite == isFavorite) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    path,
    extension,
    mimeType,
    size,
    createdAt,
    updatedAt,
    isDeleted,
    isFavorite,
    const DeepCollectionEquality().hash(_tags),
    thumbnailUrl,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of FileModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FileModelImplCopyWith<_$FileModelImpl> get copyWith =>
      __$$FileModelImplCopyWithImpl<_$FileModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FileModelImplToJson(this);
  }
}

abstract class _FileModel implements FileModel {
  const factory _FileModel({
    required final String id,
    required final String name,
    required final String path,
    required final String extension,
    required final String mimeType,
    required final int size,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    final bool isFavorite,
    final List<String>? tags,
    final String? thumbnailUrl,
    final Map<String, dynamic>? metadata,
  }) = _$FileModelImpl;

  factory _FileModel.fromJson(Map<String, dynamic> json) =
      _$FileModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get path;
  @override
  String get extension;
  @override
  String get mimeType;
  @override
  int get size;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  bool get isFavorite;
  @override
  List<String>? get tags;
  @override
  String? get thumbnailUrl;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of FileModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FileModelImplCopyWith<_$FileModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FileFilter _$FileFilterFromJson(Map<String, dynamic> json) {
  return _FileFilter.fromJson(json);
}

/// @nodoc
mixin _$FileFilter {
  List<String> get extensions => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  bool get showFavorites => throw _privateConstructorUsedError;
  bool get showDeleted => throw _privateConstructorUsedError;
  FileSort get sortBy => throw _privateConstructorUsedError;
  String? get searchQuery => throw _privateConstructorUsedError;

  /// Serializes this FileFilter to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FileFilterCopyWith<FileFilter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FileFilterCopyWith<$Res> {
  factory $FileFilterCopyWith(
    FileFilter value,
    $Res Function(FileFilter) then,
  ) = _$FileFilterCopyWithImpl<$Res, FileFilter>;
  @useResult
  $Res call({
    List<String> extensions,
    List<String> tags,
    bool showFavorites,
    bool showDeleted,
    FileSort sortBy,
    String? searchQuery,
  });
}

/// @nodoc
class _$FileFilterCopyWithImpl<$Res, $Val extends FileFilter>
    implements $FileFilterCopyWith<$Res> {
  _$FileFilterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? extensions = null,
    Object? tags = null,
    Object? showFavorites = null,
    Object? showDeleted = null,
    Object? sortBy = null,
    Object? searchQuery = freezed,
  }) {
    return _then(
      _value.copyWith(
            extensions: null == extensions
                ? _value.extensions
                : extensions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            showFavorites: null == showFavorites
                ? _value.showFavorites
                : showFavorites // ignore: cast_nullable_to_non_nullable
                      as bool,
            showDeleted: null == showDeleted
                ? _value.showDeleted
                : showDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            sortBy: null == sortBy
                ? _value.sortBy
                : sortBy // ignore: cast_nullable_to_non_nullable
                      as FileSort,
            searchQuery: freezed == searchQuery
                ? _value.searchQuery
                : searchQuery // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FileFilterImplCopyWith<$Res>
    implements $FileFilterCopyWith<$Res> {
  factory _$$FileFilterImplCopyWith(
    _$FileFilterImpl value,
    $Res Function(_$FileFilterImpl) then,
  ) = __$$FileFilterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<String> extensions,
    List<String> tags,
    bool showFavorites,
    bool showDeleted,
    FileSort sortBy,
    String? searchQuery,
  });
}

/// @nodoc
class __$$FileFilterImplCopyWithImpl<$Res>
    extends _$FileFilterCopyWithImpl<$Res, _$FileFilterImpl>
    implements _$$FileFilterImplCopyWith<$Res> {
  __$$FileFilterImplCopyWithImpl(
    _$FileFilterImpl _value,
    $Res Function(_$FileFilterImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? extensions = null,
    Object? tags = null,
    Object? showFavorites = null,
    Object? showDeleted = null,
    Object? sortBy = null,
    Object? searchQuery = freezed,
  }) {
    return _then(
      _$FileFilterImpl(
        extensions: null == extensions
            ? _value._extensions
            : extensions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        showFavorites: null == showFavorites
            ? _value.showFavorites
            : showFavorites // ignore: cast_nullable_to_non_nullable
                  as bool,
        showDeleted: null == showDeleted
            ? _value.showDeleted
            : showDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        sortBy: null == sortBy
            ? _value.sortBy
            : sortBy // ignore: cast_nullable_to_non_nullable
                  as FileSort,
        searchQuery: freezed == searchQuery
            ? _value.searchQuery
            : searchQuery // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FileFilterImpl implements _FileFilter {
  const _$FileFilterImpl({
    final List<String> extensions = const [],
    final List<String> tags = const [],
    this.showFavorites = false,
    this.showDeleted = false,
    this.sortBy = FileSort.nameAsc,
    this.searchQuery,
  }) : _extensions = extensions,
       _tags = tags;

  factory _$FileFilterImpl.fromJson(Map<String, dynamic> json) =>
      _$$FileFilterImplFromJson(json);

  final List<String> _extensions;
  @override
  @JsonKey()
  List<String> get extensions {
    if (_extensions is EqualUnmodifiableListView) return _extensions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extensions);
  }

  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  @JsonKey()
  final bool showFavorites;
  @override
  @JsonKey()
  final bool showDeleted;
  @override
  @JsonKey()
  final FileSort sortBy;
  @override
  final String? searchQuery;

  @override
  String toString() {
    return 'FileFilter(extensions: $extensions, tags: $tags, showFavorites: $showFavorites, showDeleted: $showDeleted, sortBy: $sortBy, searchQuery: $searchQuery)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileFilterImpl &&
            const DeepCollectionEquality().equals(
              other._extensions,
              _extensions,
            ) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.showFavorites, showFavorites) ||
                other.showFavorites == showFavorites) &&
            (identical(other.showDeleted, showDeleted) ||
                other.showDeleted == showDeleted) &&
            (identical(other.sortBy, sortBy) || other.sortBy == sortBy) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_extensions),
    const DeepCollectionEquality().hash(_tags),
    showFavorites,
    showDeleted,
    sortBy,
    searchQuery,
  );

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FileFilterImplCopyWith<_$FileFilterImpl> get copyWith =>
      __$$FileFilterImplCopyWithImpl<_$FileFilterImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FileFilterImplToJson(this);
  }
}

abstract class _FileFilter implements FileFilter {
  const factory _FileFilter({
    final List<String> extensions,
    final List<String> tags,
    final bool showFavorites,
    final bool showDeleted,
    final FileSort sortBy,
    final String? searchQuery,
  }) = _$FileFilterImpl;

  factory _FileFilter.fromJson(Map<String, dynamic> json) =
      _$FileFilterImpl.fromJson;

  @override
  List<String> get extensions;
  @override
  List<String> get tags;
  @override
  bool get showFavorites;
  @override
  bool get showDeleted;
  @override
  FileSort get sortBy;
  @override
  String? get searchQuery;

  /// Create a copy of FileFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FileFilterImplCopyWith<_$FileFilterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
