[{"type": {"import": "package:shivish/shared/services/seller_service.dart", "name": "SellerService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/shared/services/seller_service.dart", "name": "SellerService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_firestore", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:shared_preferences/shared_preferences.dart", "name": "SharedPreferences", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:shared_preferences/src/shared_preferences_legacy.dart"]}, "instanceName": null, "paramName": "_prefs", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]