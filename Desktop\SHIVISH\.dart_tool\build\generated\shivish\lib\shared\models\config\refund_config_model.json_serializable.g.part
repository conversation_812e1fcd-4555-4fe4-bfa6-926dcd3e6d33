// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RefundConfigModelImpl _$$RefundConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$RefundConfigModelImpl(
  maxRefundDays: (json['maxRefundDays'] as num).toInt(),
  maxRefundPercentage: (json['maxRefundPercentage'] as num).toDouble(),
  requireReason: json['requireReason'] as bool,
  requireApproval: json['requireApproval'] as bool,
  validReasons: (json['validReasons'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$$RefundConfigModelImplToJson(
  _$RefundConfigModelImpl instance,
) => <String, dynamic>{
  'maxRefundDays': instance.maxRefundDays,
  'maxRefundPercentage': instance.maxRefundPercentage,
  'requireReason': instance.requireReason,
  'requireApproval': instance.requireApproval,
  'validReasons': instance.validReasons,
};
