// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoiceCommandConfigModelImpl _$$VoiceCommandConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$VoiceCommandConfigModelImpl(
  enabled: json['enabled'] as bool,
  wakeWord: json['wakeWord'] as String,
  language: json['language'] as String,
  sensitivity: (json['sensitivity'] as num).toDouble(),
  supportedCommands: (json['supportedCommands'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  commandMappings: Map<String, String>.from(json['commandMappings'] as Map),
);

Map<String, dynamic> _$$VoiceCommandConfigModelImplToJson(
  _$VoiceCommandConfigModelImpl instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'wakeWord': instance.wakeWord,
  'language': instance.language,
  'sensitivity': instance.sensitivity,
  'supportedCommands': instance.supportedCommands,
  'commandMappings': instance.commandMappings,
};
