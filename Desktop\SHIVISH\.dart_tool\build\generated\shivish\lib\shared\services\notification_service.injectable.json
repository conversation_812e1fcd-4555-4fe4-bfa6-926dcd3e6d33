[{"type": {"import": "package:shivish/shared/services/notification_service.dart", "name": "NotificationService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/shared/services/notification_service.dart", "name": "NotificationService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:firebase_messaging/firebase_messaging.dart", "name": "FirebaseMessaging", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_messaging", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:flutter_local_notifications/flutter_local_notifications.dart", "name": "FlutterLocalNotificationsPlugin", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:flutter_local_notifications/src/flutter_local_notifications_plugin.dart"]}, "instanceName": null, "paramName": "_localNotifications", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_firestore", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:go_router/go_router.dart", "name": "GoRouter", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:go_router/src/router.dart"]}, "instanceName": null, "paramName": "_router", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]