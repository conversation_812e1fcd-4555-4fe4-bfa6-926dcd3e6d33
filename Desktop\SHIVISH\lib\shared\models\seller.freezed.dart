// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Seller _$SellerFromJson(Map<String, dynamic> json) {
  return _Seller.fromJson(json);
}

/// @nodoc
mixin _$Seller {
  String get id => throw _privateConstructorUsedError;
  String get businessName => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get profileImage => throw _privateConstructorUsedError;
  String? get businessLogo => throw _privateConstructorUsedError;
  String? get businessDescription => throw _privateConstructorUsedError;
  List<String> get businessAddress => throw _privateConstructorUsedError;
  List<String> get businessDocuments => throw _privateConstructorUsedError;
  List<String> get products => throw _privateConstructorUsedError;
  List<String> get orders => throw _privateConstructorUsedError;
  SellerCategory get category => throw _privateConstructorUsedError;
  bool get isEmailVerified => throw _privateConstructorUsedError;
  bool get isPhoneVerified => throw _privateConstructorUsedError;
  bool get isProfileComplete => throw _privateConstructorUsedError;
  bool get isApproved => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isSuspended => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DateTime? get lastLoginAt => throw _privateConstructorUsedError;
  DateTime? get approvedAt => throw _privateConstructorUsedError;
  String? get approvedBy => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get totalReviews => throw _privateConstructorUsedError;
  int get totalOrders => throw _privateConstructorUsedError;
  int get totalProducts => throw _privateConstructorUsedError;
  double get totalRevenue => throw _privateConstructorUsedError;
  Map<String, dynamic> get businessHours => throw _privateConstructorUsedError;
  Map<String, dynamic> get paymentSettings =>
      throw _privateConstructorUsedError;
  Map<String, dynamic> get shippingSettings =>
      throw _privateConstructorUsedError;
  Map<String, dynamic> get notificationSettings =>
      throw _privateConstructorUsedError;

  /// Serializes this Seller to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SellerCopyWith<Seller> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SellerCopyWith<$Res> {
  factory $SellerCopyWith(Seller value, $Res Function(Seller) then) =
      _$SellerCopyWithImpl<$Res, Seller>;
  @useResult
  $Res call({
    String id,
    String businessName,
    String email,
    String? phoneNumber,
    String? profileImage,
    String? businessLogo,
    String? businessDescription,
    List<String> businessAddress,
    List<String> businessDocuments,
    List<String> products,
    List<String> orders,
    SellerCategory category,
    bool isEmailVerified,
    bool isPhoneVerified,
    bool isProfileComplete,
    bool isApproved,
    bool isActive,
    bool isSuspended,
    bool isDeleted,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    DateTime? approvedAt,
    String? approvedBy,
    String? rejectionReason,
    double rating,
    int totalReviews,
    int totalOrders,
    int totalProducts,
    double totalRevenue,
    Map<String, dynamic> businessHours,
    Map<String, dynamic> paymentSettings,
    Map<String, dynamic> shippingSettings,
    Map<String, dynamic> notificationSettings,
  });
}

/// @nodoc
class _$SellerCopyWithImpl<$Res, $Val extends Seller>
    implements $SellerCopyWith<$Res> {
  _$SellerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessName = null,
    Object? email = null,
    Object? phoneNumber = freezed,
    Object? profileImage = freezed,
    Object? businessLogo = freezed,
    Object? businessDescription = freezed,
    Object? businessAddress = null,
    Object? businessDocuments = null,
    Object? products = null,
    Object? orders = null,
    Object? category = null,
    Object? isEmailVerified = null,
    Object? isPhoneVerified = null,
    Object? isProfileComplete = null,
    Object? isApproved = null,
    Object? isActive = null,
    Object? isSuspended = null,
    Object? isDeleted = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? lastLoginAt = freezed,
    Object? approvedAt = freezed,
    Object? approvedBy = freezed,
    Object? rejectionReason = freezed,
    Object? rating = null,
    Object? totalReviews = null,
    Object? totalOrders = null,
    Object? totalProducts = null,
    Object? totalRevenue = null,
    Object? businessHours = null,
    Object? paymentSettings = null,
    Object? shippingSettings = null,
    Object? notificationSettings = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            businessName: null == businessName
                ? _value.businessName
                : businessName // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            phoneNumber: freezed == phoneNumber
                ? _value.phoneNumber
                : phoneNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            profileImage: freezed == profileImage
                ? _value.profileImage
                : profileImage // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessLogo: freezed == businessLogo
                ? _value.businessLogo
                : businessLogo // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessDescription: freezed == businessDescription
                ? _value.businessDescription
                : businessDescription // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessAddress: null == businessAddress
                ? _value.businessAddress
                : businessAddress // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            businessDocuments: null == businessDocuments
                ? _value.businessDocuments
                : businessDocuments // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            products: null == products
                ? _value.products
                : products // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            orders: null == orders
                ? _value.orders
                : orders // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            category: null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                      as SellerCategory,
            isEmailVerified: null == isEmailVerified
                ? _value.isEmailVerified
                : isEmailVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isPhoneVerified: null == isPhoneVerified
                ? _value.isPhoneVerified
                : isPhoneVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isProfileComplete: null == isProfileComplete
                ? _value.isProfileComplete
                : isProfileComplete // ignore: cast_nullable_to_non_nullable
                      as bool,
            isApproved: null == isApproved
                ? _value.isApproved
                : isApproved // ignore: cast_nullable_to_non_nullable
                      as bool,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            isSuspended: null == isSuspended
                ? _value.isSuspended
                : isSuspended // ignore: cast_nullable_to_non_nullable
                      as bool,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            lastLoginAt: freezed == lastLoginAt
                ? _value.lastLoginAt
                : lastLoginAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            approvedAt: freezed == approvedAt
                ? _value.approvedAt
                : approvedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            approvedBy: freezed == approvedBy
                ? _value.approvedBy
                : approvedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            totalReviews: null == totalReviews
                ? _value.totalReviews
                : totalReviews // ignore: cast_nullable_to_non_nullable
                      as int,
            totalOrders: null == totalOrders
                ? _value.totalOrders
                : totalOrders // ignore: cast_nullable_to_non_nullable
                      as int,
            totalProducts: null == totalProducts
                ? _value.totalProducts
                : totalProducts // ignore: cast_nullable_to_non_nullable
                      as int,
            totalRevenue: null == totalRevenue
                ? _value.totalRevenue
                : totalRevenue // ignore: cast_nullable_to_non_nullable
                      as double,
            businessHours: null == businessHours
                ? _value.businessHours
                : businessHours // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            paymentSettings: null == paymentSettings
                ? _value.paymentSettings
                : paymentSettings // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            shippingSettings: null == shippingSettings
                ? _value.shippingSettings
                : shippingSettings // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            notificationSettings: null == notificationSettings
                ? _value.notificationSettings
                : notificationSettings // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SellerImplCopyWith<$Res> implements $SellerCopyWith<$Res> {
  factory _$$SellerImplCopyWith(
    _$SellerImpl value,
    $Res Function(_$SellerImpl) then,
  ) = __$$SellerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String businessName,
    String email,
    String? phoneNumber,
    String? profileImage,
    String? businessLogo,
    String? businessDescription,
    List<String> businessAddress,
    List<String> businessDocuments,
    List<String> products,
    List<String> orders,
    SellerCategory category,
    bool isEmailVerified,
    bool isPhoneVerified,
    bool isProfileComplete,
    bool isApproved,
    bool isActive,
    bool isSuspended,
    bool isDeleted,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    DateTime? approvedAt,
    String? approvedBy,
    String? rejectionReason,
    double rating,
    int totalReviews,
    int totalOrders,
    int totalProducts,
    double totalRevenue,
    Map<String, dynamic> businessHours,
    Map<String, dynamic> paymentSettings,
    Map<String, dynamic> shippingSettings,
    Map<String, dynamic> notificationSettings,
  });
}

/// @nodoc
class __$$SellerImplCopyWithImpl<$Res>
    extends _$SellerCopyWithImpl<$Res, _$SellerImpl>
    implements _$$SellerImplCopyWith<$Res> {
  __$$SellerImplCopyWithImpl(
    _$SellerImpl _value,
    $Res Function(_$SellerImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessName = null,
    Object? email = null,
    Object? phoneNumber = freezed,
    Object? profileImage = freezed,
    Object? businessLogo = freezed,
    Object? businessDescription = freezed,
    Object? businessAddress = null,
    Object? businessDocuments = null,
    Object? products = null,
    Object? orders = null,
    Object? category = null,
    Object? isEmailVerified = null,
    Object? isPhoneVerified = null,
    Object? isProfileComplete = null,
    Object? isApproved = null,
    Object? isActive = null,
    Object? isSuspended = null,
    Object? isDeleted = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? lastLoginAt = freezed,
    Object? approvedAt = freezed,
    Object? approvedBy = freezed,
    Object? rejectionReason = freezed,
    Object? rating = null,
    Object? totalReviews = null,
    Object? totalOrders = null,
    Object? totalProducts = null,
    Object? totalRevenue = null,
    Object? businessHours = null,
    Object? paymentSettings = null,
    Object? shippingSettings = null,
    Object? notificationSettings = null,
  }) {
    return _then(
      _$SellerImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        businessName: null == businessName
            ? _value.businessName
            : businessName // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        phoneNumber: freezed == phoneNumber
            ? _value.phoneNumber
            : phoneNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        profileImage: freezed == profileImage
            ? _value.profileImage
            : profileImage // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessLogo: freezed == businessLogo
            ? _value.businessLogo
            : businessLogo // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessDescription: freezed == businessDescription
            ? _value.businessDescription
            : businessDescription // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessAddress: null == businessAddress
            ? _value._businessAddress
            : businessAddress // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        businessDocuments: null == businessDocuments
            ? _value._businessDocuments
            : businessDocuments // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        products: null == products
            ? _value._products
            : products // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        orders: null == orders
            ? _value._orders
            : orders // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        category: null == category
            ? _value.category
            : category // ignore: cast_nullable_to_non_nullable
                  as SellerCategory,
        isEmailVerified: null == isEmailVerified
            ? _value.isEmailVerified
            : isEmailVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isPhoneVerified: null == isPhoneVerified
            ? _value.isPhoneVerified
            : isPhoneVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isProfileComplete: null == isProfileComplete
            ? _value.isProfileComplete
            : isProfileComplete // ignore: cast_nullable_to_non_nullable
                  as bool,
        isApproved: null == isApproved
            ? _value.isApproved
            : isApproved // ignore: cast_nullable_to_non_nullable
                  as bool,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        isSuspended: null == isSuspended
            ? _value.isSuspended
            : isSuspended // ignore: cast_nullable_to_non_nullable
                  as bool,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        lastLoginAt: freezed == lastLoginAt
            ? _value.lastLoginAt
            : lastLoginAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        approvedAt: freezed == approvedAt
            ? _value.approvedAt
            : approvedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        approvedBy: freezed == approvedBy
            ? _value.approvedBy
            : approvedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        totalReviews: null == totalReviews
            ? _value.totalReviews
            : totalReviews // ignore: cast_nullable_to_non_nullable
                  as int,
        totalOrders: null == totalOrders
            ? _value.totalOrders
            : totalOrders // ignore: cast_nullable_to_non_nullable
                  as int,
        totalProducts: null == totalProducts
            ? _value.totalProducts
            : totalProducts // ignore: cast_nullable_to_non_nullable
                  as int,
        totalRevenue: null == totalRevenue
            ? _value.totalRevenue
            : totalRevenue // ignore: cast_nullable_to_non_nullable
                  as double,
        businessHours: null == businessHours
            ? _value._businessHours
            : businessHours // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        paymentSettings: null == paymentSettings
            ? _value._paymentSettings
            : paymentSettings // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        shippingSettings: null == shippingSettings
            ? _value._shippingSettings
            : shippingSettings // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        notificationSettings: null == notificationSettings
            ? _value._notificationSettings
            : notificationSettings // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SellerImpl implements _Seller {
  const _$SellerImpl({
    required this.id,
    required this.businessName,
    required this.email,
    this.phoneNumber,
    this.profileImage,
    this.businessLogo,
    this.businessDescription,
    final List<String> businessAddress = const [],
    final List<String> businessDocuments = const [],
    final List<String> products = const [],
    final List<String> orders = const [],
    this.category = SellerCategory.other,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.isProfileComplete = false,
    this.isApproved = false,
    this.isActive = false,
    this.isSuspended = false,
    this.isDeleted = false,
    this.createdAt,
    this.updatedAt,
    this.lastLoginAt,
    this.approvedAt,
    this.approvedBy,
    this.rejectionReason,
    this.rating = 0.0,
    this.totalReviews = 0,
    this.totalOrders = 0,
    this.totalProducts = 0,
    this.totalRevenue = 0.0,
    final Map<String, dynamic> businessHours = const {},
    final Map<String, dynamic> paymentSettings = const {},
    final Map<String, dynamic> shippingSettings = const {},
    final Map<String, dynamic> notificationSettings = const {},
  }) : _businessAddress = businessAddress,
       _businessDocuments = businessDocuments,
       _products = products,
       _orders = orders,
       _businessHours = businessHours,
       _paymentSettings = paymentSettings,
       _shippingSettings = shippingSettings,
       _notificationSettings = notificationSettings;

  factory _$SellerImpl.fromJson(Map<String, dynamic> json) =>
      _$$SellerImplFromJson(json);

  @override
  final String id;
  @override
  final String businessName;
  @override
  final String email;
  @override
  final String? phoneNumber;
  @override
  final String? profileImage;
  @override
  final String? businessLogo;
  @override
  final String? businessDescription;
  final List<String> _businessAddress;
  @override
  @JsonKey()
  List<String> get businessAddress {
    if (_businessAddress is EqualUnmodifiableListView) return _businessAddress;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_businessAddress);
  }

  final List<String> _businessDocuments;
  @override
  @JsonKey()
  List<String> get businessDocuments {
    if (_businessDocuments is EqualUnmodifiableListView)
      return _businessDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_businessDocuments);
  }

  final List<String> _products;
  @override
  @JsonKey()
  List<String> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  final List<String> _orders;
  @override
  @JsonKey()
  List<String> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @override
  @JsonKey()
  final SellerCategory category;
  @override
  @JsonKey()
  final bool isEmailVerified;
  @override
  @JsonKey()
  final bool isPhoneVerified;
  @override
  @JsonKey()
  final bool isProfileComplete;
  @override
  @JsonKey()
  final bool isApproved;
  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final bool isSuspended;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final DateTime? lastLoginAt;
  @override
  final DateTime? approvedAt;
  @override
  final String? approvedBy;
  @override
  final String? rejectionReason;
  @override
  @JsonKey()
  final double rating;
  @override
  @JsonKey()
  final int totalReviews;
  @override
  @JsonKey()
  final int totalOrders;
  @override
  @JsonKey()
  final int totalProducts;
  @override
  @JsonKey()
  final double totalRevenue;
  final Map<String, dynamic> _businessHours;
  @override
  @JsonKey()
  Map<String, dynamic> get businessHours {
    if (_businessHours is EqualUnmodifiableMapView) return _businessHours;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_businessHours);
  }

  final Map<String, dynamic> _paymentSettings;
  @override
  @JsonKey()
  Map<String, dynamic> get paymentSettings {
    if (_paymentSettings is EqualUnmodifiableMapView) return _paymentSettings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_paymentSettings);
  }

  final Map<String, dynamic> _shippingSettings;
  @override
  @JsonKey()
  Map<String, dynamic> get shippingSettings {
    if (_shippingSettings is EqualUnmodifiableMapView) return _shippingSettings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_shippingSettings);
  }

  final Map<String, dynamic> _notificationSettings;
  @override
  @JsonKey()
  Map<String, dynamic> get notificationSettings {
    if (_notificationSettings is EqualUnmodifiableMapView)
      return _notificationSettings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_notificationSettings);
  }

  @override
  String toString() {
    return 'Seller(id: $id, businessName: $businessName, email: $email, phoneNumber: $phoneNumber, profileImage: $profileImage, businessLogo: $businessLogo, businessDescription: $businessDescription, businessAddress: $businessAddress, businessDocuments: $businessDocuments, products: $products, orders: $orders, category: $category, isEmailVerified: $isEmailVerified, isPhoneVerified: $isPhoneVerified, isProfileComplete: $isProfileComplete, isApproved: $isApproved, isActive: $isActive, isSuspended: $isSuspended, isDeleted: $isDeleted, createdAt: $createdAt, updatedAt: $updatedAt, lastLoginAt: $lastLoginAt, approvedAt: $approvedAt, approvedBy: $approvedBy, rejectionReason: $rejectionReason, rating: $rating, totalReviews: $totalReviews, totalOrders: $totalOrders, totalProducts: $totalProducts, totalRevenue: $totalRevenue, businessHours: $businessHours, paymentSettings: $paymentSettings, shippingSettings: $shippingSettings, notificationSettings: $notificationSettings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SellerImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.businessName, businessName) ||
                other.businessName == businessName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.businessLogo, businessLogo) ||
                other.businessLogo == businessLogo) &&
            (identical(other.businessDescription, businessDescription) ||
                other.businessDescription == businessDescription) &&
            const DeepCollectionEquality().equals(
              other._businessAddress,
              _businessAddress,
            ) &&
            const DeepCollectionEquality().equals(
              other._businessDocuments,
              _businessDocuments,
            ) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            const DeepCollectionEquality().equals(other._orders, _orders) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.isPhoneVerified, isPhoneVerified) ||
                other.isPhoneVerified == isPhoneVerified) &&
            (identical(other.isProfileComplete, isProfileComplete) ||
                other.isProfileComplete == isProfileComplete) &&
            (identical(other.isApproved, isApproved) ||
                other.isApproved == isApproved) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isSuspended, isSuspended) ||
                other.isSuspended == isSuspended) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.lastLoginAt, lastLoginAt) ||
                other.lastLoginAt == lastLoginAt) &&
            (identical(other.approvedAt, approvedAt) ||
                other.approvedAt == approvedAt) &&
            (identical(other.approvedBy, approvedBy) ||
                other.approvedBy == approvedBy) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalReviews, totalReviews) ||
                other.totalReviews == totalReviews) &&
            (identical(other.totalOrders, totalOrders) ||
                other.totalOrders == totalOrders) &&
            (identical(other.totalProducts, totalProducts) ||
                other.totalProducts == totalProducts) &&
            (identical(other.totalRevenue, totalRevenue) ||
                other.totalRevenue == totalRevenue) &&
            const DeepCollectionEquality().equals(
              other._businessHours,
              _businessHours,
            ) &&
            const DeepCollectionEquality().equals(
              other._paymentSettings,
              _paymentSettings,
            ) &&
            const DeepCollectionEquality().equals(
              other._shippingSettings,
              _shippingSettings,
            ) &&
            const DeepCollectionEquality().equals(
              other._notificationSettings,
              _notificationSettings,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    businessName,
    email,
    phoneNumber,
    profileImage,
    businessLogo,
    businessDescription,
    const DeepCollectionEquality().hash(_businessAddress),
    const DeepCollectionEquality().hash(_businessDocuments),
    const DeepCollectionEquality().hash(_products),
    const DeepCollectionEquality().hash(_orders),
    category,
    isEmailVerified,
    isPhoneVerified,
    isProfileComplete,
    isApproved,
    isActive,
    isSuspended,
    isDeleted,
    createdAt,
    updatedAt,
    lastLoginAt,
    approvedAt,
    approvedBy,
    rejectionReason,
    rating,
    totalReviews,
    totalOrders,
    totalProducts,
    totalRevenue,
    const DeepCollectionEquality().hash(_businessHours),
    const DeepCollectionEquality().hash(_paymentSettings),
    const DeepCollectionEquality().hash(_shippingSettings),
    const DeepCollectionEquality().hash(_notificationSettings),
  ]);

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SellerImplCopyWith<_$SellerImpl> get copyWith =>
      __$$SellerImplCopyWithImpl<_$SellerImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SellerImplToJson(this);
  }
}

abstract class _Seller implements Seller {
  const factory _Seller({
    required final String id,
    required final String businessName,
    required final String email,
    final String? phoneNumber,
    final String? profileImage,
    final String? businessLogo,
    final String? businessDescription,
    final List<String> businessAddress,
    final List<String> businessDocuments,
    final List<String> products,
    final List<String> orders,
    final SellerCategory category,
    final bool isEmailVerified,
    final bool isPhoneVerified,
    final bool isProfileComplete,
    final bool isApproved,
    final bool isActive,
    final bool isSuspended,
    final bool isDeleted,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    final DateTime? lastLoginAt,
    final DateTime? approvedAt,
    final String? approvedBy,
    final String? rejectionReason,
    final double rating,
    final int totalReviews,
    final int totalOrders,
    final int totalProducts,
    final double totalRevenue,
    final Map<String, dynamic> businessHours,
    final Map<String, dynamic> paymentSettings,
    final Map<String, dynamic> shippingSettings,
    final Map<String, dynamic> notificationSettings,
  }) = _$SellerImpl;

  factory _Seller.fromJson(Map<String, dynamic> json) = _$SellerImpl.fromJson;

  @override
  String get id;
  @override
  String get businessName;
  @override
  String get email;
  @override
  String? get phoneNumber;
  @override
  String? get profileImage;
  @override
  String? get businessLogo;
  @override
  String? get businessDescription;
  @override
  List<String> get businessAddress;
  @override
  List<String> get businessDocuments;
  @override
  List<String> get products;
  @override
  List<String> get orders;
  @override
  SellerCategory get category;
  @override
  bool get isEmailVerified;
  @override
  bool get isPhoneVerified;
  @override
  bool get isProfileComplete;
  @override
  bool get isApproved;
  @override
  bool get isActive;
  @override
  bool get isSuspended;
  @override
  bool get isDeleted;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  DateTime? get lastLoginAt;
  @override
  DateTime? get approvedAt;
  @override
  String? get approvedBy;
  @override
  String? get rejectionReason;
  @override
  double get rating;
  @override
  int get totalReviews;
  @override
  int get totalOrders;
  @override
  int get totalProducts;
  @override
  double get totalRevenue;
  @override
  Map<String, dynamic> get businessHours;
  @override
  Map<String, dynamic> get paymentSettings;
  @override
  Map<String, dynamic> get shippingSettings;
  @override
  Map<String, dynamic> get notificationSettings;

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SellerImplCopyWith<_$SellerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
