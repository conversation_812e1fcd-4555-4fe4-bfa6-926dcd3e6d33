// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'whatsapp_contact_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

MessageTemplate _$MessageTemplateFromJson(Map<String, dynamic> json) {
  return _MessageTemplate.fromJson(json);
}

/// @nodoc
mixin _$MessageTemplate {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  MessageTemplateType get type => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this MessageTemplate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MessageTemplate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MessageTemplateCopyWith<MessageTemplate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageTemplateCopyWith<$Res> {
  factory $MessageTemplateCopyWith(
    MessageTemplate value,
    $Res Function(MessageTemplate) then,
  ) = _$MessageTemplateCopyWithImpl<$Res, MessageTemplate>;
  @useResult
  $Res call({
    String id,
    String name,
    String content,
    MessageTemplateType type,
    String? description,
    bool isActive,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$MessageTemplateCopyWithImpl<$Res, $Val extends MessageTemplate>
    implements $MessageTemplateCopyWith<$Res> {
  _$MessageTemplateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MessageTemplate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? content = null,
    Object? type = null,
    Object? description = freezed,
    Object? isActive = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as MessageTemplateType,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MessageTemplateImplCopyWith<$Res>
    implements $MessageTemplateCopyWith<$Res> {
  factory _$$MessageTemplateImplCopyWith(
    _$MessageTemplateImpl value,
    $Res Function(_$MessageTemplateImpl) then,
  ) = __$$MessageTemplateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String content,
    MessageTemplateType type,
    String? description,
    bool isActive,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$MessageTemplateImplCopyWithImpl<$Res>
    extends _$MessageTemplateCopyWithImpl<$Res, _$MessageTemplateImpl>
    implements _$$MessageTemplateImplCopyWith<$Res> {
  __$$MessageTemplateImplCopyWithImpl(
    _$MessageTemplateImpl _value,
    $Res Function(_$MessageTemplateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MessageTemplate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? content = null,
    Object? type = null,
    Object? description = freezed,
    Object? isActive = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$MessageTemplateImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as MessageTemplateType,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageTemplateImpl implements _MessageTemplate {
  const _$MessageTemplateImpl({
    required this.id,
    required this.name,
    required this.content,
    required this.type,
    this.description,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$MessageTemplateImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageTemplateImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String content;
  @override
  final MessageTemplateType type;
  @override
  final String? description;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'MessageTemplate(id: $id, name: $name, content: $content, type: $type, description: $description, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageTemplateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    content,
    type,
    description,
    isActive,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of MessageTemplate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageTemplateImplCopyWith<_$MessageTemplateImpl> get copyWith =>
      __$$MessageTemplateImplCopyWithImpl<_$MessageTemplateImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageTemplateImplToJson(this);
  }
}

abstract class _MessageTemplate implements MessageTemplate {
  const factory _MessageTemplate({
    required final String id,
    required final String name,
    required final String content,
    required final MessageTemplateType type,
    final String? description,
    final bool isActive,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$MessageTemplateImpl;

  factory _MessageTemplate.fromJson(Map<String, dynamic> json) =
      _$MessageTemplateImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get content;
  @override
  MessageTemplateType get type;
  @override
  String? get description;
  @override
  bool get isActive;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of MessageTemplate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MessageTemplateImplCopyWith<_$MessageTemplateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MessageHistory _$MessageHistoryFromJson(Map<String, dynamic> json) {
  return _MessageHistory.fromJson(json);
}

/// @nodoc
mixin _$MessageHistory {
  String get id => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  MessageStatus get status => throw _privateConstructorUsedError;
  String? get templateId => throw _privateConstructorUsedError;
  DateTime? get sentAt => throw _privateConstructorUsedError;
  DateTime? get deliveredAt => throw _privateConstructorUsedError;
  DateTime? get readAt => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this MessageHistory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MessageHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MessageHistoryCopyWith<MessageHistory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageHistoryCopyWith<$Res> {
  factory $MessageHistoryCopyWith(
    MessageHistory value,
    $Res Function(MessageHistory) then,
  ) = _$MessageHistoryCopyWithImpl<$Res, MessageHistory>;
  @useResult
  $Res call({
    String id,
    String message,
    MessageStatus status,
    String? templateId,
    DateTime? sentAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    String? errorMessage,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$MessageHistoryCopyWithImpl<$Res, $Val extends MessageHistory>
    implements $MessageHistoryCopyWith<$Res> {
  _$MessageHistoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MessageHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? message = null,
    Object? status = null,
    Object? templateId = freezed,
    Object? sentAt = freezed,
    Object? deliveredAt = freezed,
    Object? readAt = freezed,
    Object? errorMessage = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as MessageStatus,
            templateId: freezed == templateId
                ? _value.templateId
                : templateId // ignore: cast_nullable_to_non_nullable
                      as String?,
            sentAt: freezed == sentAt
                ? _value.sentAt
                : sentAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            deliveredAt: freezed == deliveredAt
                ? _value.deliveredAt
                : deliveredAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            readAt: freezed == readAt
                ? _value.readAt
                : readAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MessageHistoryImplCopyWith<$Res>
    implements $MessageHistoryCopyWith<$Res> {
  factory _$$MessageHistoryImplCopyWith(
    _$MessageHistoryImpl value,
    $Res Function(_$MessageHistoryImpl) then,
  ) = __$$MessageHistoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String message,
    MessageStatus status,
    String? templateId,
    DateTime? sentAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    String? errorMessage,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$MessageHistoryImplCopyWithImpl<$Res>
    extends _$MessageHistoryCopyWithImpl<$Res, _$MessageHistoryImpl>
    implements _$$MessageHistoryImplCopyWith<$Res> {
  __$$MessageHistoryImplCopyWithImpl(
    _$MessageHistoryImpl _value,
    $Res Function(_$MessageHistoryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MessageHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? message = null,
    Object? status = null,
    Object? templateId = freezed,
    Object? sentAt = freezed,
    Object? deliveredAt = freezed,
    Object? readAt = freezed,
    Object? errorMessage = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$MessageHistoryImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as MessageStatus,
        templateId: freezed == templateId
            ? _value.templateId
            : templateId // ignore: cast_nullable_to_non_nullable
                  as String?,
        sentAt: freezed == sentAt
            ? _value.sentAt
            : sentAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        deliveredAt: freezed == deliveredAt
            ? _value.deliveredAt
            : deliveredAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        readAt: freezed == readAt
            ? _value.readAt
            : readAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageHistoryImpl implements _MessageHistory {
  const _$MessageHistoryImpl({
    required this.id,
    required this.message,
    required this.status,
    this.templateId,
    this.sentAt,
    this.deliveredAt,
    this.readAt,
    this.errorMessage,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$MessageHistoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageHistoryImplFromJson(json);

  @override
  final String id;
  @override
  final String message;
  @override
  final MessageStatus status;
  @override
  final String? templateId;
  @override
  final DateTime? sentAt;
  @override
  final DateTime? deliveredAt;
  @override
  final DateTime? readAt;
  @override
  final String? errorMessage;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'MessageHistory(id: $id, message: $message, status: $status, templateId: $templateId, sentAt: $sentAt, deliveredAt: $deliveredAt, readAt: $readAt, errorMessage: $errorMessage, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageHistoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.templateId, templateId) ||
                other.templateId == templateId) &&
            (identical(other.sentAt, sentAt) || other.sentAt == sentAt) &&
            (identical(other.deliveredAt, deliveredAt) ||
                other.deliveredAt == deliveredAt) &&
            (identical(other.readAt, readAt) || other.readAt == readAt) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    message,
    status,
    templateId,
    sentAt,
    deliveredAt,
    readAt,
    errorMessage,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of MessageHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageHistoryImplCopyWith<_$MessageHistoryImpl> get copyWith =>
      __$$MessageHistoryImplCopyWithImpl<_$MessageHistoryImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageHistoryImplToJson(this);
  }
}

abstract class _MessageHistory implements MessageHistory {
  const factory _MessageHistory({
    required final String id,
    required final String message,
    required final MessageStatus status,
    final String? templateId,
    final DateTime? sentAt,
    final DateTime? deliveredAt,
    final DateTime? readAt,
    final String? errorMessage,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$MessageHistoryImpl;

  factory _MessageHistory.fromJson(Map<String, dynamic> json) =
      _$MessageHistoryImpl.fromJson;

  @override
  String get id;
  @override
  String get message;
  @override
  MessageStatus get status;
  @override
  String? get templateId;
  @override
  DateTime? get sentAt;
  @override
  DateTime? get deliveredAt;
  @override
  DateTime? get readAt;
  @override
  String? get errorMessage;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of MessageHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MessageHistoryImplCopyWith<_$MessageHistoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WhatsAppContactModel _$WhatsAppContactModelFromJson(Map<String, dynamic> json) {
  return _WhatsAppContactModel.fromJson(json);
}

/// @nodoc
mixin _$WhatsAppContactModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  ContactType get type => throw _privateConstructorUsedError;
  ContactStatus get status => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  List<String> get messageTemplates => throw _privateConstructorUsedError;
  List<MessageHistory> get messageHistory => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  String? get profilePicture => throw _privateConstructorUsedError;
  String? get about => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this WhatsAppContactModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WhatsAppContactModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WhatsAppContactModelCopyWith<WhatsAppContactModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WhatsAppContactModelCopyWith<$Res> {
  factory $WhatsAppContactModelCopyWith(
    WhatsAppContactModel value,
    $Res Function(WhatsAppContactModel) then,
  ) = _$WhatsAppContactModelCopyWithImpl<$Res, WhatsAppContactModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String phoneNumber,
    String userId,
    ContactType type,
    ContactStatus status,
    bool isDeleted,
    List<String> messageTemplates,
    List<MessageHistory> messageHistory,
    Map<String, dynamic> metadata,
    String? profilePicture,
    String? about,
    String? notes,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$WhatsAppContactModelCopyWithImpl<
  $Res,
  $Val extends WhatsAppContactModel
>
    implements $WhatsAppContactModelCopyWith<$Res> {
  _$WhatsAppContactModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WhatsAppContactModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phoneNumber = null,
    Object? userId = null,
    Object? type = null,
    Object? status = null,
    Object? isDeleted = null,
    Object? messageTemplates = null,
    Object? messageHistory = null,
    Object? metadata = null,
    Object? profilePicture = freezed,
    Object? about = freezed,
    Object? notes = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            phoneNumber: null == phoneNumber
                ? _value.phoneNumber
                : phoneNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ContactType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as ContactStatus,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            messageTemplates: null == messageTemplates
                ? _value.messageTemplates
                : messageTemplates // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            messageHistory: null == messageHistory
                ? _value.messageHistory
                : messageHistory // ignore: cast_nullable_to_non_nullable
                      as List<MessageHistory>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            profilePicture: freezed == profilePicture
                ? _value.profilePicture
                : profilePicture // ignore: cast_nullable_to_non_nullable
                      as String?,
            about: freezed == about
                ? _value.about
                : about // ignore: cast_nullable_to_non_nullable
                      as String?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WhatsAppContactModelImplCopyWith<$Res>
    implements $WhatsAppContactModelCopyWith<$Res> {
  factory _$$WhatsAppContactModelImplCopyWith(
    _$WhatsAppContactModelImpl value,
    $Res Function(_$WhatsAppContactModelImpl) then,
  ) = __$$WhatsAppContactModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String phoneNumber,
    String userId,
    ContactType type,
    ContactStatus status,
    bool isDeleted,
    List<String> messageTemplates,
    List<MessageHistory> messageHistory,
    Map<String, dynamic> metadata,
    String? profilePicture,
    String? about,
    String? notes,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$WhatsAppContactModelImplCopyWithImpl<$Res>
    extends _$WhatsAppContactModelCopyWithImpl<$Res, _$WhatsAppContactModelImpl>
    implements _$$WhatsAppContactModelImplCopyWith<$Res> {
  __$$WhatsAppContactModelImplCopyWithImpl(
    _$WhatsAppContactModelImpl _value,
    $Res Function(_$WhatsAppContactModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WhatsAppContactModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phoneNumber = null,
    Object? userId = null,
    Object? type = null,
    Object? status = null,
    Object? isDeleted = null,
    Object? messageTemplates = null,
    Object? messageHistory = null,
    Object? metadata = null,
    Object? profilePicture = freezed,
    Object? about = freezed,
    Object? notes = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$WhatsAppContactModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        phoneNumber: null == phoneNumber
            ? _value.phoneNumber
            : phoneNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ContactType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as ContactStatus,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        messageTemplates: null == messageTemplates
            ? _value._messageTemplates
            : messageTemplates // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        messageHistory: null == messageHistory
            ? _value._messageHistory
            : messageHistory // ignore: cast_nullable_to_non_nullable
                  as List<MessageHistory>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        profilePicture: freezed == profilePicture
            ? _value.profilePicture
            : profilePicture // ignore: cast_nullable_to_non_nullable
                  as String?,
        about: freezed == about
            ? _value.about
            : about // ignore: cast_nullable_to_non_nullable
                  as String?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WhatsAppContactModelImpl implements _WhatsAppContactModel {
  const _$WhatsAppContactModelImpl({
    this.id = '',
    required this.name,
    required this.phoneNumber,
    required this.userId,
    required this.type,
    this.status = ContactStatus.active,
    this.isDeleted = false,
    final List<String> messageTemplates = const [],
    final List<MessageHistory> messageHistory = const [],
    final Map<String, dynamic> metadata = const {},
    this.profilePicture,
    this.about,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  }) : _messageTemplates = messageTemplates,
       _messageHistory = messageHistory,
       _metadata = metadata;

  factory _$WhatsAppContactModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WhatsAppContactModelImplFromJson(json);

  @override
  @JsonKey()
  final String id;
  @override
  final String name;
  @override
  final String phoneNumber;
  @override
  final String userId;
  @override
  final ContactType type;
  @override
  @JsonKey()
  final ContactStatus status;
  @override
  @JsonKey()
  final bool isDeleted;
  final List<String> _messageTemplates;
  @override
  @JsonKey()
  List<String> get messageTemplates {
    if (_messageTemplates is EqualUnmodifiableListView)
      return _messageTemplates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messageTemplates);
  }

  final List<MessageHistory> _messageHistory;
  @override
  @JsonKey()
  List<MessageHistory> get messageHistory {
    if (_messageHistory is EqualUnmodifiableListView) return _messageHistory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messageHistory);
  }

  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final String? profilePicture;
  @override
  final String? about;
  @override
  final String? notes;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'WhatsAppContactModel(id: $id, name: $name, phoneNumber: $phoneNumber, userId: $userId, type: $type, status: $status, isDeleted: $isDeleted, messageTemplates: $messageTemplates, messageHistory: $messageHistory, metadata: $metadata, profilePicture: $profilePicture, about: $about, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WhatsAppContactModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            const DeepCollectionEquality().equals(
              other._messageTemplates,
              _messageTemplates,
            ) &&
            const DeepCollectionEquality().equals(
              other._messageHistory,
              _messageHistory,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.profilePicture, profilePicture) ||
                other.profilePicture == profilePicture) &&
            (identical(other.about, about) || other.about == about) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    phoneNumber,
    userId,
    type,
    status,
    isDeleted,
    const DeepCollectionEquality().hash(_messageTemplates),
    const DeepCollectionEquality().hash(_messageHistory),
    const DeepCollectionEquality().hash(_metadata),
    profilePicture,
    about,
    notes,
    createdAt,
    updatedAt,
  );

  /// Create a copy of WhatsAppContactModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WhatsAppContactModelImplCopyWith<_$WhatsAppContactModelImpl>
  get copyWith =>
      __$$WhatsAppContactModelImplCopyWithImpl<_$WhatsAppContactModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$WhatsAppContactModelImplToJson(this);
  }
}

abstract class _WhatsAppContactModel implements WhatsAppContactModel {
  const factory _WhatsAppContactModel({
    final String id,
    required final String name,
    required final String phoneNumber,
    required final String userId,
    required final ContactType type,
    final ContactStatus status,
    final bool isDeleted,
    final List<String> messageTemplates,
    final List<MessageHistory> messageHistory,
    final Map<String, dynamic> metadata,
    final String? profilePicture,
    final String? about,
    final String? notes,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$WhatsAppContactModelImpl;

  factory _WhatsAppContactModel.fromJson(Map<String, dynamic> json) =
      _$WhatsAppContactModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get phoneNumber;
  @override
  String get userId;
  @override
  ContactType get type;
  @override
  ContactStatus get status;
  @override
  bool get isDeleted;
  @override
  List<String> get messageTemplates;
  @override
  List<MessageHistory> get messageHistory;
  @override
  Map<String, dynamic> get metadata;
  @override
  String? get profilePicture;
  @override
  String? get about;
  @override
  String? get notes;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of WhatsAppContactModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WhatsAppContactModelImplCopyWith<_$WhatsAppContactModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
