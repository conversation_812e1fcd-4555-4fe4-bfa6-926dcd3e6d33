// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'connected_device_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ConnectedDeviceModel _$ConnectedDeviceModelFromJson(Map<String, dynamic> json) {
  return _ConnectedDeviceModel.fromJson(json);
}

/// @nodoc
mixin _$ConnectedDeviceModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  DateTime get lastActive => throw _privateConstructorUsedError;
  String get deviceInfo => throw _privateConstructorUsedError;
  bool get isCurrentDevice => throw _privateConstructorUsedError;
  bool get isAuthorized => throw _privateConstructorUsedError;

  /// Serializes this ConnectedDeviceModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConnectedDeviceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConnectedDeviceModelCopyWith<ConnectedDeviceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConnectedDeviceModelCopyWith<$Res> {
  factory $ConnectedDeviceModelCopyWith(
    ConnectedDeviceModel value,
    $Res Function(ConnectedDeviceModel) then,
  ) = _$ConnectedDeviceModelCopyWithImpl<$Res, ConnectedDeviceModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String type,
    DateTime lastActive,
    String deviceInfo,
    bool isCurrentDevice,
    bool isAuthorized,
  });
}

/// @nodoc
class _$ConnectedDeviceModelCopyWithImpl<
  $Res,
  $Val extends ConnectedDeviceModel
>
    implements $ConnectedDeviceModelCopyWith<$Res> {
  _$ConnectedDeviceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConnectedDeviceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? lastActive = null,
    Object? deviceInfo = null,
    Object? isCurrentDevice = null,
    Object? isAuthorized = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as String,
            lastActive: null == lastActive
                ? _value.lastActive
                : lastActive // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            deviceInfo: null == deviceInfo
                ? _value.deviceInfo
                : deviceInfo // ignore: cast_nullable_to_non_nullable
                      as String,
            isCurrentDevice: null == isCurrentDevice
                ? _value.isCurrentDevice
                : isCurrentDevice // ignore: cast_nullable_to_non_nullable
                      as bool,
            isAuthorized: null == isAuthorized
                ? _value.isAuthorized
                : isAuthorized // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ConnectedDeviceModelImplCopyWith<$Res>
    implements $ConnectedDeviceModelCopyWith<$Res> {
  factory _$$ConnectedDeviceModelImplCopyWith(
    _$ConnectedDeviceModelImpl value,
    $Res Function(_$ConnectedDeviceModelImpl) then,
  ) = __$$ConnectedDeviceModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String type,
    DateTime lastActive,
    String deviceInfo,
    bool isCurrentDevice,
    bool isAuthorized,
  });
}

/// @nodoc
class __$$ConnectedDeviceModelImplCopyWithImpl<$Res>
    extends _$ConnectedDeviceModelCopyWithImpl<$Res, _$ConnectedDeviceModelImpl>
    implements _$$ConnectedDeviceModelImplCopyWith<$Res> {
  __$$ConnectedDeviceModelImplCopyWithImpl(
    _$ConnectedDeviceModelImpl _value,
    $Res Function(_$ConnectedDeviceModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ConnectedDeviceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? lastActive = null,
    Object? deviceInfo = null,
    Object? isCurrentDevice = null,
    Object? isAuthorized = null,
  }) {
    return _then(
      _$ConnectedDeviceModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as String,
        lastActive: null == lastActive
            ? _value.lastActive
            : lastActive // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        deviceInfo: null == deviceInfo
            ? _value.deviceInfo
            : deviceInfo // ignore: cast_nullable_to_non_nullable
                  as String,
        isCurrentDevice: null == isCurrentDevice
            ? _value.isCurrentDevice
            : isCurrentDevice // ignore: cast_nullable_to_non_nullable
                  as bool,
        isAuthorized: null == isAuthorized
            ? _value.isAuthorized
            : isAuthorized // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ConnectedDeviceModelImpl implements _ConnectedDeviceModel {
  const _$ConnectedDeviceModelImpl({
    required this.id,
    required this.name,
    required this.type,
    required this.lastActive,
    required this.deviceInfo,
    required this.isCurrentDevice,
    this.isAuthorized = false,
  });

  factory _$ConnectedDeviceModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConnectedDeviceModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String type;
  @override
  final DateTime lastActive;
  @override
  final String deviceInfo;
  @override
  final bool isCurrentDevice;
  @override
  @JsonKey()
  final bool isAuthorized;

  @override
  String toString() {
    return 'ConnectedDeviceModel(id: $id, name: $name, type: $type, lastActive: $lastActive, deviceInfo: $deviceInfo, isCurrentDevice: $isCurrentDevice, isAuthorized: $isAuthorized)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConnectedDeviceModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.lastActive, lastActive) ||
                other.lastActive == lastActive) &&
            (identical(other.deviceInfo, deviceInfo) ||
                other.deviceInfo == deviceInfo) &&
            (identical(other.isCurrentDevice, isCurrentDevice) ||
                other.isCurrentDevice == isCurrentDevice) &&
            (identical(other.isAuthorized, isAuthorized) ||
                other.isAuthorized == isAuthorized));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    type,
    lastActive,
    deviceInfo,
    isCurrentDevice,
    isAuthorized,
  );

  /// Create a copy of ConnectedDeviceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConnectedDeviceModelImplCopyWith<_$ConnectedDeviceModelImpl>
  get copyWith =>
      __$$ConnectedDeviceModelImplCopyWithImpl<_$ConnectedDeviceModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ConnectedDeviceModelImplToJson(this);
  }
}

abstract class _ConnectedDeviceModel implements ConnectedDeviceModel {
  const factory _ConnectedDeviceModel({
    required final String id,
    required final String name,
    required final String type,
    required final DateTime lastActive,
    required final String deviceInfo,
    required final bool isCurrentDevice,
    final bool isAuthorized,
  }) = _$ConnectedDeviceModelImpl;

  factory _ConnectedDeviceModel.fromJson(Map<String, dynamic> json) =
      _$ConnectedDeviceModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get type;
  @override
  DateTime get lastActive;
  @override
  String get deviceInfo;
  @override
  bool get isCurrentDevice;
  @override
  bool get isAuthorized;

  /// Create a copy of ConnectedDeviceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConnectedDeviceModelImplCopyWith<_$ConnectedDeviceModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
