// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$storagePermissionServiceHash() =>
    r'7f8d170a34d406aa8fe847cb5c49cf9f9cec5ed5';

/// See also [storagePermissionService].
@ProviderFor(storagePermissionService)
final storagePermissionServiceProvider =
    AutoDisposeProvider<StoragePermissionService>.internal(
      storagePermissionService,
      name: r'storagePermissionServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$storagePermissionServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef StoragePermissionServiceRef =
    AutoDisposeProviderRef<StoragePermissionService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
