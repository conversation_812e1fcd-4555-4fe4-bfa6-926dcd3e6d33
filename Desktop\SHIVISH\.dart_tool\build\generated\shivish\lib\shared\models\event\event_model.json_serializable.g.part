// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EventProductImpl _$$EventProductImplFromJson(Map<String, dynamic> json) =>
    _$EventProductImpl(
      productId: json['productId'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      imageUrl: json['imageUrl'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$EventProductImplToJson(_$EventProductImpl instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'name': instance.name,
      'price': instance.price,
      'quantity': instance.quantity,
      'imageUrl': instance.imageUrl,
      'description': instance.description,
    };

_$EventModelImpl _$$EventModelImplFromJson(Map<String, dynamic> json) =>
    _$EventModelImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$EventTypeEnumMap, json['type']),
      status: $enumDecode(_$EventStatusEnumMap, json['status']),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      createdBy: json['createdBy'] as String,
      approvedBy: json['approvedBy'] as String?,
      rejectedBy: json['rejectedBy'] as String?,
      rejectionReason: json['rejectionReason'] as String?,
      isPublic: json['isPublic'] as bool? ?? false,
      isRecurring: json['isRecurring'] as bool? ?? false,
      recurrencePattern: json['recurrencePattern'] as String?,
      reminderBefore: json['reminderBefore'] == null
          ? null
          : Duration(microseconds: (json['reminderBefore'] as num).toInt()),
      whatsappMessage: json['whatsappMessage'] as String?,
      whatsappContacts: (json['whatsappContacts'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      mediaUrls: (json['mediaUrls'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      products: (json['products'] as List<dynamic>?)
          ?.map((e) => EventProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$EventModelImplToJson(_$EventModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$EventTypeEnumMap[instance.type]!,
      'status': _$EventStatusEnumMap[instance.status]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'createdBy': instance.createdBy,
      'approvedBy': instance.approvedBy,
      'rejectedBy': instance.rejectedBy,
      'rejectionReason': instance.rejectionReason,
      'isPublic': instance.isPublic,
      'isRecurring': instance.isRecurring,
      'recurrencePattern': instance.recurrencePattern,
      'reminderBefore': instance.reminderBefore?.inMicroseconds,
      'whatsappMessage': instance.whatsappMessage,
      'whatsappContacts': instance.whatsappContacts,
      'mediaUrls': instance.mediaUrls,
      'products': instance.products?.map((e) => e.toJson()).toList(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

const _$EventTypeEnumMap = {
  EventType.festival: 0,
  EventType.birthday: 1,
  EventType.anniversary: 2,
  EventType.custom: 3,
};

const _$EventStatusEnumMap = {
  EventStatus.draft: 0,
  EventStatus.pending: 1,
  EventStatus.approved: 2,
  EventStatus.rejected: 3,
  EventStatus.cancelled: 4,
  EventStatus.completed: 5,
};
