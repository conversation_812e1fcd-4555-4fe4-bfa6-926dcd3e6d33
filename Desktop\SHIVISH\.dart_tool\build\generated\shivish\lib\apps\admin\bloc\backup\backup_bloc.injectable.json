[{"type": {"import": "package:shivish/apps/admin/bloc/backup/backup_bloc.dart", "name": "BackupBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/backup/backup_bloc.dart", "name": "BackupBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/backup_service.dart", "name": "BackupService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_backupService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]