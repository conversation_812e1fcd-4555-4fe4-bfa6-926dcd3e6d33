// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PanchangamNotificationSettingsImpl
_$$PanchangamNotificationSettingsImplFromJson(Map<String, dynamic> json) =>
    _$PanchangamNotificationSettingsImpl(
      isEnabled: json['isEnabled'] as bool? ?? true,
      notificationHour: (json['notificationHour'] as num?)?.toInt() ?? 7,
      notificationMinute: (json['notificationMinute'] as num?)?.toInt() ?? 0,
      language: json['language'] as String? ?? 'en',
      location: json['location'] as String? ?? 'Mumbai',
    );

Map<String, dynamic> _$$PanchangamNotificationSettingsImplToJson(
  _$PanchangamNotificationSettingsImpl instance,
) => <String, dynamic>{
  'isEnabled': instance.isEnabled,
  'notificationHour': instance.notificationHour,
  'notificationMinute': instance.notificationMinute,
  'language': instance.language,
  'location': instance.location,
};
