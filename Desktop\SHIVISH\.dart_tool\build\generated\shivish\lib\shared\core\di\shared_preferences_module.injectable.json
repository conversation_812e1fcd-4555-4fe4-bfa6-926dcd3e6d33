[{"type": {"import": "package:shared_preferences/shared_preferences.dart", "name": "SharedPreferences", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:shared_preferences/src/shared_preferences_legacy.dart"]}, "typeImpl": {"import": "package:shared_preferences/shared_preferences.dart", "name": "SharedPreferences", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:shared_preferences/src/shared_preferences_legacy.dart"]}, "isAsync": true, "postConstructReturnsSelf": false, "preResolve": true, "canBeConst": false, "injectableType": 1, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:shivish/shared/core/di/shared_preferences_module.dart", "name": "SharedPreferencesModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "prefs"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "prefs", "orderPosition": 0}]