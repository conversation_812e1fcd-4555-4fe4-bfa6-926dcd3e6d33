// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_tracking_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderTrackingModelImpl _$$OrderTrackingModelImplFromJson(
  Map<String, dynamic> json,
) => _$OrderTrackingModelImpl(
  id: json['id'] as String,
  orderId: json['orderId'] as String,
  status: $enumDecode(_$OrderTrackingStatusEnumMap, json['status']),
  location: json['location'] as String,
  description: json['description'] as String?,
  timestamp: DateTime.parse(json['timestamp'] as String),
  updatedBy: json['updatedBy'] as String?,
  latitude: (json['latitude'] as num?)?.toDouble(),
  longitude: (json['longitude'] as num?)?.toDouble(),
  estimatedDeliveryTime: json['estimatedDeliveryTime'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$OrderTrackingModelImplToJson(
  _$OrderTrackingModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'orderId': instance.orderId,
  'status': instance.status.toJson(),
  'location': instance.location,
  'description': instance.description,
  'timestamp': instance.timestamp.toIso8601String(),
  'updatedBy': instance.updatedBy,
  'latitude': instance.latitude,
  'longitude': instance.longitude,
  'estimatedDeliveryTime': instance.estimatedDeliveryTime,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$OrderTrackingStatusEnumMap = {
  OrderTrackingStatus.orderPlaced: 'order_placed',
  OrderTrackingStatus.orderConfirmed: 'order_confirmed',
  OrderTrackingStatus.orderProcessing: 'order_processing',
  OrderTrackingStatus.orderShipped: 'order_shipped',
  OrderTrackingStatus.outForDelivery: 'out_for_delivery',
  OrderTrackingStatus.delivered: 'delivered',
  OrderTrackingStatus.cancelled: 'cancelled',
  OrderTrackingStatus.returned: 'returned',
};
