// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AISettingsImpl _$$AISettingsImplFromJson(
  Map<String, dynamic> json,
) => _$AISettingsImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  voiceCommandSettings: json['voiceCommandSettings'] as Map<String, dynamic>,
  chatbotSettings: json['chatbotSettings'] as Map<String, dynamic>,
  languageModelSettings: json['languageModelSettings'] as Map<String, dynamic>,
  responseCustomization: json['responseCustomization'] as Map<String, dynamic>,
  voiceRecognitionSettings:
      json['voiceRecognitionSettings'] as Map<String, dynamic>,
  isEnabled: json['isEnabled'] as bool,
  createdAt: json['createdAt'] as String,
  updatedAt: json['updatedAt'] as String,
);

Map<String, dynamic> _$$AISettingsImplToJson(_$AISettingsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'voiceCommandSettings': instance.voiceCommandSettings,
      'chatbotSettings': instance.chatbotSettings,
      'languageModelSettings': instance.languageModelSettings,
      'responseCustomization': instance.responseCustomization,
      'voiceRecognitionSettings': instance.voiceRecognitionSettings,
      'isEnabled': instance.isEnabled,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
