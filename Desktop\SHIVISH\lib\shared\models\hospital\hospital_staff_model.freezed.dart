// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hospital_staff_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

HospitalStaffModel _$HospitalStaffModelFromJson(Map<String, dynamic> json) {
  return _HospitalStaffModel.fromJson(json);
}

/// @nodoc
mixin _$HospitalStaffModel {
  String get id => throw _privateConstructorUsedError;
  String get hospitalId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  HospitalUserRole get role => throw _privateConstructorUsedError;
  String get accessCode => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  String? get profileImage => throw _privateConstructorUsedError;
  String get createdBy => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this HospitalStaffModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HospitalStaffModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HospitalStaffModelCopyWith<HospitalStaffModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HospitalStaffModelCopyWith<$Res> {
  factory $HospitalStaffModelCopyWith(
    HospitalStaffModel value,
    $Res Function(HospitalStaffModel) then,
  ) = _$HospitalStaffModelCopyWithImpl<$Res, HospitalStaffModel>;
  @useResult
  $Res call({
    String id,
    String hospitalId,
    String name,
    String email,
    String phone,
    HospitalUserRole role,
    String accessCode,
    bool isActive,
    String? profileImage,
    String createdBy,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$HospitalStaffModelCopyWithImpl<$Res, $Val extends HospitalStaffModel>
    implements $HospitalStaffModelCopyWith<$Res> {
  _$HospitalStaffModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HospitalStaffModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? hospitalId = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? role = null,
    Object? accessCode = null,
    Object? isActive = null,
    Object? profileImage = freezed,
    Object? createdBy = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            hospitalId: null == hospitalId
                ? _value.hospitalId
                : hospitalId // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            role: null == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as HospitalUserRole,
            accessCode: null == accessCode
                ? _value.accessCode
                : accessCode // ignore: cast_nullable_to_non_nullable
                      as String,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            profileImage: freezed == profileImage
                ? _value.profileImage
                : profileImage // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdBy: null == createdBy
                ? _value.createdBy
                : createdBy // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$HospitalStaffModelImplCopyWith<$Res>
    implements $HospitalStaffModelCopyWith<$Res> {
  factory _$$HospitalStaffModelImplCopyWith(
    _$HospitalStaffModelImpl value,
    $Res Function(_$HospitalStaffModelImpl) then,
  ) = __$$HospitalStaffModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String hospitalId,
    String name,
    String email,
    String phone,
    HospitalUserRole role,
    String accessCode,
    bool isActive,
    String? profileImage,
    String createdBy,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$HospitalStaffModelImplCopyWithImpl<$Res>
    extends _$HospitalStaffModelCopyWithImpl<$Res, _$HospitalStaffModelImpl>
    implements _$$HospitalStaffModelImplCopyWith<$Res> {
  __$$HospitalStaffModelImplCopyWithImpl(
    _$HospitalStaffModelImpl _value,
    $Res Function(_$HospitalStaffModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of HospitalStaffModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? hospitalId = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? role = null,
    Object? accessCode = null,
    Object? isActive = null,
    Object? profileImage = freezed,
    Object? createdBy = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$HospitalStaffModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        hospitalId: null == hospitalId
            ? _value.hospitalId
            : hospitalId // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        role: null == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as HospitalUserRole,
        accessCode: null == accessCode
            ? _value.accessCode
            : accessCode // ignore: cast_nullable_to_non_nullable
                  as String,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        profileImage: freezed == profileImage
            ? _value.profileImage
            : profileImage // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdBy: null == createdBy
            ? _value.createdBy
            : createdBy // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$HospitalStaffModelImpl implements _HospitalStaffModel {
  const _$HospitalStaffModelImpl({
    required this.id,
    required this.hospitalId,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.accessCode,
    required this.isActive,
    this.profileImage,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$HospitalStaffModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$HospitalStaffModelImplFromJson(json);

  @override
  final String id;
  @override
  final String hospitalId;
  @override
  final String name;
  @override
  final String email;
  @override
  final String phone;
  @override
  final HospitalUserRole role;
  @override
  final String accessCode;
  @override
  final bool isActive;
  @override
  final String? profileImage;
  @override
  final String createdBy;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'HospitalStaffModel(id: $id, hospitalId: $hospitalId, name: $name, email: $email, phone: $phone, role: $role, accessCode: $accessCode, isActive: $isActive, profileImage: $profileImage, createdBy: $createdBy, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HospitalStaffModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.hospitalId, hospitalId) ||
                other.hospitalId == hospitalId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.accessCode, accessCode) ||
                other.accessCode == accessCode) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    hospitalId,
    name,
    email,
    phone,
    role,
    accessCode,
    isActive,
    profileImage,
    createdBy,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of HospitalStaffModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HospitalStaffModelImplCopyWith<_$HospitalStaffModelImpl> get copyWith =>
      __$$HospitalStaffModelImplCopyWithImpl<_$HospitalStaffModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$HospitalStaffModelImplToJson(this);
  }
}

abstract class _HospitalStaffModel implements HospitalStaffModel {
  const factory _HospitalStaffModel({
    required final String id,
    required final String hospitalId,
    required final String name,
    required final String email,
    required final String phone,
    required final HospitalUserRole role,
    required final String accessCode,
    required final bool isActive,
    final String? profileImage,
    required final String createdBy,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$HospitalStaffModelImpl;

  factory _HospitalStaffModel.fromJson(Map<String, dynamic> json) =
      _$HospitalStaffModelImpl.fromJson;

  @override
  String get id;
  @override
  String get hospitalId;
  @override
  String get name;
  @override
  String get email;
  @override
  String get phone;
  @override
  HospitalUserRole get role;
  @override
  String get accessCode;
  @override
  bool get isActive;
  @override
  String? get profileImage;
  @override
  String get createdBy;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of HospitalStaffModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HospitalStaffModelImplCopyWith<_$HospitalStaffModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
