// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'article_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ArticleModel _$ArticleModelFromJson(Map<String, dynamic> json) {
  return _ArticleModel.fromJson(json);
}

/// @nodoc
mixin _$ArticleModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  String get readTime => throw _privateConstructorUsedError;
  bool get isPopular => throw _privateConstructorUsedError;
  bool get isBookmarked => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  List<ArticleModel>? get relatedArticles => throw _privateConstructorUsedError;

  /// Serializes this ArticleModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ArticleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ArticleModelCopyWith<ArticleModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArticleModelCopyWith<$Res> {
  factory $ArticleModelCopyWith(
    ArticleModel value,
    $Res Function(ArticleModel) then,
  ) = _$ArticleModelCopyWithImpl<$Res, ArticleModel>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String content,
    String category,
    String readTime,
    bool isPopular,
    bool isBookmarked,
    DateTime createdAt,
    DateTime updatedAt,
    List<ArticleModel>? relatedArticles,
  });
}

/// @nodoc
class _$ArticleModelCopyWithImpl<$Res, $Val extends ArticleModel>
    implements $ArticleModelCopyWith<$Res> {
  _$ArticleModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ArticleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? content = null,
    Object? category = null,
    Object? readTime = null,
    Object? isPopular = null,
    Object? isBookmarked = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? relatedArticles = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            category: null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                      as String,
            readTime: null == readTime
                ? _value.readTime
                : readTime // ignore: cast_nullable_to_non_nullable
                      as String,
            isPopular: null == isPopular
                ? _value.isPopular
                : isPopular // ignore: cast_nullable_to_non_nullable
                      as bool,
            isBookmarked: null == isBookmarked
                ? _value.isBookmarked
                : isBookmarked // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            relatedArticles: freezed == relatedArticles
                ? _value.relatedArticles
                : relatedArticles // ignore: cast_nullable_to_non_nullable
                      as List<ArticleModel>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ArticleModelImplCopyWith<$Res>
    implements $ArticleModelCopyWith<$Res> {
  factory _$$ArticleModelImplCopyWith(
    _$ArticleModelImpl value,
    $Res Function(_$ArticleModelImpl) then,
  ) = __$$ArticleModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String content,
    String category,
    String readTime,
    bool isPopular,
    bool isBookmarked,
    DateTime createdAt,
    DateTime updatedAt,
    List<ArticleModel>? relatedArticles,
  });
}

/// @nodoc
class __$$ArticleModelImplCopyWithImpl<$Res>
    extends _$ArticleModelCopyWithImpl<$Res, _$ArticleModelImpl>
    implements _$$ArticleModelImplCopyWith<$Res> {
  __$$ArticleModelImplCopyWithImpl(
    _$ArticleModelImpl _value,
    $Res Function(_$ArticleModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ArticleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? content = null,
    Object? category = null,
    Object? readTime = null,
    Object? isPopular = null,
    Object? isBookmarked = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? relatedArticles = freezed,
  }) {
    return _then(
      _$ArticleModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        category: null == category
            ? _value.category
            : category // ignore: cast_nullable_to_non_nullable
                  as String,
        readTime: null == readTime
            ? _value.readTime
            : readTime // ignore: cast_nullable_to_non_nullable
                  as String,
        isPopular: null == isPopular
            ? _value.isPopular
            : isPopular // ignore: cast_nullable_to_non_nullable
                  as bool,
        isBookmarked: null == isBookmarked
            ? _value.isBookmarked
            : isBookmarked // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        relatedArticles: freezed == relatedArticles
            ? _value._relatedArticles
            : relatedArticles // ignore: cast_nullable_to_non_nullable
                  as List<ArticleModel>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ArticleModelImpl implements _ArticleModel {
  const _$ArticleModelImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.content,
    required this.category,
    required this.readTime,
    this.isPopular = false,
    this.isBookmarked = false,
    required this.createdAt,
    required this.updatedAt,
    final List<ArticleModel>? relatedArticles,
  }) : _relatedArticles = relatedArticles;

  factory _$ArticleModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ArticleModelImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String content;
  @override
  final String category;
  @override
  final String readTime;
  @override
  @JsonKey()
  final bool isPopular;
  @override
  @JsonKey()
  final bool isBookmarked;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  final List<ArticleModel>? _relatedArticles;
  @override
  List<ArticleModel>? get relatedArticles {
    final value = _relatedArticles;
    if (value == null) return null;
    if (_relatedArticles is EqualUnmodifiableListView) return _relatedArticles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ArticleModel(id: $id, title: $title, description: $description, content: $content, category: $category, readTime: $readTime, isPopular: $isPopular, isBookmarked: $isBookmarked, createdAt: $createdAt, updatedAt: $updatedAt, relatedArticles: $relatedArticles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ArticleModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.readTime, readTime) ||
                other.readTime == readTime) &&
            (identical(other.isPopular, isPopular) ||
                other.isPopular == isPopular) &&
            (identical(other.isBookmarked, isBookmarked) ||
                other.isBookmarked == isBookmarked) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(
              other._relatedArticles,
              _relatedArticles,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    content,
    category,
    readTime,
    isPopular,
    isBookmarked,
    createdAt,
    updatedAt,
    const DeepCollectionEquality().hash(_relatedArticles),
  );

  /// Create a copy of ArticleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ArticleModelImplCopyWith<_$ArticleModelImpl> get copyWith =>
      __$$ArticleModelImplCopyWithImpl<_$ArticleModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ArticleModelImplToJson(this);
  }
}

abstract class _ArticleModel implements ArticleModel {
  const factory _ArticleModel({
    required final String id,
    required final String title,
    required final String description,
    required final String content,
    required final String category,
    required final String readTime,
    final bool isPopular,
    final bool isBookmarked,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final List<ArticleModel>? relatedArticles,
  }) = _$ArticleModelImpl;

  factory _ArticleModel.fromJson(Map<String, dynamic> json) =
      _$ArticleModelImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get content;
  @override
  String get category;
  @override
  String get readTime;
  @override
  bool get isPopular;
  @override
  bool get isBookmarked;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  List<ArticleModel>? get relatedArticles;

  /// Create a copy of ArticleModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ArticleModelImplCopyWith<_$ArticleModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ArticleCategoryModel _$ArticleCategoryModelFromJson(Map<String, dynamic> json) {
  return _ArticleCategoryModel.fromJson(json);
}

/// @nodoc
mixin _$ArticleCategoryModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get icon => throw _privateConstructorUsedError;
  List<ArticleModel> get articles => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ArticleCategoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ArticleCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ArticleCategoryModelCopyWith<ArticleCategoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArticleCategoryModelCopyWith<$Res> {
  factory $ArticleCategoryModelCopyWith(
    ArticleCategoryModel value,
    $Res Function(ArticleCategoryModel) then,
  ) = _$ArticleCategoryModelCopyWithImpl<$Res, ArticleCategoryModel>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String icon,
    List<ArticleModel> articles,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$ArticleCategoryModelCopyWithImpl<
  $Res,
  $Val extends ArticleCategoryModel
>
    implements $ArticleCategoryModelCopyWith<$Res> {
  _$ArticleCategoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ArticleCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? icon = null,
    Object? articles = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            icon: null == icon
                ? _value.icon
                : icon // ignore: cast_nullable_to_non_nullable
                      as String,
            articles: null == articles
                ? _value.articles
                : articles // ignore: cast_nullable_to_non_nullable
                      as List<ArticleModel>,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ArticleCategoryModelImplCopyWith<$Res>
    implements $ArticleCategoryModelCopyWith<$Res> {
  factory _$$ArticleCategoryModelImplCopyWith(
    _$ArticleCategoryModelImpl value,
    $Res Function(_$ArticleCategoryModelImpl) then,
  ) = __$$ArticleCategoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String icon,
    List<ArticleModel> articles,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$ArticleCategoryModelImplCopyWithImpl<$Res>
    extends _$ArticleCategoryModelCopyWithImpl<$Res, _$ArticleCategoryModelImpl>
    implements _$$ArticleCategoryModelImplCopyWith<$Res> {
  __$$ArticleCategoryModelImplCopyWithImpl(
    _$ArticleCategoryModelImpl _value,
    $Res Function(_$ArticleCategoryModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ArticleCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? icon = null,
    Object? articles = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$ArticleCategoryModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _value.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        articles: null == articles
            ? _value._articles
            : articles // ignore: cast_nullable_to_non_nullable
                  as List<ArticleModel>,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ArticleCategoryModelImpl implements _ArticleCategoryModel {
  const _$ArticleCategoryModelImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required final List<ArticleModel> articles,
    required this.createdAt,
    required this.updatedAt,
  }) : _articles = articles;

  factory _$ArticleCategoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ArticleCategoryModelImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String icon;
  final List<ArticleModel> _articles;
  @override
  List<ArticleModel> get articles {
    if (_articles is EqualUnmodifiableListView) return _articles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_articles);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'ArticleCategoryModel(id: $id, title: $title, description: $description, icon: $icon, articles: $articles, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ArticleCategoryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            const DeepCollectionEquality().equals(other._articles, _articles) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    icon,
    const DeepCollectionEquality().hash(_articles),
    createdAt,
    updatedAt,
  );

  /// Create a copy of ArticleCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ArticleCategoryModelImplCopyWith<_$ArticleCategoryModelImpl>
  get copyWith =>
      __$$ArticleCategoryModelImplCopyWithImpl<_$ArticleCategoryModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ArticleCategoryModelImplToJson(this);
  }
}

abstract class _ArticleCategoryModel implements ArticleCategoryModel {
  const factory _ArticleCategoryModel({
    required final String id,
    required final String title,
    required final String description,
    required final String icon,
    required final List<ArticleModel> articles,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$ArticleCategoryModelImpl;

  factory _ArticleCategoryModel.fromJson(Map<String, dynamic> json) =
      _$ArticleCategoryModelImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get icon;
  @override
  List<ArticleModel> get articles;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of ArticleCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ArticleCategoryModelImplCopyWith<_$ArticleCategoryModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
