// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeliveryRequestModelImpl _$$DeliveryRequestModelImplFromJson(
  Map<String, dynamic> json,
) => _$DeliveryRequestModelImpl(
  id: json['id'] as String,
  orderId: json['orderId'] as String,
  buyerId: json['buyerId'] as String,
  sellerId: json['sellerId'] as String,
  status: $enumDecode(_$DeliveryRequestStatusEnumMap, json['status']),
  pickupLocation: const GeoPointConverter().fromJson(
    json['pickupLocation'] as Map<String, dynamic>,
  ),
  deliveryLocation: const GeoPointConverter().fromJson(
    json['deliveryLocation'] as Map<String, dynamic>,
  ),
  pickupAddress: json['pickupAddress'] as String,
  deliveryAddress: json['deliveryAddress'] as String,
  distance: (json['distance'] as num).toDouble(),
  deliveryCharge: (json['deliveryCharge'] as num).toDouble(),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  deliveryPersonId: json['deliveryPersonId'] as String?,
  buyerName: json['buyerName'] as String?,
  buyerPhone: json['buyerPhone'] as String?,
  sellerName: json['sellerName'] as String?,
  sellerPhone: json['sellerPhone'] as String?,
  assignedAt: json['assignedAt'] == null
      ? null
      : DateTime.parse(json['assignedAt'] as String),
  pickedUpAt: json['pickedUpAt'] == null
      ? null
      : DateTime.parse(json['pickedUpAt'] as String),
  deliveredAt: json['deliveredAt'] == null
      ? null
      : DateTime.parse(json['deliveredAt'] as String),
  estimatedDeliveryTime: json['estimatedDeliveryTime'] == null
      ? null
      : DateTime.parse(json['estimatedDeliveryTime'] as String),
  cancellationReason: json['cancellationReason'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  customerName: json['customerName'] as String?,
  customerPhone: json['customerPhone'] as String?,
  dropAddress: json['dropAddress'] as String?,
  dropLocation: _$JsonConverterFromJson<Map<String, dynamic>, GeoPoint>(
    json['dropLocation'],
    const GeoPointConverter().fromJson,
  ),
  acceptedAt: json['acceptedAt'] == null
      ? null
      : DateTime.parse(json['acceptedAt'] as String),
  itemCount: (json['itemCount'] as num?)?.toInt(),
  orderTotal: (json['orderTotal'] as num?)?.toDouble(),
  deliveryFee: (json['deliveryFee'] as num?)?.toDouble(),
  notes: json['notes'] as String?,
  deliveryInstructions: json['deliveryInstructions'] as String?,
);

Map<String, dynamic> _$$DeliveryRequestModelImplToJson(
  _$DeliveryRequestModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'orderId': instance.orderId,
  'buyerId': instance.buyerId,
  'sellerId': instance.sellerId,
  'status': _$DeliveryRequestStatusEnumMap[instance.status]!,
  'pickupLocation': const GeoPointConverter().toJson(instance.pickupLocation),
  'deliveryLocation': const GeoPointConverter().toJson(
    instance.deliveryLocation,
  ),
  'pickupAddress': instance.pickupAddress,
  'deliveryAddress': instance.deliveryAddress,
  'distance': instance.distance,
  'deliveryCharge': instance.deliveryCharge,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'deliveryPersonId': instance.deliveryPersonId,
  'buyerName': instance.buyerName,
  'buyerPhone': instance.buyerPhone,
  'sellerName': instance.sellerName,
  'sellerPhone': instance.sellerPhone,
  'assignedAt': instance.assignedAt?.toIso8601String(),
  'pickedUpAt': instance.pickedUpAt?.toIso8601String(),
  'deliveredAt': instance.deliveredAt?.toIso8601String(),
  'estimatedDeliveryTime': instance.estimatedDeliveryTime?.toIso8601String(),
  'cancellationReason': instance.cancellationReason,
  'metadata': instance.metadata,
  'customerName': instance.customerName,
  'customerPhone': instance.customerPhone,
  'dropAddress': instance.dropAddress,
  'dropLocation': _$JsonConverterToJson<Map<String, dynamic>, GeoPoint>(
    instance.dropLocation,
    const GeoPointConverter().toJson,
  ),
  'acceptedAt': instance.acceptedAt?.toIso8601String(),
  'itemCount': instance.itemCount,
  'orderTotal': instance.orderTotal,
  'deliveryFee': instance.deliveryFee,
  'notes': instance.notes,
  'deliveryInstructions': instance.deliveryInstructions,
};

const _$DeliveryRequestStatusEnumMap = {
  DeliveryRequestStatus.pending: 'pending',
  DeliveryRequestStatus.accepted: 'accepted',
  DeliveryRequestStatus.pickedUp: 'picked_up',
  DeliveryRequestStatus.inTransit: 'in_transit',
  DeliveryRequestStatus.delivered: 'delivered',
  DeliveryRequestStatus.cancelled: 'cancelled',
  DeliveryRequestStatus.failed: 'failed',
};

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) => json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) => value == null ? null : toJson(value);
