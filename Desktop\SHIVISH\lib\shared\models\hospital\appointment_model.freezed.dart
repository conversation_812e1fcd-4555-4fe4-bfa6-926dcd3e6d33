// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'appointment_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

AppointmentModel _$AppointmentModelFromJson(Map<String, dynamic> json) {
  return _AppointmentModel.fromJson(json);
}

/// @nodoc
mixin _$AppointmentModel {
  String get id => throw _privateConstructorUsedError;
  String get appointmentNumber => throw _privateConstructorUsedError;
  String get patientId => throw _privateConstructorUsedError;
  String get patientName => throw _privateConstructorUsedError;
  String get patientPhone => throw _privateConstructorUsedError;
  String? get patientEmail => throw _privateConstructorUsedError;
  String get hospitalId => throw _privateConstructorUsedError;
  String get hospitalName => throw _privateConstructorUsedError;
  String get doctorId => throw _privateConstructorUsedError;
  String get doctorName => throw _privateConstructorUsedError;
  String get departmentId => throw _privateConstructorUsedError;
  String get departmentName => throw _privateConstructorUsedError;
  DateTime get appointmentDate => throw _privateConstructorUsedError;
  String get timeSlotId => throw _privateConstructorUsedError;
  String get startTime => throw _privateConstructorUsedError;
  String get endTime => throw _privateConstructorUsedError;
  AppointmentType get type => throw _privateConstructorUsedError;
  AppointmentStatus get status => throw _privateConstructorUsedError;
  String get symptoms => throw _privateConstructorUsedError;
  String? get diagnosis => throw _privateConstructorUsedError;
  String? get prescription => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  double get consultationFee => throw _privateConstructorUsedError;
  bool get isPaid => throw _privateConstructorUsedError;
  String? get paymentId => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  DateTime? get deletedAt => throw _privateConstructorUsedError;

  /// Serializes this AppointmentModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppointmentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppointmentModelCopyWith<AppointmentModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppointmentModelCopyWith<$Res> {
  factory $AppointmentModelCopyWith(
    AppointmentModel value,
    $Res Function(AppointmentModel) then,
  ) = _$AppointmentModelCopyWithImpl<$Res, AppointmentModel>;
  @useResult
  $Res call({
    String id,
    String appointmentNumber,
    String patientId,
    String patientName,
    String patientPhone,
    String? patientEmail,
    String hospitalId,
    String hospitalName,
    String doctorId,
    String doctorName,
    String departmentId,
    String departmentName,
    DateTime appointmentDate,
    String timeSlotId,
    String startTime,
    String endTime,
    AppointmentType type,
    AppointmentStatus status,
    String symptoms,
    String? diagnosis,
    String? prescription,
    String? notes,
    double consultationFee,
    bool isPaid,
    String? paymentId,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });
}

/// @nodoc
class _$AppointmentModelCopyWithImpl<$Res, $Val extends AppointmentModel>
    implements $AppointmentModelCopyWith<$Res> {
  _$AppointmentModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppointmentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? appointmentNumber = null,
    Object? patientId = null,
    Object? patientName = null,
    Object? patientPhone = null,
    Object? patientEmail = freezed,
    Object? hospitalId = null,
    Object? hospitalName = null,
    Object? doctorId = null,
    Object? doctorName = null,
    Object? departmentId = null,
    Object? departmentName = null,
    Object? appointmentDate = null,
    Object? timeSlotId = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? type = null,
    Object? status = null,
    Object? symptoms = null,
    Object? diagnosis = freezed,
    Object? prescription = freezed,
    Object? notes = freezed,
    Object? consultationFee = null,
    Object? isPaid = null,
    Object? paymentId = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            appointmentNumber: null == appointmentNumber
                ? _value.appointmentNumber
                : appointmentNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            patientId: null == patientId
                ? _value.patientId
                : patientId // ignore: cast_nullable_to_non_nullable
                      as String,
            patientName: null == patientName
                ? _value.patientName
                : patientName // ignore: cast_nullable_to_non_nullable
                      as String,
            patientPhone: null == patientPhone
                ? _value.patientPhone
                : patientPhone // ignore: cast_nullable_to_non_nullable
                      as String,
            patientEmail: freezed == patientEmail
                ? _value.patientEmail
                : patientEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            hospitalId: null == hospitalId
                ? _value.hospitalId
                : hospitalId // ignore: cast_nullable_to_non_nullable
                      as String,
            hospitalName: null == hospitalName
                ? _value.hospitalName
                : hospitalName // ignore: cast_nullable_to_non_nullable
                      as String,
            doctorId: null == doctorId
                ? _value.doctorId
                : doctorId // ignore: cast_nullable_to_non_nullable
                      as String,
            doctorName: null == doctorName
                ? _value.doctorName
                : doctorName // ignore: cast_nullable_to_non_nullable
                      as String,
            departmentId: null == departmentId
                ? _value.departmentId
                : departmentId // ignore: cast_nullable_to_non_nullable
                      as String,
            departmentName: null == departmentName
                ? _value.departmentName
                : departmentName // ignore: cast_nullable_to_non_nullable
                      as String,
            appointmentDate: null == appointmentDate
                ? _value.appointmentDate
                : appointmentDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            timeSlotId: null == timeSlotId
                ? _value.timeSlotId
                : timeSlotId // ignore: cast_nullable_to_non_nullable
                      as String,
            startTime: null == startTime
                ? _value.startTime
                : startTime // ignore: cast_nullable_to_non_nullable
                      as String,
            endTime: null == endTime
                ? _value.endTime
                : endTime // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as AppointmentType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as AppointmentStatus,
            symptoms: null == symptoms
                ? _value.symptoms
                : symptoms // ignore: cast_nullable_to_non_nullable
                      as String,
            diagnosis: freezed == diagnosis
                ? _value.diagnosis
                : diagnosis // ignore: cast_nullable_to_non_nullable
                      as String?,
            prescription: freezed == prescription
                ? _value.prescription
                : prescription // ignore: cast_nullable_to_non_nullable
                      as String?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            consultationFee: null == consultationFee
                ? _value.consultationFee
                : consultationFee // ignore: cast_nullable_to_non_nullable
                      as double,
            isPaid: null == isPaid
                ? _value.isPaid
                : isPaid // ignore: cast_nullable_to_non_nullable
                      as bool,
            paymentId: freezed == paymentId
                ? _value.paymentId
                : paymentId // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            deletedAt: freezed == deletedAt
                ? _value.deletedAt
                : deletedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AppointmentModelImplCopyWith<$Res>
    implements $AppointmentModelCopyWith<$Res> {
  factory _$$AppointmentModelImplCopyWith(
    _$AppointmentModelImpl value,
    $Res Function(_$AppointmentModelImpl) then,
  ) = __$$AppointmentModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String appointmentNumber,
    String patientId,
    String patientName,
    String patientPhone,
    String? patientEmail,
    String hospitalId,
    String hospitalName,
    String doctorId,
    String doctorName,
    String departmentId,
    String departmentName,
    DateTime appointmentDate,
    String timeSlotId,
    String startTime,
    String endTime,
    AppointmentType type,
    AppointmentStatus status,
    String symptoms,
    String? diagnosis,
    String? prescription,
    String? notes,
    double consultationFee,
    bool isPaid,
    String? paymentId,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });
}

/// @nodoc
class __$$AppointmentModelImplCopyWithImpl<$Res>
    extends _$AppointmentModelCopyWithImpl<$Res, _$AppointmentModelImpl>
    implements _$$AppointmentModelImplCopyWith<$Res> {
  __$$AppointmentModelImplCopyWithImpl(
    _$AppointmentModelImpl _value,
    $Res Function(_$AppointmentModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AppointmentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? appointmentNumber = null,
    Object? patientId = null,
    Object? patientName = null,
    Object? patientPhone = null,
    Object? patientEmail = freezed,
    Object? hospitalId = null,
    Object? hospitalName = null,
    Object? doctorId = null,
    Object? doctorName = null,
    Object? departmentId = null,
    Object? departmentName = null,
    Object? appointmentDate = null,
    Object? timeSlotId = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? type = null,
    Object? status = null,
    Object? symptoms = null,
    Object? diagnosis = freezed,
    Object? prescription = freezed,
    Object? notes = freezed,
    Object? consultationFee = null,
    Object? isPaid = null,
    Object? paymentId = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _$AppointmentModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        appointmentNumber: null == appointmentNumber
            ? _value.appointmentNumber
            : appointmentNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        patientId: null == patientId
            ? _value.patientId
            : patientId // ignore: cast_nullable_to_non_nullable
                  as String,
        patientName: null == patientName
            ? _value.patientName
            : patientName // ignore: cast_nullable_to_non_nullable
                  as String,
        patientPhone: null == patientPhone
            ? _value.patientPhone
            : patientPhone // ignore: cast_nullable_to_non_nullable
                  as String,
        patientEmail: freezed == patientEmail
            ? _value.patientEmail
            : patientEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        hospitalId: null == hospitalId
            ? _value.hospitalId
            : hospitalId // ignore: cast_nullable_to_non_nullable
                  as String,
        hospitalName: null == hospitalName
            ? _value.hospitalName
            : hospitalName // ignore: cast_nullable_to_non_nullable
                  as String,
        doctorId: null == doctorId
            ? _value.doctorId
            : doctorId // ignore: cast_nullable_to_non_nullable
                  as String,
        doctorName: null == doctorName
            ? _value.doctorName
            : doctorName // ignore: cast_nullable_to_non_nullable
                  as String,
        departmentId: null == departmentId
            ? _value.departmentId
            : departmentId // ignore: cast_nullable_to_non_nullable
                  as String,
        departmentName: null == departmentName
            ? _value.departmentName
            : departmentName // ignore: cast_nullable_to_non_nullable
                  as String,
        appointmentDate: null == appointmentDate
            ? _value.appointmentDate
            : appointmentDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        timeSlotId: null == timeSlotId
            ? _value.timeSlotId
            : timeSlotId // ignore: cast_nullable_to_non_nullable
                  as String,
        startTime: null == startTime
            ? _value.startTime
            : startTime // ignore: cast_nullable_to_non_nullable
                  as String,
        endTime: null == endTime
            ? _value.endTime
            : endTime // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as AppointmentType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as AppointmentStatus,
        symptoms: null == symptoms
            ? _value.symptoms
            : symptoms // ignore: cast_nullable_to_non_nullable
                  as String,
        diagnosis: freezed == diagnosis
            ? _value.diagnosis
            : diagnosis // ignore: cast_nullable_to_non_nullable
                  as String?,
        prescription: freezed == prescription
            ? _value.prescription
            : prescription // ignore: cast_nullable_to_non_nullable
                  as String?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        consultationFee: null == consultationFee
            ? _value.consultationFee
            : consultationFee // ignore: cast_nullable_to_non_nullable
                  as double,
        isPaid: null == isPaid
            ? _value.isPaid
            : isPaid // ignore: cast_nullable_to_non_nullable
                  as bool,
        paymentId: freezed == paymentId
            ? _value.paymentId
            : paymentId // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        deletedAt: freezed == deletedAt
            ? _value.deletedAt
            : deletedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AppointmentModelImpl implements _AppointmentModel {
  const _$AppointmentModelImpl({
    required this.id,
    required this.appointmentNumber,
    required this.patientId,
    required this.patientName,
    required this.patientPhone,
    this.patientEmail,
    required this.hospitalId,
    required this.hospitalName,
    required this.doctorId,
    required this.doctorName,
    required this.departmentId,
    required this.departmentName,
    required this.appointmentDate,
    required this.timeSlotId,
    required this.startTime,
    required this.endTime,
    required this.type,
    required this.status,
    required this.symptoms,
    this.diagnosis,
    this.prescription,
    this.notes,
    required this.consultationFee,
    required this.isPaid,
    this.paymentId,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    this.deletedAt,
  });

  factory _$AppointmentModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppointmentModelImplFromJson(json);

  @override
  final String id;
  @override
  final String appointmentNumber;
  @override
  final String patientId;
  @override
  final String patientName;
  @override
  final String patientPhone;
  @override
  final String? patientEmail;
  @override
  final String hospitalId;
  @override
  final String hospitalName;
  @override
  final String doctorId;
  @override
  final String doctorName;
  @override
  final String departmentId;
  @override
  final String departmentName;
  @override
  final DateTime appointmentDate;
  @override
  final String timeSlotId;
  @override
  final String startTime;
  @override
  final String endTime;
  @override
  final AppointmentType type;
  @override
  final AppointmentStatus status;
  @override
  final String symptoms;
  @override
  final String? diagnosis;
  @override
  final String? prescription;
  @override
  final String? notes;
  @override
  final double consultationFee;
  @override
  final bool isPaid;
  @override
  final String? paymentId;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  final DateTime? deletedAt;

  @override
  String toString() {
    return 'AppointmentModel(id: $id, appointmentNumber: $appointmentNumber, patientId: $patientId, patientName: $patientName, patientPhone: $patientPhone, patientEmail: $patientEmail, hospitalId: $hospitalId, hospitalName: $hospitalName, doctorId: $doctorId, doctorName: $doctorName, departmentId: $departmentId, departmentName: $departmentName, appointmentDate: $appointmentDate, timeSlotId: $timeSlotId, startTime: $startTime, endTime: $endTime, type: $type, status: $status, symptoms: $symptoms, diagnosis: $diagnosis, prescription: $prescription, notes: $notes, consultationFee: $consultationFee, isPaid: $isPaid, paymentId: $paymentId, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppointmentModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.appointmentNumber, appointmentNumber) ||
                other.appointmentNumber == appointmentNumber) &&
            (identical(other.patientId, patientId) ||
                other.patientId == patientId) &&
            (identical(other.patientName, patientName) ||
                other.patientName == patientName) &&
            (identical(other.patientPhone, patientPhone) ||
                other.patientPhone == patientPhone) &&
            (identical(other.patientEmail, patientEmail) ||
                other.patientEmail == patientEmail) &&
            (identical(other.hospitalId, hospitalId) ||
                other.hospitalId == hospitalId) &&
            (identical(other.hospitalName, hospitalName) ||
                other.hospitalName == hospitalName) &&
            (identical(other.doctorId, doctorId) ||
                other.doctorId == doctorId) &&
            (identical(other.doctorName, doctorName) ||
                other.doctorName == doctorName) &&
            (identical(other.departmentId, departmentId) ||
                other.departmentId == departmentId) &&
            (identical(other.departmentName, departmentName) ||
                other.departmentName == departmentName) &&
            (identical(other.appointmentDate, appointmentDate) ||
                other.appointmentDate == appointmentDate) &&
            (identical(other.timeSlotId, timeSlotId) ||
                other.timeSlotId == timeSlotId) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.symptoms, symptoms) ||
                other.symptoms == symptoms) &&
            (identical(other.diagnosis, diagnosis) ||
                other.diagnosis == diagnosis) &&
            (identical(other.prescription, prescription) ||
                other.prescription == prescription) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.consultationFee, consultationFee) ||
                other.consultationFee == consultationFee) &&
            (identical(other.isPaid, isPaid) || other.isPaid == isPaid) &&
            (identical(other.paymentId, paymentId) ||
                other.paymentId == paymentId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    appointmentNumber,
    patientId,
    patientName,
    patientPhone,
    patientEmail,
    hospitalId,
    hospitalName,
    doctorId,
    doctorName,
    departmentId,
    departmentName,
    appointmentDate,
    timeSlotId,
    startTime,
    endTime,
    type,
    status,
    symptoms,
    diagnosis,
    prescription,
    notes,
    consultationFee,
    isPaid,
    paymentId,
    createdAt,
    updatedAt,
    isDeleted,
    deletedAt,
  ]);

  /// Create a copy of AppointmentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppointmentModelImplCopyWith<_$AppointmentModelImpl> get copyWith =>
      __$$AppointmentModelImplCopyWithImpl<_$AppointmentModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$AppointmentModelImplToJson(this);
  }
}

abstract class _AppointmentModel implements AppointmentModel {
  const factory _AppointmentModel({
    required final String id,
    required final String appointmentNumber,
    required final String patientId,
    required final String patientName,
    required final String patientPhone,
    final String? patientEmail,
    required final String hospitalId,
    required final String hospitalName,
    required final String doctorId,
    required final String doctorName,
    required final String departmentId,
    required final String departmentName,
    required final DateTime appointmentDate,
    required final String timeSlotId,
    required final String startTime,
    required final String endTime,
    required final AppointmentType type,
    required final AppointmentStatus status,
    required final String symptoms,
    final String? diagnosis,
    final String? prescription,
    final String? notes,
    required final double consultationFee,
    required final bool isPaid,
    final String? paymentId,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    final DateTime? deletedAt,
  }) = _$AppointmentModelImpl;

  factory _AppointmentModel.fromJson(Map<String, dynamic> json) =
      _$AppointmentModelImpl.fromJson;

  @override
  String get id;
  @override
  String get appointmentNumber;
  @override
  String get patientId;
  @override
  String get patientName;
  @override
  String get patientPhone;
  @override
  String? get patientEmail;
  @override
  String get hospitalId;
  @override
  String get hospitalName;
  @override
  String get doctorId;
  @override
  String get doctorName;
  @override
  String get departmentId;
  @override
  String get departmentName;
  @override
  DateTime get appointmentDate;
  @override
  String get timeSlotId;
  @override
  String get startTime;
  @override
  String get endTime;
  @override
  AppointmentType get type;
  @override
  AppointmentStatus get status;
  @override
  String get symptoms;
  @override
  String? get diagnosis;
  @override
  String? get prescription;
  @override
  String? get notes;
  @override
  double get consultationFee;
  @override
  bool get isPaid;
  @override
  String? get paymentId;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  DateTime? get deletedAt;

  /// Create a copy of AppointmentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppointmentModelImplCopyWith<_$AppointmentModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
