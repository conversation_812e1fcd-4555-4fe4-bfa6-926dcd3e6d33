// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AnalyticsDataImpl _$$AnalyticsDataImplFromJson(Map<String, dynamic> json) =>
    _$AnalyticsDataImpl(
      totalSales: (json['totalSales'] as num).toDouble(),
      totalOrders: (json['totalOrders'] as num).toInt(),
      averageOrderValue: (json['averageOrderValue'] as num).toDouble(),
      salesData: (json['salesData'] as List<dynamic>)
          .map((e) => SalesDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      topProducts: (json['topProducts'] as List<dynamic>)
          .map((e) => TopProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
      customerMetrics: CustomerMetrics.fromJson(
        json['customerMetrics'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$$AnalyticsDataImplToJson(_$AnalyticsDataImpl instance) =>
    <String, dynamic>{
      'totalSales': instance.totalSales,
      'totalOrders': instance.totalOrders,
      'averageOrderValue': instance.averageOrderValue,
      'salesData': instance.salesData.map((e) => e.toJson()).toList(),
      'topProducts': instance.topProducts.map((e) => e.toJson()).toList(),
      'customerMetrics': instance.customerMetrics.toJson(),
    };

_$SalesDataPointImpl _$$SalesDataPointImplFromJson(Map<String, dynamic> json) =>
    _$SalesDataPointImpl(
      date: json['date'] as String,
      amount: (json['amount'] as num).toDouble(),
    );

Map<String, dynamic> _$$SalesDataPointImplToJson(
  _$SalesDataPointImpl instance,
) => <String, dynamic>{'date': instance.date, 'amount': instance.amount};

_$TopProductImpl _$$TopProductImplFromJson(Map<String, dynamic> json) =>
    _$TopProductImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      imageUrl: json['imageUrl'] as String,
      quantity: (json['quantity'] as num).toInt(),
      revenue: (json['revenue'] as num).toDouble(),
    );

Map<String, dynamic> _$$TopProductImplToJson(_$TopProductImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'quantity': instance.quantity,
      'revenue': instance.revenue,
    };

_$CustomerMetricsImpl _$$CustomerMetricsImplFromJson(
  Map<String, dynamic> json,
) => _$CustomerMetricsImpl(
  newCustomers: (json['newCustomers'] as num).toInt(),
  repeatCustomers: (json['repeatCustomers'] as num).toInt(),
  customerRetentionRate: (json['customerRetentionRate'] as num).toDouble(),
  customerSatisfaction: (json['customerSatisfaction'] as num).toDouble(),
);

Map<String, dynamic> _$$CustomerMetricsImplToJson(
  _$CustomerMetricsImpl instance,
) => <String, dynamic>{
  'newCustomers': instance.newCustomers,
  'repeatCustomers': instance.repeatCustomers,
  'customerRetentionRate': instance.customerRetentionRate,
  'customerSatisfaction': instance.customerSatisfaction,
};
