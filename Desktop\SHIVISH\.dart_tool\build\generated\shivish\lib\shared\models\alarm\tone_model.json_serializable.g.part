// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ToneModelImpl _$$ToneModelImplFromJson(Map<String, dynamic> json) =>
    _$ToneModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      filePath: json['filePath'] as String,
      duration: (json['duration'] as num).toInt(),
      isDefault: json['isDefault'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      uploadedBy: json['uploadedBy'] as String,
      status:
          $enumDecodeNullable(_$ToneStatusEnumMap, json['status']) ??
          ToneStatus.pending,
      rejectionReason: json['rejectionReason'] as String?,
      approvedBy: json['approvedBy'] as String?,
      approvedAt: json['approvedAt'] == null
          ? null
          : DateTime.parse(json['approvedAt'] as String),
      language: json['language'] as String? ?? 'en',
      isMultiple: json['isMultiple'] as bool? ?? false,
      daysOfWeek:
          (json['daysOfWeek'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$ToneModelImplToJson(_$ToneModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'category': instance.category,
      'filePath': instance.filePath,
      'duration': instance.duration,
      'isDefault': instance.isDefault,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'uploadedBy': instance.uploadedBy,
      'status': _$ToneStatusEnumMap[instance.status]!,
      'rejectionReason': instance.rejectionReason,
      'approvedBy': instance.approvedBy,
      'approvedAt': instance.approvedAt?.toIso8601String(),
      'language': instance.language,
      'isMultiple': instance.isMultiple,
      'daysOfWeek': instance.daysOfWeek,
    };

const _$ToneStatusEnumMap = {
  ToneStatus.pending: 0,
  ToneStatus.approved: 1,
  ToneStatus.rejected: 2,
};
