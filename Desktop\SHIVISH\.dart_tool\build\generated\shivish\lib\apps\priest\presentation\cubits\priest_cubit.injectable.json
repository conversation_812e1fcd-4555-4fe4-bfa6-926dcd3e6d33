[{"type": {"import": "package:shivish/apps/priest/presentation/cubits/priest_cubit.dart", "name": "<PERSON><PERSON><PERSON><PERSON>", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/priest/presentation/cubits/priest_cubit.dart", "name": "<PERSON><PERSON><PERSON><PERSON>", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/apps/priest/domain/repositories/priest_repository.dart", "name": "PriestRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_priestRepository", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:shivish/shared/services/auth/auth_service.dart", "name": "AuthService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_authService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]