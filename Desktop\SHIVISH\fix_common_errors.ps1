# PowerShell script to fix common Flutter analysis errors
# Run this from the project root directory

Write-Host "Starting automated error fixes..." -ForegroundColor Green

# Get all Dart files
$dartFiles = Get-ChildItem -Path "lib" -Recurse -Filter "*.dart"
$totalFiles = $dartFiles.Count
$processedFiles = 0

Write-Host "Found $totalFiles Dart files to process..." -ForegroundColor Yellow

foreach ($file in $dartFiles) {
    $processedFiles++
    $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
    Write-Progress -Activity "Processing Dart files" -Status "Processing $relativePath" -PercentComplete (($processedFiles / $totalFiles) * 100)
    
    try {
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        $originalContent = $content
        $changesMade = $false
        
        # Fix 1: Replace Provider.of<CustomAuthProvider> calls
        if ($content -match 'Provider\.of<CustomAuthProvider>') {
            $content = $content -replace 'Provider\.of<CustomAuthProvider>\(context, listen: false\)', 'ref.read(auth_providers.customAuthServiceProvider)'
            $changesMade = $true
            Write-Host "  Fixed Provider.of<CustomAuthProvider> in $relativePath" -ForegroundColor Cyan
        }
        
        # Fix 2: Add missing import for auth_providers when CustomAuthProvider is used
        if ($content -match 'CustomAuthProvider' -and $content -notmatch "import 'package:shivish/shared/auth/providers/auth_providers.dart'") {
            # Add the import after existing imports
            $importPattern = "(import 'package:flutter[^']*';)"
            if ($content -match $importPattern) {
                $content = $content -replace $importPattern, "`$1`nimport 'package:shivish/shared/auth/providers/auth_providers.dart' as auth_providers;"
                $changesMade = $true
                Write-Host "  Added auth_providers import in $relativePath" -ForegroundColor Cyan
            }
        }
        
        # Fix 3: Replace provider import with riverpod when both are present
        if ($content -match "import 'package:provider/provider.dart';" -and $content -match "import 'package:flutter_riverpod/flutter_riverpod.dart';") {
            $content = $content -replace "import 'package:provider/provider.dart';", ""
            $changesMade = $true
            Write-Host "  Removed provider import (riverpod already present) in $relativePath" -ForegroundColor Cyan
        }
        
        # Fix 4: Convert StatelessWidget to ConsumerWidget when using ref
        if ($content -match 'class \w+ extends StatelessWidget' -and $content -match 'ref\.') {
            $content = $content -replace 'extends StatelessWidget', 'extends ConsumerWidget'
            $content = $content -replace 'Widget build\(BuildContext context\)', 'Widget build(BuildContext context, WidgetRef ref)'
            $changesMade = $true
            Write-Host "  Converted to ConsumerWidget in $relativePath" -ForegroundColor Cyan
        }
        
        # Fix 5: Convert StatefulWidget to ConsumerStatefulWidget when using ref
        if ($content -match 'class \w+ extends StatefulWidget' -and $content -match 'ref\.') {
            $content = $content -replace 'extends StatefulWidget', 'extends ConsumerStatefulWidget'
            $content = $content -replace 'extends State<(\w+)>', 'extends ConsumerState<$1>'
            $changesMade = $true
            Write-Host "  Converted to ConsumerStatefulWidget in $relativePath" -ForegroundColor Cyan
        }
        
        # Fix 6: Replace currentUserProvider ambiguous imports
        if ($content -match 'currentUserProvider' -and $content -notmatch 'auth_providers\.currentUserProvider') {
            $content = $content -replace '\bcurrentUserProvider\b', 'auth_providers.currentUserProvider'
            $changesMade = $true
            Write-Host "  Fixed currentUserProvider reference in $relativePath" -ForegroundColor Cyan
        }
        
        # Fix 7: Replace customAuthServiceProvider references
        if ($content -match 'customAuthServiceProvider' -and $content -notmatch 'auth_providers\.customAuthServiceProvider') {
            $content = $content -replace '\bcustomAuthServiceProvider\b', 'auth_providers.customAuthServiceProvider'
            $changesMade = $true
            Write-Host "  Fixed customAuthServiceProvider reference in $relativePath" -ForegroundColor Cyan
        }
        
        # Fix 8: Replace deprecated Share with SharePlus
        if ($content -match 'Share\.share') {
            $content = $content -replace 'Share\.share', 'SharePlus.instance.share'
            $content = $content -replace "import 'package:share/share.dart';", "import 'package:share_plus/share_plus.dart';"
            $changesMade = $true
            Write-Host "  Fixed deprecated Share usage in $relativePath" -ForegroundColor Cyan
        }
        
        # Fix 9: Replace withOpacity with withAlpha
        if ($content -match '\.withOpacity\(') {
            $content = $content -replace '\.withOpacity\(([^)]+)\)', '.withAlpha((255 * $1).round())'
            $changesMade = $true
            Write-Host "  Fixed deprecated withOpacity in $relativePath" -ForegroundColor Cyan
        }
        
        # Fix 10: Remove unused imports
        $unusedImports = @(
            "import 'package:shivish/shared/auth/services/auth_service.dart';",
            "import 'package:shivish/shared/auth/models/auth_models.dart';"
        )
        
        foreach ($import in $unusedImports) {
            if ($content -match [regex]::Escape($import) -and $content -notmatch 'CustomAuthService|AuthUser|LoginRequest|RegisterRequest') {
                $content = $content -replace [regex]::Escape($import), ""
                $changesMade = $true
                Write-Host "  Removed unused import in $relativePath" -ForegroundColor Cyan
            }
        }
        
        # Only write file if changes were made
        if ($changesMade) {
            Set-Content -Path $file.FullName -Value $content -ErrorAction Stop
            Write-Host "  ✓ Updated $relativePath" -ForegroundColor Green
        }

    } catch {
        Write-Host "  ✗ Error processing $relativePath : $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Progress -Activity "Processing Dart files" -Completed

Write-Host "`nAutomated fixes completed!" -ForegroundColor Green
Write-Host "Processed $processedFiles files" -ForegroundColor Yellow
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Run 'flutter analyze lib --no-pub' to check remaining issues" -ForegroundColor White
Write-Host "2. Run 'dart fix --apply' to apply additional automatic fixes" -ForegroundColor White
Write-Host "3. Review changes with 'git diff' before committing" -ForegroundColor White
