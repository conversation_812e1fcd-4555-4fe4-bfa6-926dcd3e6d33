[{"type": {"import": "package:shivish/apps/admin/bloc/technician/technician_bloc.dart", "name": "TechnicianBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/technician/technician_bloc.dart", "name": "TechnicianBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/technician_service.dart", "name": "TechnicianService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_technicianService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]