// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medicine_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

MedicineModel _$MedicineModelFromJson(Map<String, dynamic> json) {
  return _MedicineModel.fromJson(json);
}

/// @nodoc
mixin _$MedicineModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get hospitalId => throw _privateConstructorUsedError;
  MedicineType get type => throw _privateConstructorUsedError;
  MedicineCategory get category => throw _privateConstructorUsedError;
  String get manufacturer => throw _privateConstructorUsedError;
  String get composition => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  int get stockQuantity => throw _privateConstructorUsedError;
  String get dosageForm => throw _privateConstructorUsedError;
  String get strength => throw _privateConstructorUsedError;
  bool get requiresPrescription => throw _privateConstructorUsedError;
  bool get isAvailable => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String get batchNumber => throw _privateConstructorUsedError;
  DateTime get expiryDate => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  DateTime? get deletedAt => throw _privateConstructorUsedError;

  /// Serializes this MedicineModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MedicineModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MedicineModelCopyWith<MedicineModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedicineModelCopyWith<$Res> {
  factory $MedicineModelCopyWith(
    MedicineModel value,
    $Res Function(MedicineModel) then,
  ) = _$MedicineModelCopyWithImpl<$Res, MedicineModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    String hospitalId,
    MedicineType type,
    MedicineCategory category,
    String manufacturer,
    String composition,
    double price,
    int stockQuantity,
    String dosageForm,
    String strength,
    bool requiresPrescription,
    bool isAvailable,
    String? imageUrl,
    String batchNumber,
    DateTime expiryDate,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });
}

/// @nodoc
class _$MedicineModelCopyWithImpl<$Res, $Val extends MedicineModel>
    implements $MedicineModelCopyWith<$Res> {
  _$MedicineModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MedicineModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? hospitalId = null,
    Object? type = null,
    Object? category = null,
    Object? manufacturer = null,
    Object? composition = null,
    Object? price = null,
    Object? stockQuantity = null,
    Object? dosageForm = null,
    Object? strength = null,
    Object? requiresPrescription = null,
    Object? isAvailable = null,
    Object? imageUrl = freezed,
    Object? batchNumber = null,
    Object? expiryDate = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            hospitalId: null == hospitalId
                ? _value.hospitalId
                : hospitalId // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as MedicineType,
            category: null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                      as MedicineCategory,
            manufacturer: null == manufacturer
                ? _value.manufacturer
                : manufacturer // ignore: cast_nullable_to_non_nullable
                      as String,
            composition: null == composition
                ? _value.composition
                : composition // ignore: cast_nullable_to_non_nullable
                      as String,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            stockQuantity: null == stockQuantity
                ? _value.stockQuantity
                : stockQuantity // ignore: cast_nullable_to_non_nullable
                      as int,
            dosageForm: null == dosageForm
                ? _value.dosageForm
                : dosageForm // ignore: cast_nullable_to_non_nullable
                      as String,
            strength: null == strength
                ? _value.strength
                : strength // ignore: cast_nullable_to_non_nullable
                      as String,
            requiresPrescription: null == requiresPrescription
                ? _value.requiresPrescription
                : requiresPrescription // ignore: cast_nullable_to_non_nullable
                      as bool,
            isAvailable: null == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            batchNumber: null == batchNumber
                ? _value.batchNumber
                : batchNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            expiryDate: null == expiryDate
                ? _value.expiryDate
                : expiryDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            deletedAt: freezed == deletedAt
                ? _value.deletedAt
                : deletedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MedicineModelImplCopyWith<$Res>
    implements $MedicineModelCopyWith<$Res> {
  factory _$$MedicineModelImplCopyWith(
    _$MedicineModelImpl value,
    $Res Function(_$MedicineModelImpl) then,
  ) = __$$MedicineModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    String hospitalId,
    MedicineType type,
    MedicineCategory category,
    String manufacturer,
    String composition,
    double price,
    int stockQuantity,
    String dosageForm,
    String strength,
    bool requiresPrescription,
    bool isAvailable,
    String? imageUrl,
    String batchNumber,
    DateTime expiryDate,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });
}

/// @nodoc
class __$$MedicineModelImplCopyWithImpl<$Res>
    extends _$MedicineModelCopyWithImpl<$Res, _$MedicineModelImpl>
    implements _$$MedicineModelImplCopyWith<$Res> {
  __$$MedicineModelImplCopyWithImpl(
    _$MedicineModelImpl _value,
    $Res Function(_$MedicineModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MedicineModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? hospitalId = null,
    Object? type = null,
    Object? category = null,
    Object? manufacturer = null,
    Object? composition = null,
    Object? price = null,
    Object? stockQuantity = null,
    Object? dosageForm = null,
    Object? strength = null,
    Object? requiresPrescription = null,
    Object? isAvailable = null,
    Object? imageUrl = freezed,
    Object? batchNumber = null,
    Object? expiryDate = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _$MedicineModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        hospitalId: null == hospitalId
            ? _value.hospitalId
            : hospitalId // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as MedicineType,
        category: null == category
            ? _value.category
            : category // ignore: cast_nullable_to_non_nullable
                  as MedicineCategory,
        manufacturer: null == manufacturer
            ? _value.manufacturer
            : manufacturer // ignore: cast_nullable_to_non_nullable
                  as String,
        composition: null == composition
            ? _value.composition
            : composition // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        stockQuantity: null == stockQuantity
            ? _value.stockQuantity
            : stockQuantity // ignore: cast_nullable_to_non_nullable
                  as int,
        dosageForm: null == dosageForm
            ? _value.dosageForm
            : dosageForm // ignore: cast_nullable_to_non_nullable
                  as String,
        strength: null == strength
            ? _value.strength
            : strength // ignore: cast_nullable_to_non_nullable
                  as String,
        requiresPrescription: null == requiresPrescription
            ? _value.requiresPrescription
            : requiresPrescription // ignore: cast_nullable_to_non_nullable
                  as bool,
        isAvailable: null == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        batchNumber: null == batchNumber
            ? _value.batchNumber
            : batchNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        expiryDate: null == expiryDate
            ? _value.expiryDate
            : expiryDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        deletedAt: freezed == deletedAt
            ? _value.deletedAt
            : deletedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MedicineModelImpl implements _MedicineModel {
  const _$MedicineModelImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.hospitalId,
    required this.type,
    required this.category,
    required this.manufacturer,
    required this.composition,
    required this.price,
    required this.stockQuantity,
    required this.dosageForm,
    required this.strength,
    required this.requiresPrescription,
    required this.isAvailable,
    this.imageUrl,
    required this.batchNumber,
    required this.expiryDate,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    this.deletedAt,
  });

  factory _$MedicineModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MedicineModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final String hospitalId;
  @override
  final MedicineType type;
  @override
  final MedicineCategory category;
  @override
  final String manufacturer;
  @override
  final String composition;
  @override
  final double price;
  @override
  final int stockQuantity;
  @override
  final String dosageForm;
  @override
  final String strength;
  @override
  final bool requiresPrescription;
  @override
  final bool isAvailable;
  @override
  final String? imageUrl;
  @override
  final String batchNumber;
  @override
  final DateTime expiryDate;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  final DateTime? deletedAt;

  @override
  String toString() {
    return 'MedicineModel(id: $id, name: $name, description: $description, hospitalId: $hospitalId, type: $type, category: $category, manufacturer: $manufacturer, composition: $composition, price: $price, stockQuantity: $stockQuantity, dosageForm: $dosageForm, strength: $strength, requiresPrescription: $requiresPrescription, isAvailable: $isAvailable, imageUrl: $imageUrl, batchNumber: $batchNumber, expiryDate: $expiryDate, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MedicineModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.hospitalId, hospitalId) ||
                other.hospitalId == hospitalId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.manufacturer, manufacturer) ||
                other.manufacturer == manufacturer) &&
            (identical(other.composition, composition) ||
                other.composition == composition) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.stockQuantity, stockQuantity) ||
                other.stockQuantity == stockQuantity) &&
            (identical(other.dosageForm, dosageForm) ||
                other.dosageForm == dosageForm) &&
            (identical(other.strength, strength) ||
                other.strength == strength) &&
            (identical(other.requiresPrescription, requiresPrescription) ||
                other.requiresPrescription == requiresPrescription) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.batchNumber, batchNumber) ||
                other.batchNumber == batchNumber) &&
            (identical(other.expiryDate, expiryDate) ||
                other.expiryDate == expiryDate) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    description,
    hospitalId,
    type,
    category,
    manufacturer,
    composition,
    price,
    stockQuantity,
    dosageForm,
    strength,
    requiresPrescription,
    isAvailable,
    imageUrl,
    batchNumber,
    expiryDate,
    createdAt,
    updatedAt,
    isDeleted,
    deletedAt,
  ]);

  /// Create a copy of MedicineModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MedicineModelImplCopyWith<_$MedicineModelImpl> get copyWith =>
      __$$MedicineModelImplCopyWithImpl<_$MedicineModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MedicineModelImplToJson(this);
  }
}

abstract class _MedicineModel implements MedicineModel {
  const factory _MedicineModel({
    required final String id,
    required final String name,
    required final String description,
    required final String hospitalId,
    required final MedicineType type,
    required final MedicineCategory category,
    required final String manufacturer,
    required final String composition,
    required final double price,
    required final int stockQuantity,
    required final String dosageForm,
    required final String strength,
    required final bool requiresPrescription,
    required final bool isAvailable,
    final String? imageUrl,
    required final String batchNumber,
    required final DateTime expiryDate,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    final DateTime? deletedAt,
  }) = _$MedicineModelImpl;

  factory _MedicineModel.fromJson(Map<String, dynamic> json) =
      _$MedicineModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  String get hospitalId;
  @override
  MedicineType get type;
  @override
  MedicineCategory get category;
  @override
  String get manufacturer;
  @override
  String get composition;
  @override
  double get price;
  @override
  int get stockQuantity;
  @override
  String get dosageForm;
  @override
  String get strength;
  @override
  bool get requiresPrescription;
  @override
  bool get isAvailable;
  @override
  String? get imageUrl;
  @override
  String get batchNumber;
  @override
  DateTime get expiryDate;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  DateTime? get deletedAt;

  /// Create a copy of MedicineModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MedicineModelImplCopyWith<_$MedicineModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
