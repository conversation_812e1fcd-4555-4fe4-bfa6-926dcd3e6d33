// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bannerPricingNotifierHash() =>
    r'af91ed0c81171bf4dafe4ca61855093a72e099c4';

/// See also [BannerPricingNotifier].
@ProviderFor(BannerPricingNotifier)
final bannerPricingNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      BannerPricingNotifier,
      BannerPricing?
    >.internal(
      BannerPricingNotifier.new,
      name: r'bannerPricingNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bannerPricingNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BannerPricingNotifier = AutoDisposeAsyncNotifier<BannerPricing?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
