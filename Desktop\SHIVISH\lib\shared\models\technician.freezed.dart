// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'technician.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Technician _$TechnicianFromJson(Map<String, dynamic> json) {
  return _Technician.fromJson(json);
}

/// @nodoc
mixin _$Technician {
  String get id => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String? get profileImage => throw _privateConstructorUsedError;
  String? get qualificationProof => throw _privateConstructorUsedError;
  int get experienceYears => throw _privateConstructorUsedError;
  List<String> get specializations => throw _privateConstructorUsedError;
  List<String> get serviceAreas => throw _privateConstructorUsedError;
  Map<String, List<String>> get availabilitySchedule =>
      throw _privateConstructorUsedError;
  List<String> get certifications => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get totalReviews => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  String get verificationStatus => throw _privateConstructorUsedError;
  String? get verificationNotes => throw _privateConstructorUsedError;
  Map<String, int> get partsInventory => throw _privateConstructorUsedError;
  Map<String, double> get serviceRates => throw _privateConstructorUsedError;
  List<String> get toolsEquipment => throw _privateConstructorUsedError;
  Map<String, String> get insuranceInfo => throw _privateConstructorUsedError;
  Map<String, String> get emergencyContact =>
      throw _privateConstructorUsedError;
  Map<String, List<String>> get businessHours =>
      throw _privateConstructorUsedError;
  int get responseTime => throw _privateConstructorUsedError; // in minutes
  int get maxDistance => throw _privateConstructorUsedError; // in kilometers
  BankAccount? get bankAccount => throw _privateConstructorUsedError;

  /// Serializes this Technician to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Technician
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TechnicianCopyWith<Technician> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TechnicianCopyWith<$Res> {
  factory $TechnicianCopyWith(
    Technician value,
    $Res Function(Technician) then,
  ) = _$TechnicianCopyWithImpl<$Res, Technician>;
  @useResult
  $Res call({
    String id,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    String name,
    String email,
    String phone,
    String? profileImage,
    String? qualificationProof,
    int experienceYears,
    List<String> specializations,
    List<String> serviceAreas,
    Map<String, List<String>> availabilitySchedule,
    List<String> certifications,
    double rating,
    int totalReviews,
    bool isVerified,
    bool isActive,
    String verificationStatus,
    String? verificationNotes,
    Map<String, int> partsInventory,
    Map<String, double> serviceRates,
    List<String> toolsEquipment,
    Map<String, String> insuranceInfo,
    Map<String, String> emergencyContact,
    Map<String, List<String>> businessHours,
    int responseTime,
    int maxDistance,
    BankAccount? bankAccount,
  });

  $BankAccountCopyWith<$Res>? get bankAccount;
}

/// @nodoc
class _$TechnicianCopyWithImpl<$Res, $Val extends Technician>
    implements $TechnicianCopyWith<$Res> {
  _$TechnicianCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Technician
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? profileImage = freezed,
    Object? qualificationProof = freezed,
    Object? experienceYears = null,
    Object? specializations = null,
    Object? serviceAreas = null,
    Object? availabilitySchedule = null,
    Object? certifications = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? isVerified = null,
    Object? isActive = null,
    Object? verificationStatus = null,
    Object? verificationNotes = freezed,
    Object? partsInventory = null,
    Object? serviceRates = null,
    Object? toolsEquipment = null,
    Object? insuranceInfo = null,
    Object? emergencyContact = null,
    Object? businessHours = null,
    Object? responseTime = null,
    Object? maxDistance = null,
    Object? bankAccount = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            profileImage: freezed == profileImage
                ? _value.profileImage
                : profileImage // ignore: cast_nullable_to_non_nullable
                      as String?,
            qualificationProof: freezed == qualificationProof
                ? _value.qualificationProof
                : qualificationProof // ignore: cast_nullable_to_non_nullable
                      as String?,
            experienceYears: null == experienceYears
                ? _value.experienceYears
                : experienceYears // ignore: cast_nullable_to_non_nullable
                      as int,
            specializations: null == specializations
                ? _value.specializations
                : specializations // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            serviceAreas: null == serviceAreas
                ? _value.serviceAreas
                : serviceAreas // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            availabilitySchedule: null == availabilitySchedule
                ? _value.availabilitySchedule
                : availabilitySchedule // ignore: cast_nullable_to_non_nullable
                      as Map<String, List<String>>,
            certifications: null == certifications
                ? _value.certifications
                : certifications // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            totalReviews: null == totalReviews
                ? _value.totalReviews
                : totalReviews // ignore: cast_nullable_to_non_nullable
                      as int,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            verificationStatus: null == verificationStatus
                ? _value.verificationStatus
                : verificationStatus // ignore: cast_nullable_to_non_nullable
                      as String,
            verificationNotes: freezed == verificationNotes
                ? _value.verificationNotes
                : verificationNotes // ignore: cast_nullable_to_non_nullable
                      as String?,
            partsInventory: null == partsInventory
                ? _value.partsInventory
                : partsInventory // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            serviceRates: null == serviceRates
                ? _value.serviceRates
                : serviceRates // ignore: cast_nullable_to_non_nullable
                      as Map<String, double>,
            toolsEquipment: null == toolsEquipment
                ? _value.toolsEquipment
                : toolsEquipment // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            insuranceInfo: null == insuranceInfo
                ? _value.insuranceInfo
                : insuranceInfo // ignore: cast_nullable_to_non_nullable
                      as Map<String, String>,
            emergencyContact: null == emergencyContact
                ? _value.emergencyContact
                : emergencyContact // ignore: cast_nullable_to_non_nullable
                      as Map<String, String>,
            businessHours: null == businessHours
                ? _value.businessHours
                : businessHours // ignore: cast_nullable_to_non_nullable
                      as Map<String, List<String>>,
            responseTime: null == responseTime
                ? _value.responseTime
                : responseTime // ignore: cast_nullable_to_non_nullable
                      as int,
            maxDistance: null == maxDistance
                ? _value.maxDistance
                : maxDistance // ignore: cast_nullable_to_non_nullable
                      as int,
            bankAccount: freezed == bankAccount
                ? _value.bankAccount
                : bankAccount // ignore: cast_nullable_to_non_nullable
                      as BankAccount?,
          )
          as $Val,
    );
  }

  /// Create a copy of Technician
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BankAccountCopyWith<$Res>? get bankAccount {
    if (_value.bankAccount == null) {
      return null;
    }

    return $BankAccountCopyWith<$Res>(_value.bankAccount!, (value) {
      return _then(_value.copyWith(bankAccount: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TechnicianImplCopyWith<$Res>
    implements $TechnicianCopyWith<$Res> {
  factory _$$TechnicianImplCopyWith(
    _$TechnicianImpl value,
    $Res Function(_$TechnicianImpl) then,
  ) = __$$TechnicianImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    String name,
    String email,
    String phone,
    String? profileImage,
    String? qualificationProof,
    int experienceYears,
    List<String> specializations,
    List<String> serviceAreas,
    Map<String, List<String>> availabilitySchedule,
    List<String> certifications,
    double rating,
    int totalReviews,
    bool isVerified,
    bool isActive,
    String verificationStatus,
    String? verificationNotes,
    Map<String, int> partsInventory,
    Map<String, double> serviceRates,
    List<String> toolsEquipment,
    Map<String, String> insuranceInfo,
    Map<String, String> emergencyContact,
    Map<String, List<String>> businessHours,
    int responseTime,
    int maxDistance,
    BankAccount? bankAccount,
  });

  @override
  $BankAccountCopyWith<$Res>? get bankAccount;
}

/// @nodoc
class __$$TechnicianImplCopyWithImpl<$Res>
    extends _$TechnicianCopyWithImpl<$Res, _$TechnicianImpl>
    implements _$$TechnicianImplCopyWith<$Res> {
  __$$TechnicianImplCopyWithImpl(
    _$TechnicianImpl _value,
    $Res Function(_$TechnicianImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Technician
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? profileImage = freezed,
    Object? qualificationProof = freezed,
    Object? experienceYears = null,
    Object? specializations = null,
    Object? serviceAreas = null,
    Object? availabilitySchedule = null,
    Object? certifications = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? isVerified = null,
    Object? isActive = null,
    Object? verificationStatus = null,
    Object? verificationNotes = freezed,
    Object? partsInventory = null,
    Object? serviceRates = null,
    Object? toolsEquipment = null,
    Object? insuranceInfo = null,
    Object? emergencyContact = null,
    Object? businessHours = null,
    Object? responseTime = null,
    Object? maxDistance = null,
    Object? bankAccount = freezed,
  }) {
    return _then(
      _$TechnicianImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        profileImage: freezed == profileImage
            ? _value.profileImage
            : profileImage // ignore: cast_nullable_to_non_nullable
                  as String?,
        qualificationProof: freezed == qualificationProof
            ? _value.qualificationProof
            : qualificationProof // ignore: cast_nullable_to_non_nullable
                  as String?,
        experienceYears: null == experienceYears
            ? _value.experienceYears
            : experienceYears // ignore: cast_nullable_to_non_nullable
                  as int,
        specializations: null == specializations
            ? _value._specializations
            : specializations // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        serviceAreas: null == serviceAreas
            ? _value._serviceAreas
            : serviceAreas // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        availabilitySchedule: null == availabilitySchedule
            ? _value._availabilitySchedule
            : availabilitySchedule // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>,
        certifications: null == certifications
            ? _value._certifications
            : certifications // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        totalReviews: null == totalReviews
            ? _value.totalReviews
            : totalReviews // ignore: cast_nullable_to_non_nullable
                  as int,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        verificationStatus: null == verificationStatus
            ? _value.verificationStatus
            : verificationStatus // ignore: cast_nullable_to_non_nullable
                  as String,
        verificationNotes: freezed == verificationNotes
            ? _value.verificationNotes
            : verificationNotes // ignore: cast_nullable_to_non_nullable
                  as String?,
        partsInventory: null == partsInventory
            ? _value._partsInventory
            : partsInventory // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        serviceRates: null == serviceRates
            ? _value._serviceRates
            : serviceRates // ignore: cast_nullable_to_non_nullable
                  as Map<String, double>,
        toolsEquipment: null == toolsEquipment
            ? _value._toolsEquipment
            : toolsEquipment // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        insuranceInfo: null == insuranceInfo
            ? _value._insuranceInfo
            : insuranceInfo // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>,
        emergencyContact: null == emergencyContact
            ? _value._emergencyContact
            : emergencyContact // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>,
        businessHours: null == businessHours
            ? _value._businessHours
            : businessHours // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>,
        responseTime: null == responseTime
            ? _value.responseTime
            : responseTime // ignore: cast_nullable_to_non_nullable
                  as int,
        maxDistance: null == maxDistance
            ? _value.maxDistance
            : maxDistance // ignore: cast_nullable_to_non_nullable
                  as int,
        bankAccount: freezed == bankAccount
            ? _value.bankAccount
            : bankAccount // ignore: cast_nullable_to_non_nullable
                  as BankAccount?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TechnicianImpl extends _Technician {
  const _$TechnicianImpl({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    required this.name,
    required this.email,
    required this.phone,
    this.profileImage,
    this.qualificationProof,
    required this.experienceYears,
    required final List<String> specializations,
    required final List<String> serviceAreas,
    required final Map<String, List<String>> availabilitySchedule,
    required final List<String> certifications,
    this.rating = 0.0,
    this.totalReviews = 0,
    this.isVerified = false,
    this.isActive = true,
    this.verificationStatus = 'pending',
    this.verificationNotes,
    required final Map<String, int> partsInventory,
    required final Map<String, double> serviceRates,
    required final List<String> toolsEquipment,
    required final Map<String, String> insuranceInfo,
    required final Map<String, String> emergencyContact,
    required final Map<String, List<String>> businessHours,
    this.responseTime = 60,
    this.maxDistance = 50,
    this.bankAccount,
  }) : _specializations = specializations,
       _serviceAreas = serviceAreas,
       _availabilitySchedule = availabilitySchedule,
       _certifications = certifications,
       _partsInventory = partsInventory,
       _serviceRates = serviceRates,
       _toolsEquipment = toolsEquipment,
       _insuranceInfo = insuranceInfo,
       _emergencyContact = emergencyContact,
       _businessHours = businessHours,
       super._();

  factory _$TechnicianImpl.fromJson(Map<String, dynamic> json) =>
      _$$TechnicianImplFromJson(json);

  @override
  final String id;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  final String name;
  @override
  final String email;
  @override
  final String phone;
  @override
  final String? profileImage;
  @override
  final String? qualificationProof;
  @override
  final int experienceYears;
  final List<String> _specializations;
  @override
  List<String> get specializations {
    if (_specializations is EqualUnmodifiableListView) return _specializations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_specializations);
  }

  final List<String> _serviceAreas;
  @override
  List<String> get serviceAreas {
    if (_serviceAreas is EqualUnmodifiableListView) return _serviceAreas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_serviceAreas);
  }

  final Map<String, List<String>> _availabilitySchedule;
  @override
  Map<String, List<String>> get availabilitySchedule {
    if (_availabilitySchedule is EqualUnmodifiableMapView)
      return _availabilitySchedule;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_availabilitySchedule);
  }

  final List<String> _certifications;
  @override
  List<String> get certifications {
    if (_certifications is EqualUnmodifiableListView) return _certifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_certifications);
  }

  @override
  @JsonKey()
  final double rating;
  @override
  @JsonKey()
  final int totalReviews;
  @override
  @JsonKey()
  final bool isVerified;
  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final String verificationStatus;
  @override
  final String? verificationNotes;
  final Map<String, int> _partsInventory;
  @override
  Map<String, int> get partsInventory {
    if (_partsInventory is EqualUnmodifiableMapView) return _partsInventory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_partsInventory);
  }

  final Map<String, double> _serviceRates;
  @override
  Map<String, double> get serviceRates {
    if (_serviceRates is EqualUnmodifiableMapView) return _serviceRates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_serviceRates);
  }

  final List<String> _toolsEquipment;
  @override
  List<String> get toolsEquipment {
    if (_toolsEquipment is EqualUnmodifiableListView) return _toolsEquipment;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_toolsEquipment);
  }

  final Map<String, String> _insuranceInfo;
  @override
  Map<String, String> get insuranceInfo {
    if (_insuranceInfo is EqualUnmodifiableMapView) return _insuranceInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_insuranceInfo);
  }

  final Map<String, String> _emergencyContact;
  @override
  Map<String, String> get emergencyContact {
    if (_emergencyContact is EqualUnmodifiableMapView) return _emergencyContact;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_emergencyContact);
  }

  final Map<String, List<String>> _businessHours;
  @override
  Map<String, List<String>> get businessHours {
    if (_businessHours is EqualUnmodifiableMapView) return _businessHours;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_businessHours);
  }

  @override
  @JsonKey()
  final int responseTime;
  // in minutes
  @override
  @JsonKey()
  final int maxDistance;
  // in kilometers
  @override
  final BankAccount? bankAccount;

  @override
  String toString() {
    return 'Technician(id: $id, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, name: $name, email: $email, phone: $phone, profileImage: $profileImage, qualificationProof: $qualificationProof, experienceYears: $experienceYears, specializations: $specializations, serviceAreas: $serviceAreas, availabilitySchedule: $availabilitySchedule, certifications: $certifications, rating: $rating, totalReviews: $totalReviews, isVerified: $isVerified, isActive: $isActive, verificationStatus: $verificationStatus, verificationNotes: $verificationNotes, partsInventory: $partsInventory, serviceRates: $serviceRates, toolsEquipment: $toolsEquipment, insuranceInfo: $insuranceInfo, emergencyContact: $emergencyContact, businessHours: $businessHours, responseTime: $responseTime, maxDistance: $maxDistance, bankAccount: $bankAccount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TechnicianImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.qualificationProof, qualificationProof) ||
                other.qualificationProof == qualificationProof) &&
            (identical(other.experienceYears, experienceYears) ||
                other.experienceYears == experienceYears) &&
            const DeepCollectionEquality().equals(
              other._specializations,
              _specializations,
            ) &&
            const DeepCollectionEquality().equals(
              other._serviceAreas,
              _serviceAreas,
            ) &&
            const DeepCollectionEquality().equals(
              other._availabilitySchedule,
              _availabilitySchedule,
            ) &&
            const DeepCollectionEquality().equals(
              other._certifications,
              _certifications,
            ) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalReviews, totalReviews) ||
                other.totalReviews == totalReviews) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.verificationStatus, verificationStatus) ||
                other.verificationStatus == verificationStatus) &&
            (identical(other.verificationNotes, verificationNotes) ||
                other.verificationNotes == verificationNotes) &&
            const DeepCollectionEquality().equals(
              other._partsInventory,
              _partsInventory,
            ) &&
            const DeepCollectionEquality().equals(
              other._serviceRates,
              _serviceRates,
            ) &&
            const DeepCollectionEquality().equals(
              other._toolsEquipment,
              _toolsEquipment,
            ) &&
            const DeepCollectionEquality().equals(
              other._insuranceInfo,
              _insuranceInfo,
            ) &&
            const DeepCollectionEquality().equals(
              other._emergencyContact,
              _emergencyContact,
            ) &&
            const DeepCollectionEquality().equals(
              other._businessHours,
              _businessHours,
            ) &&
            (identical(other.responseTime, responseTime) ||
                other.responseTime == responseTime) &&
            (identical(other.maxDistance, maxDistance) ||
                other.maxDistance == maxDistance) &&
            (identical(other.bankAccount, bankAccount) ||
                other.bankAccount == bankAccount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    createdAt,
    updatedAt,
    isDeleted,
    name,
    email,
    phone,
    profileImage,
    qualificationProof,
    experienceYears,
    const DeepCollectionEquality().hash(_specializations),
    const DeepCollectionEquality().hash(_serviceAreas),
    const DeepCollectionEquality().hash(_availabilitySchedule),
    const DeepCollectionEquality().hash(_certifications),
    rating,
    totalReviews,
    isVerified,
    isActive,
    verificationStatus,
    verificationNotes,
    const DeepCollectionEquality().hash(_partsInventory),
    const DeepCollectionEquality().hash(_serviceRates),
    const DeepCollectionEquality().hash(_toolsEquipment),
    const DeepCollectionEquality().hash(_insuranceInfo),
    const DeepCollectionEquality().hash(_emergencyContact),
    const DeepCollectionEquality().hash(_businessHours),
    responseTime,
    maxDistance,
    bankAccount,
  ]);

  /// Create a copy of Technician
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TechnicianImplCopyWith<_$TechnicianImpl> get copyWith =>
      __$$TechnicianImplCopyWithImpl<_$TechnicianImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TechnicianImplToJson(this);
  }
}

abstract class _Technician extends Technician {
  const factory _Technician({
    required final String id,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    required final String name,
    required final String email,
    required final String phone,
    final String? profileImage,
    final String? qualificationProof,
    required final int experienceYears,
    required final List<String> specializations,
    required final List<String> serviceAreas,
    required final Map<String, List<String>> availabilitySchedule,
    required final List<String> certifications,
    final double rating,
    final int totalReviews,
    final bool isVerified,
    final bool isActive,
    final String verificationStatus,
    final String? verificationNotes,
    required final Map<String, int> partsInventory,
    required final Map<String, double> serviceRates,
    required final List<String> toolsEquipment,
    required final Map<String, String> insuranceInfo,
    required final Map<String, String> emergencyContact,
    required final Map<String, List<String>> businessHours,
    final int responseTime,
    final int maxDistance,
    final BankAccount? bankAccount,
  }) = _$TechnicianImpl;
  const _Technician._() : super._();

  factory _Technician.fromJson(Map<String, dynamic> json) =
      _$TechnicianImpl.fromJson;

  @override
  String get id;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  String get name;
  @override
  String get email;
  @override
  String get phone;
  @override
  String? get profileImage;
  @override
  String? get qualificationProof;
  @override
  int get experienceYears;
  @override
  List<String> get specializations;
  @override
  List<String> get serviceAreas;
  @override
  Map<String, List<String>> get availabilitySchedule;
  @override
  List<String> get certifications;
  @override
  double get rating;
  @override
  int get totalReviews;
  @override
  bool get isVerified;
  @override
  bool get isActive;
  @override
  String get verificationStatus;
  @override
  String? get verificationNotes;
  @override
  Map<String, int> get partsInventory;
  @override
  Map<String, double> get serviceRates;
  @override
  List<String> get toolsEquipment;
  @override
  Map<String, String> get insuranceInfo;
  @override
  Map<String, String> get emergencyContact;
  @override
  Map<String, List<String>> get businessHours;
  @override
  int get responseTime; // in minutes
  @override
  int get maxDistance; // in kilometers
  @override
  BankAccount? get bankAccount;

  /// Create a copy of Technician
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TechnicianImplCopyWith<_$TechnicianImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
