// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'panchangam_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PanchangamModel _$PanchangamModelFromJson(Map<String, dynamic> json) {
  return _PanchangamModel.fromJson(json);
}

/// @nodoc
mixin _$PanchangamModel {
  DateTime get date => throw _privateConstructorUsedError;
  bool get isAuspicious => throw _privateConstructorUsedError;
  String get tithi => throw _privateConstructorUsedError;
  String get nakshatra => throw _privateConstructorUsedError;
  String get yoga => throw _privateConstructorUsedError;
  String get karana => throw _privateConstructorUsedError;
  String get sunrise => throw _privateConstructorUsedError;
  String get sunset => throw _privateConstructorUsedError;
  String get moonrise => throw _privateConstructorUsedError;
  String get moonset => throw _privateConstructorUsedError;
  String get rahuKalam => throw _privateConstructorUsedError;
  String get gulikaKalam => throw _privateConstructorUsedError;
  String get yamagandam => throw _privateConstructorUsedError;
  String get abhijitMuhurta => throw _privateConstructorUsedError;
  List<String> get festivals => throw _privateConstructorUsedError;
  List<String> get vratas => throw _privateConstructorUsedError;
  List<String> get specialEvents => throw _privateConstructorUsedError;

  /// Serializes this PanchangamModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PanchangamModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PanchangamModelCopyWith<PanchangamModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PanchangamModelCopyWith<$Res> {
  factory $PanchangamModelCopyWith(
    PanchangamModel value,
    $Res Function(PanchangamModel) then,
  ) = _$PanchangamModelCopyWithImpl<$Res, PanchangamModel>;
  @useResult
  $Res call({
    DateTime date,
    bool isAuspicious,
    String tithi,
    String nakshatra,
    String yoga,
    String karana,
    String sunrise,
    String sunset,
    String moonrise,
    String moonset,
    String rahuKalam,
    String gulikaKalam,
    String yamagandam,
    String abhijitMuhurta,
    List<String> festivals,
    List<String> vratas,
    List<String> specialEvents,
  });
}

/// @nodoc
class _$PanchangamModelCopyWithImpl<$Res, $Val extends PanchangamModel>
    implements $PanchangamModelCopyWith<$Res> {
  _$PanchangamModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PanchangamModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? isAuspicious = null,
    Object? tithi = null,
    Object? nakshatra = null,
    Object? yoga = null,
    Object? karana = null,
    Object? sunrise = null,
    Object? sunset = null,
    Object? moonrise = null,
    Object? moonset = null,
    Object? rahuKalam = null,
    Object? gulikaKalam = null,
    Object? yamagandam = null,
    Object? abhijitMuhurta = null,
    Object? festivals = null,
    Object? vratas = null,
    Object? specialEvents = null,
  }) {
    return _then(
      _value.copyWith(
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isAuspicious: null == isAuspicious
                ? _value.isAuspicious
                : isAuspicious // ignore: cast_nullable_to_non_nullable
                      as bool,
            tithi: null == tithi
                ? _value.tithi
                : tithi // ignore: cast_nullable_to_non_nullable
                      as String,
            nakshatra: null == nakshatra
                ? _value.nakshatra
                : nakshatra // ignore: cast_nullable_to_non_nullable
                      as String,
            yoga: null == yoga
                ? _value.yoga
                : yoga // ignore: cast_nullable_to_non_nullable
                      as String,
            karana: null == karana
                ? _value.karana
                : karana // ignore: cast_nullable_to_non_nullable
                      as String,
            sunrise: null == sunrise
                ? _value.sunrise
                : sunrise // ignore: cast_nullable_to_non_nullable
                      as String,
            sunset: null == sunset
                ? _value.sunset
                : sunset // ignore: cast_nullable_to_non_nullable
                      as String,
            moonrise: null == moonrise
                ? _value.moonrise
                : moonrise // ignore: cast_nullable_to_non_nullable
                      as String,
            moonset: null == moonset
                ? _value.moonset
                : moonset // ignore: cast_nullable_to_non_nullable
                      as String,
            rahuKalam: null == rahuKalam
                ? _value.rahuKalam
                : rahuKalam // ignore: cast_nullable_to_non_nullable
                      as String,
            gulikaKalam: null == gulikaKalam
                ? _value.gulikaKalam
                : gulikaKalam // ignore: cast_nullable_to_non_nullable
                      as String,
            yamagandam: null == yamagandam
                ? _value.yamagandam
                : yamagandam // ignore: cast_nullable_to_non_nullable
                      as String,
            abhijitMuhurta: null == abhijitMuhurta
                ? _value.abhijitMuhurta
                : abhijitMuhurta // ignore: cast_nullable_to_non_nullable
                      as String,
            festivals: null == festivals
                ? _value.festivals
                : festivals // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            vratas: null == vratas
                ? _value.vratas
                : vratas // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            specialEvents: null == specialEvents
                ? _value.specialEvents
                : specialEvents // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PanchangamModelImplCopyWith<$Res>
    implements $PanchangamModelCopyWith<$Res> {
  factory _$$PanchangamModelImplCopyWith(
    _$PanchangamModelImpl value,
    $Res Function(_$PanchangamModelImpl) then,
  ) = __$$PanchangamModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    DateTime date,
    bool isAuspicious,
    String tithi,
    String nakshatra,
    String yoga,
    String karana,
    String sunrise,
    String sunset,
    String moonrise,
    String moonset,
    String rahuKalam,
    String gulikaKalam,
    String yamagandam,
    String abhijitMuhurta,
    List<String> festivals,
    List<String> vratas,
    List<String> specialEvents,
  });
}

/// @nodoc
class __$$PanchangamModelImplCopyWithImpl<$Res>
    extends _$PanchangamModelCopyWithImpl<$Res, _$PanchangamModelImpl>
    implements _$$PanchangamModelImplCopyWith<$Res> {
  __$$PanchangamModelImplCopyWithImpl(
    _$PanchangamModelImpl _value,
    $Res Function(_$PanchangamModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PanchangamModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? isAuspicious = null,
    Object? tithi = null,
    Object? nakshatra = null,
    Object? yoga = null,
    Object? karana = null,
    Object? sunrise = null,
    Object? sunset = null,
    Object? moonrise = null,
    Object? moonset = null,
    Object? rahuKalam = null,
    Object? gulikaKalam = null,
    Object? yamagandam = null,
    Object? abhijitMuhurta = null,
    Object? festivals = null,
    Object? vratas = null,
    Object? specialEvents = null,
  }) {
    return _then(
      _$PanchangamModelImpl(
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isAuspicious: null == isAuspicious
            ? _value.isAuspicious
            : isAuspicious // ignore: cast_nullable_to_non_nullable
                  as bool,
        tithi: null == tithi
            ? _value.tithi
            : tithi // ignore: cast_nullable_to_non_nullable
                  as String,
        nakshatra: null == nakshatra
            ? _value.nakshatra
            : nakshatra // ignore: cast_nullable_to_non_nullable
                  as String,
        yoga: null == yoga
            ? _value.yoga
            : yoga // ignore: cast_nullable_to_non_nullable
                  as String,
        karana: null == karana
            ? _value.karana
            : karana // ignore: cast_nullable_to_non_nullable
                  as String,
        sunrise: null == sunrise
            ? _value.sunrise
            : sunrise // ignore: cast_nullable_to_non_nullable
                  as String,
        sunset: null == sunset
            ? _value.sunset
            : sunset // ignore: cast_nullable_to_non_nullable
                  as String,
        moonrise: null == moonrise
            ? _value.moonrise
            : moonrise // ignore: cast_nullable_to_non_nullable
                  as String,
        moonset: null == moonset
            ? _value.moonset
            : moonset // ignore: cast_nullable_to_non_nullable
                  as String,
        rahuKalam: null == rahuKalam
            ? _value.rahuKalam
            : rahuKalam // ignore: cast_nullable_to_non_nullable
                  as String,
        gulikaKalam: null == gulikaKalam
            ? _value.gulikaKalam
            : gulikaKalam // ignore: cast_nullable_to_non_nullable
                  as String,
        yamagandam: null == yamagandam
            ? _value.yamagandam
            : yamagandam // ignore: cast_nullable_to_non_nullable
                  as String,
        abhijitMuhurta: null == abhijitMuhurta
            ? _value.abhijitMuhurta
            : abhijitMuhurta // ignore: cast_nullable_to_non_nullable
                  as String,
        festivals: null == festivals
            ? _value._festivals
            : festivals // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        vratas: null == vratas
            ? _value._vratas
            : vratas // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        specialEvents: null == specialEvents
            ? _value._specialEvents
            : specialEvents // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PanchangamModelImpl implements _PanchangamModel {
  const _$PanchangamModelImpl({
    required this.date,
    required this.isAuspicious,
    required this.tithi,
    required this.nakshatra,
    required this.yoga,
    required this.karana,
    required this.sunrise,
    required this.sunset,
    required this.moonrise,
    required this.moonset,
    required this.rahuKalam,
    required this.gulikaKalam,
    required this.yamagandam,
    required this.abhijitMuhurta,
    final List<String> festivals = const [],
    final List<String> vratas = const [],
    final List<String> specialEvents = const [],
  }) : _festivals = festivals,
       _vratas = vratas,
       _specialEvents = specialEvents;

  factory _$PanchangamModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PanchangamModelImplFromJson(json);

  @override
  final DateTime date;
  @override
  final bool isAuspicious;
  @override
  final String tithi;
  @override
  final String nakshatra;
  @override
  final String yoga;
  @override
  final String karana;
  @override
  final String sunrise;
  @override
  final String sunset;
  @override
  final String moonrise;
  @override
  final String moonset;
  @override
  final String rahuKalam;
  @override
  final String gulikaKalam;
  @override
  final String yamagandam;
  @override
  final String abhijitMuhurta;
  final List<String> _festivals;
  @override
  @JsonKey()
  List<String> get festivals {
    if (_festivals is EqualUnmodifiableListView) return _festivals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_festivals);
  }

  final List<String> _vratas;
  @override
  @JsonKey()
  List<String> get vratas {
    if (_vratas is EqualUnmodifiableListView) return _vratas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vratas);
  }

  final List<String> _specialEvents;
  @override
  @JsonKey()
  List<String> get specialEvents {
    if (_specialEvents is EqualUnmodifiableListView) return _specialEvents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_specialEvents);
  }

  @override
  String toString() {
    return 'PanchangamModel(date: $date, isAuspicious: $isAuspicious, tithi: $tithi, nakshatra: $nakshatra, yoga: $yoga, karana: $karana, sunrise: $sunrise, sunset: $sunset, moonrise: $moonrise, moonset: $moonset, rahuKalam: $rahuKalam, gulikaKalam: $gulikaKalam, yamagandam: $yamagandam, abhijitMuhurta: $abhijitMuhurta, festivals: $festivals, vratas: $vratas, specialEvents: $specialEvents)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PanchangamModelImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.isAuspicious, isAuspicious) ||
                other.isAuspicious == isAuspicious) &&
            (identical(other.tithi, tithi) || other.tithi == tithi) &&
            (identical(other.nakshatra, nakshatra) ||
                other.nakshatra == nakshatra) &&
            (identical(other.yoga, yoga) || other.yoga == yoga) &&
            (identical(other.karana, karana) || other.karana == karana) &&
            (identical(other.sunrise, sunrise) || other.sunrise == sunrise) &&
            (identical(other.sunset, sunset) || other.sunset == sunset) &&
            (identical(other.moonrise, moonrise) ||
                other.moonrise == moonrise) &&
            (identical(other.moonset, moonset) || other.moonset == moonset) &&
            (identical(other.rahuKalam, rahuKalam) ||
                other.rahuKalam == rahuKalam) &&
            (identical(other.gulikaKalam, gulikaKalam) ||
                other.gulikaKalam == gulikaKalam) &&
            (identical(other.yamagandam, yamagandam) ||
                other.yamagandam == yamagandam) &&
            (identical(other.abhijitMuhurta, abhijitMuhurta) ||
                other.abhijitMuhurta == abhijitMuhurta) &&
            const DeepCollectionEquality().equals(
              other._festivals,
              _festivals,
            ) &&
            const DeepCollectionEquality().equals(other._vratas, _vratas) &&
            const DeepCollectionEquality().equals(
              other._specialEvents,
              _specialEvents,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    date,
    isAuspicious,
    tithi,
    nakshatra,
    yoga,
    karana,
    sunrise,
    sunset,
    moonrise,
    moonset,
    rahuKalam,
    gulikaKalam,
    yamagandam,
    abhijitMuhurta,
    const DeepCollectionEquality().hash(_festivals),
    const DeepCollectionEquality().hash(_vratas),
    const DeepCollectionEquality().hash(_specialEvents),
  );

  /// Create a copy of PanchangamModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PanchangamModelImplCopyWith<_$PanchangamModelImpl> get copyWith =>
      __$$PanchangamModelImplCopyWithImpl<_$PanchangamModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PanchangamModelImplToJson(this);
  }
}

abstract class _PanchangamModel implements PanchangamModel {
  const factory _PanchangamModel({
    required final DateTime date,
    required final bool isAuspicious,
    required final String tithi,
    required final String nakshatra,
    required final String yoga,
    required final String karana,
    required final String sunrise,
    required final String sunset,
    required final String moonrise,
    required final String moonset,
    required final String rahuKalam,
    required final String gulikaKalam,
    required final String yamagandam,
    required final String abhijitMuhurta,
    final List<String> festivals,
    final List<String> vratas,
    final List<String> specialEvents,
  }) = _$PanchangamModelImpl;

  factory _PanchangamModel.fromJson(Map<String, dynamic> json) =
      _$PanchangamModelImpl.fromJson;

  @override
  DateTime get date;
  @override
  bool get isAuspicious;
  @override
  String get tithi;
  @override
  String get nakshatra;
  @override
  String get yoga;
  @override
  String get karana;
  @override
  String get sunrise;
  @override
  String get sunset;
  @override
  String get moonrise;
  @override
  String get moonset;
  @override
  String get rahuKalam;
  @override
  String get gulikaKalam;
  @override
  String get yamagandam;
  @override
  String get abhijitMuhurta;
  @override
  List<String> get festivals;
  @override
  List<String> get vratas;
  @override
  List<String> get specialEvents;

  /// Create a copy of PanchangamModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PanchangamModelImplCopyWith<_$PanchangamModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
