[{"type": {"import": "package:shivish/apps/admin/bloc/home/<USER>", "name": "HomeCubit", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/home/<USER>", "name": "HomeCubit", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/notification_service.dart", "name": "NotificationService", "isNullable": true, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_notificationService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]