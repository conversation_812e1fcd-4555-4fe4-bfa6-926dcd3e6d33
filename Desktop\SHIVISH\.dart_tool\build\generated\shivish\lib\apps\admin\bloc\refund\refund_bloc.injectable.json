[{"type": {"import": "package:shivish/apps/admin/bloc/refund/refund_bloc.dart", "name": "RefundBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/refund/refund_bloc.dart", "name": "RefundBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/refund_service.dart", "name": "RefundService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_refundService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]