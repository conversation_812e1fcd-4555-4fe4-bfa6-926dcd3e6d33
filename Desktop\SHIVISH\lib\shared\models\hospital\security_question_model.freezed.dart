// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'security_question_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

SecurityQuestionModel _$SecurityQuestionModelFromJson(
  Map<String, dynamic> json,
) {
  return _SecurityQuestionModel.fromJson(json);
}

/// @nodoc
mixin _$SecurityQuestionModel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get question => throw _privateConstructorUsedError;
  String get answer => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this SecurityQuestionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SecurityQuestionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SecurityQuestionModelCopyWith<SecurityQuestionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SecurityQuestionModelCopyWith<$Res> {
  factory $SecurityQuestionModelCopyWith(
    SecurityQuestionModel value,
    $Res Function(SecurityQuestionModel) then,
  ) = _$SecurityQuestionModelCopyWithImpl<$Res, SecurityQuestionModel>;
  @useResult
  $Res call({
    String id,
    String userId,
    String question,
    String answer,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$SecurityQuestionModelCopyWithImpl<
  $Res,
  $Val extends SecurityQuestionModel
>
    implements $SecurityQuestionModelCopyWith<$Res> {
  _$SecurityQuestionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SecurityQuestionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? question = null,
    Object? answer = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            question: null == question
                ? _value.question
                : question // ignore: cast_nullable_to_non_nullable
                      as String,
            answer: null == answer
                ? _value.answer
                : answer // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SecurityQuestionModelImplCopyWith<$Res>
    implements $SecurityQuestionModelCopyWith<$Res> {
  factory _$$SecurityQuestionModelImplCopyWith(
    _$SecurityQuestionModelImpl value,
    $Res Function(_$SecurityQuestionModelImpl) then,
  ) = __$$SecurityQuestionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String question,
    String answer,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$SecurityQuestionModelImplCopyWithImpl<$Res>
    extends
        _$SecurityQuestionModelCopyWithImpl<$Res, _$SecurityQuestionModelImpl>
    implements _$$SecurityQuestionModelImplCopyWith<$Res> {
  __$$SecurityQuestionModelImplCopyWithImpl(
    _$SecurityQuestionModelImpl _value,
    $Res Function(_$SecurityQuestionModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SecurityQuestionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? question = null,
    Object? answer = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$SecurityQuestionModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        question: null == question
            ? _value.question
            : question // ignore: cast_nullable_to_non_nullable
                  as String,
        answer: null == answer
            ? _value.answer
            : answer // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SecurityQuestionModelImpl implements _SecurityQuestionModel {
  const _$SecurityQuestionModelImpl({
    required this.id,
    required this.userId,
    required this.question,
    required this.answer,
    required this.createdAt,
    required this.updatedAt,
  });

  factory _$SecurityQuestionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SecurityQuestionModelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String question;
  @override
  final String answer;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'SecurityQuestionModel(id: $id, userId: $userId, question: $question, answer: $answer, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SecurityQuestionModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.answer, answer) || other.answer == answer) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    question,
    answer,
    createdAt,
    updatedAt,
  );

  /// Create a copy of SecurityQuestionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SecurityQuestionModelImplCopyWith<_$SecurityQuestionModelImpl>
  get copyWith =>
      __$$SecurityQuestionModelImplCopyWithImpl<_$SecurityQuestionModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$SecurityQuestionModelImplToJson(this);
  }
}

abstract class _SecurityQuestionModel implements SecurityQuestionModel {
  const factory _SecurityQuestionModel({
    required final String id,
    required final String userId,
    required final String question,
    required final String answer,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$SecurityQuestionModelImpl;

  factory _SecurityQuestionModel.fromJson(Map<String, dynamic> json) =
      _$SecurityQuestionModelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get question;
  @override
  String get answer;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of SecurityQuestionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SecurityQuestionModelImplCopyWith<_$SecurityQuestionModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
