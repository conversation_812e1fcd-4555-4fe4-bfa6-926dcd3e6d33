// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ScheduleModelImpl _$$ScheduleModelImplFromJson(Map<String, dynamic> json) =>
    _$ScheduleModelImpl(
      id: json['id'] as String,
      isOpen24x7: json['isOpen24x7'] as bool,
      weeklySchedule: (json['weeklySchedule'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, DaySchedule.fromJson(e as Map<String, dynamic>)),
      ),
      holidays: (json['holidays'] as List<dynamic>)
          .map((e) => HolidaySchedule.fromJson(e as Map<String, dynamic>))
          .toList(),
      timeZone: json['timeZone'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$ScheduleModelImplToJson(_$ScheduleModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isOpen24x7': instance.isOpen24x7,
      'weeklySchedule': instance.weeklySchedule.map(
        (k, e) => MapEntry(k, e.toJson()),
      ),
      'holidays': instance.holidays.map((e) => e.toJson()).toList(),
      'timeZone': instance.timeZone,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_$DayScheduleImpl _$$DayScheduleImplFromJson(Map<String, dynamic> json) =>
    _$DayScheduleImpl(
      isOpen: json['isOpen'] as bool,
      slots: (json['slots'] as List<dynamic>)
          .map((e) => TimeSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$DayScheduleImplToJson(_$DayScheduleImpl instance) =>
    <String, dynamic>{
      'isOpen': instance.isOpen,
      'slots': instance.slots.map((e) => e.toJson()).toList(),
    };

_$TimeSlotImpl _$$TimeSlotImplFromJson(Map<String, dynamic> json) =>
    _$TimeSlotImpl(
      openTime: json['openTime'] as String,
      closeTime: json['closeTime'] as String,
      breakStart: json['breakStart'] as String?,
      breakEnd: json['breakEnd'] as String?,
    );

Map<String, dynamic> _$$TimeSlotImplToJson(_$TimeSlotImpl instance) =>
    <String, dynamic>{
      'openTime': instance.openTime,
      'closeTime': instance.closeTime,
      'breakStart': instance.breakStart,
      'breakEnd': instance.breakEnd,
    };

_$HolidayScheduleImpl _$$HolidayScheduleImplFromJson(
  Map<String, dynamic> json,
) => _$HolidayScheduleImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  isRecurring: json['isRecurring'] as bool? ?? false,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$HolidayScheduleImplToJson(
  _$HolidayScheduleImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate.toIso8601String(),
  'isRecurring': instance.isRecurring,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
