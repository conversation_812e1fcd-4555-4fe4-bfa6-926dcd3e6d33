// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HelpSuggestionImpl _$$HelpSuggestionImplFromJson(Map<String, dynamic> json) =>
    _$HelpSuggestionImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      relevance: (json['relevance'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$HelpSuggestionImplToJson(
  _$HelpSuggestionImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'category': instance.category,
  'relevance': instance.relevance,
};
