// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ShoppingListModelImpl _$$ShoppingListModelImplFromJson(
  Map<String, dynamic> json,
) => _$ShoppingListModelImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  userId: json['userId'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => ShoppingListItem.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  status:
      $enumDecodeNullable(_$ShoppingListStatusEnumMap, json['status']) ??
      ShoppingListStatus.draft,
  sellerId: json['sellerId'] as String?,
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$$ShoppingListModelImplToJson(
  _$ShoppingListModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'userId': instance.userId,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
  'items': instance.items.map((e) => e.toJson()).toList(),
  'status': _$ShoppingListStatusEnumMap[instance.status]!,
  'sellerId': instance.sellerId,
  'notes': instance.notes,
};

const _$ShoppingListStatusEnumMap = {
  ShoppingListStatus.draft: 'draft',
  ShoppingListStatus.submitted: 'submitted',
  ShoppingListStatus.accepted: 'accepted',
  ShoppingListStatus.rejected: 'rejected',
  ShoppingListStatus.completed: 'completed',
  ShoppingListStatus.cancelled: 'cancelled',
};

_$ShoppingListItemImpl _$$ShoppingListItemImplFromJson(
  Map<String, dynamic> json,
) => _$ShoppingListItemImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  quantity: (json['quantity'] as num?)?.toInt() ?? 1,
  productId: json['productId'] as String?,
  notes: json['notes'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$ShoppingListItemImplToJson(
  _$ShoppingListItemImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'quantity': instance.quantity,
  'productId': instance.productId,
  'notes': instance.notes,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
