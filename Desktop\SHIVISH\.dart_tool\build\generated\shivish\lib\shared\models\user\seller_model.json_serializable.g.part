// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SellerModelImpl _$$SellerModelImplFromJson(Map<String, dynamic> json) =>
    _$SellerModelImpl(
      id: json['id'] as String,
      businessName: json['businessName'] as String,
      businessAddress: json['businessAddress'] as String,
      location: const GeoPointConverter().fromJson(
        json['location'] as Map<String, dynamic>,
      ),
      isDeleted: json['isDeleted'] as bool,
      isSuspended: json['isSuspended'] as bool,
      isVerified: json['isVerified'] as bool,
      profileImageUrl: json['profileImageUrl'] as String,
      businessLicenseUrl: json['businessLicenseUrl'] as String,
      taxRegistrationUrl: json['taxRegistrationUrl'] as String,
      metadata: json['metadata'] as Map<String, dynamic>,
      distance: (json['distance'] as num?)?.toDouble() ?? 0.0,
      activeOrders: (json['activeOrders'] as num?)?.toInt() ?? 0,
      maxOrders: (json['maxOrders'] as num?)?.toInt() ?? 10,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      deliveryTime: (json['deliveryTime'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$SellerModelImplToJson(_$SellerModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'businessName': instance.businessName,
      'businessAddress': instance.businessAddress,
      'location': const GeoPointConverter().toJson(instance.location),
      'isDeleted': instance.isDeleted,
      'isSuspended': instance.isSuspended,
      'isVerified': instance.isVerified,
      'profileImageUrl': instance.profileImageUrl,
      'businessLicenseUrl': instance.businessLicenseUrl,
      'taxRegistrationUrl': instance.taxRegistrationUrl,
      'metadata': instance.metadata,
      'distance': instance.distance,
      'activeOrders': instance.activeOrders,
      'maxOrders': instance.maxOrders,
      'rating': instance.rating,
      'deliveryTime': instance.deliveryTime,
    };
