// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationSettingsModelImpl _$$NotificationSettingsModelImplFromJson(
  Map<String, dynamic> json,
) => _$NotificationSettingsModelImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  pushEnabled: json['pushEnabled'] as bool? ?? true,
  emailEnabled: json['emailEnabled'] as bool? ?? true,
  smsEnabled: json['smsEnabled'] as bool? ?? true,
  inAppEnabled: json['inAppEnabled'] as bool? ?? true,
  typeSettings: (json['typeSettings'] as Map<String, dynamic>).map(
    (k, e) => MapEntry($enumDecode(_$NotificationTypeEnumMap, k), e as bool),
  ),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
);

Map<String, dynamic> _$$NotificationSettingsModelImplToJson(
  _$NotificationSettingsModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'pushEnabled': instance.pushEnabled,
  'emailEnabled': instance.emailEnabled,
  'smsEnabled': instance.smsEnabled,
  'inAppEnabled': instance.inAppEnabled,
  'typeSettings': instance.typeSettings.map(
    (k, e) => MapEntry(_$NotificationTypeEnumMap[k]!, e),
  ),
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
};

const _$NotificationTypeEnumMap = {
  NotificationType.general: 'general',
  NotificationType.order: 'order',
  NotificationType.payment: 'payment',
  NotificationType.booking: 'booking',
  NotificationType.event: 'event',
  NotificationType.chat: 'chat',
  NotificationType.system: 'system',
  NotificationType.verification: 'verification',
};
