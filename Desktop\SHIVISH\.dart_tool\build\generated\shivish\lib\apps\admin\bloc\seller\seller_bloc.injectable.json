[{"type": {"import": "package:shivish/apps/admin/bloc/seller/seller_bloc.dart", "name": "SellerBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/seller/seller_bloc.dart", "name": "SellerBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/seller_service.dart", "name": "SellerService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_sellerService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]