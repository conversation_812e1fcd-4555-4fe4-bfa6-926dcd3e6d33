// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'review_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ReviewImage _$ReviewImageFromJson(Map<String, dynamic> json) {
  return _ReviewImage.fromJson(json);
}

/// @nodoc
mixin _$ReviewImage {
  String get id => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;
  String? get caption => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this ReviewImage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReviewImage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReviewImageCopyWith<ReviewImage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReviewImageCopyWith<$Res> {
  factory $ReviewImageCopyWith(
    ReviewImage value,
    $Res Function(ReviewImage) then,
  ) = _$ReviewImageCopyWithImpl<$Res, ReviewImage>;
  @useResult
  $Res call({
    String id,
    String url,
    String? caption,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$ReviewImageCopyWithImpl<$Res, $Val extends ReviewImage>
    implements $ReviewImageCopyWith<$Res> {
  _$ReviewImageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReviewImage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? url = null,
    Object? caption = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            url: null == url
                ? _value.url
                : url // ignore: cast_nullable_to_non_nullable
                      as String,
            caption: freezed == caption
                ? _value.caption
                : caption // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ReviewImageImplCopyWith<$Res>
    implements $ReviewImageCopyWith<$Res> {
  factory _$$ReviewImageImplCopyWith(
    _$ReviewImageImpl value,
    $Res Function(_$ReviewImageImpl) then,
  ) = __$$ReviewImageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String url,
    String? caption,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$ReviewImageImplCopyWithImpl<$Res>
    extends _$ReviewImageCopyWithImpl<$Res, _$ReviewImageImpl>
    implements _$$ReviewImageImplCopyWith<$Res> {
  __$$ReviewImageImplCopyWithImpl(
    _$ReviewImageImpl _value,
    $Res Function(_$ReviewImageImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ReviewImage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? url = null,
    Object? caption = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$ReviewImageImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _value.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        caption: freezed == caption
            ? _value.caption
            : caption // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ReviewImageImpl implements _ReviewImage {
  const _$ReviewImageImpl({
    required this.id,
    required this.url,
    this.caption,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$ReviewImageImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReviewImageImplFromJson(json);

  @override
  final String id;
  @override
  final String url;
  @override
  final String? caption;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'ReviewImage(id: $id, url: $url, caption: $caption, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReviewImageImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.caption, caption) || other.caption == caption) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    url,
    caption,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of ReviewImage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReviewImageImplCopyWith<_$ReviewImageImpl> get copyWith =>
      __$$ReviewImageImplCopyWithImpl<_$ReviewImageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReviewImageImplToJson(this);
  }
}

abstract class _ReviewImage implements ReviewImage {
  const factory _ReviewImage({
    required final String id,
    required final String url,
    final String? caption,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$ReviewImageImpl;

  factory _ReviewImage.fromJson(Map<String, dynamic> json) =
      _$ReviewImageImpl.fromJson;

  @override
  String get id;
  @override
  String get url;
  @override
  String? get caption;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of ReviewImage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReviewImageImplCopyWith<_$ReviewImageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReviewRating _$ReviewRatingFromJson(Map<String, dynamic> json) {
  return _ReviewRating.fromJson(json);
}

/// @nodoc
mixin _$ReviewRating {
  String get id => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  double get value => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this ReviewRating to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReviewRating
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReviewRatingCopyWith<ReviewRating> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReviewRatingCopyWith<$Res> {
  factory $ReviewRatingCopyWith(
    ReviewRating value,
    $Res Function(ReviewRating) then,
  ) = _$ReviewRatingCopyWithImpl<$Res, ReviewRating>;
  @useResult
  $Res call({
    String id,
    String category,
    double value,
    String? comment,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$ReviewRatingCopyWithImpl<$Res, $Val extends ReviewRating>
    implements $ReviewRatingCopyWith<$Res> {
  _$ReviewRatingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReviewRating
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? category = null,
    Object? value = null,
    Object? comment = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            category: null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                      as String,
            value: null == value
                ? _value.value
                : value // ignore: cast_nullable_to_non_nullable
                      as double,
            comment: freezed == comment
                ? _value.comment
                : comment // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ReviewRatingImplCopyWith<$Res>
    implements $ReviewRatingCopyWith<$Res> {
  factory _$$ReviewRatingImplCopyWith(
    _$ReviewRatingImpl value,
    $Res Function(_$ReviewRatingImpl) then,
  ) = __$$ReviewRatingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String category,
    double value,
    String? comment,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$ReviewRatingImplCopyWithImpl<$Res>
    extends _$ReviewRatingCopyWithImpl<$Res, _$ReviewRatingImpl>
    implements _$$ReviewRatingImplCopyWith<$Res> {
  __$$ReviewRatingImplCopyWithImpl(
    _$ReviewRatingImpl _value,
    $Res Function(_$ReviewRatingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ReviewRating
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? category = null,
    Object? value = null,
    Object? comment = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$ReviewRatingImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        category: null == category
            ? _value.category
            : category // ignore: cast_nullable_to_non_nullable
                  as String,
        value: null == value
            ? _value.value
            : value // ignore: cast_nullable_to_non_nullable
                  as double,
        comment: freezed == comment
            ? _value.comment
            : comment // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ReviewRatingImpl implements _ReviewRating {
  const _$ReviewRatingImpl({
    required this.id,
    required this.category,
    required this.value,
    this.comment,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$ReviewRatingImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReviewRatingImplFromJson(json);

  @override
  final String id;
  @override
  final String category;
  @override
  final double value;
  @override
  final String? comment;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'ReviewRating(id: $id, category: $category, value: $value, comment: $comment, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReviewRatingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    category,
    value,
    comment,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of ReviewRating
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReviewRatingImplCopyWith<_$ReviewRatingImpl> get copyWith =>
      __$$ReviewRatingImplCopyWithImpl<_$ReviewRatingImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReviewRatingImplToJson(this);
  }
}

abstract class _ReviewRating implements ReviewRating {
  const factory _ReviewRating({
    required final String id,
    required final String category,
    required final double value,
    final String? comment,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$ReviewRatingImpl;

  factory _ReviewRating.fromJson(Map<String, dynamic> json) =
      _$ReviewRatingImpl.fromJson;

  @override
  String get id;
  @override
  String get category;
  @override
  double get value;
  @override
  String? get comment;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of ReviewRating
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReviewRatingImplCopyWith<_$ReviewRatingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReviewModel _$ReviewModelFromJson(Map<String, dynamic> json) {
  return _ReviewModel.fromJson(json);
}

/// @nodoc
mixin _$ReviewModel {
  String get id => throw _privateConstructorUsedError;
  String get productId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  int get rating => throw _privateConstructorUsedError;
  String get comment => throw _privateConstructorUsedError;
  List<String> get images => throw _privateConstructorUsedError;
  int get helpfulCount => throw _privateConstructorUsedError;
  int get notHelpfulCount => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isHidden => throw _privateConstructorUsedError;
  List<String> get reportedBy => throw _privateConstructorUsedError;
  List<String> get moderatedBy => throw _privateConstructorUsedError;
  List<String> get adminNotes => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ReviewModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReviewModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReviewModelCopyWith<ReviewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReviewModelCopyWith<$Res> {
  factory $ReviewModelCopyWith(
    ReviewModel value,
    $Res Function(ReviewModel) then,
  ) = _$ReviewModelCopyWithImpl<$Res, ReviewModel>;
  @useResult
  $Res call({
    String id,
    String productId,
    String userId,
    String userName,
    int rating,
    String comment,
    List<String> images,
    int helpfulCount,
    int notHelpfulCount,
    bool isVerified,
    bool isHidden,
    List<String> reportedBy,
    List<String> moderatedBy,
    List<String> adminNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$ReviewModelCopyWithImpl<$Res, $Val extends ReviewModel>
    implements $ReviewModelCopyWith<$Res> {
  _$ReviewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReviewModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? productId = null,
    Object? userId = null,
    Object? userName = null,
    Object? rating = null,
    Object? comment = null,
    Object? images = null,
    Object? helpfulCount = null,
    Object? notHelpfulCount = null,
    Object? isVerified = null,
    Object? isHidden = null,
    Object? reportedBy = null,
    Object? moderatedBy = null,
    Object? adminNotes = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            productId: null == productId
                ? _value.productId
                : productId // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            userName: null == userName
                ? _value.userName
                : userName // ignore: cast_nullable_to_non_nullable
                      as String,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as int,
            comment: null == comment
                ? _value.comment
                : comment // ignore: cast_nullable_to_non_nullable
                      as String,
            images: null == images
                ? _value.images
                : images // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            helpfulCount: null == helpfulCount
                ? _value.helpfulCount
                : helpfulCount // ignore: cast_nullable_to_non_nullable
                      as int,
            notHelpfulCount: null == notHelpfulCount
                ? _value.notHelpfulCount
                : notHelpfulCount // ignore: cast_nullable_to_non_nullable
                      as int,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isHidden: null == isHidden
                ? _value.isHidden
                : isHidden // ignore: cast_nullable_to_non_nullable
                      as bool,
            reportedBy: null == reportedBy
                ? _value.reportedBy
                : reportedBy // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            moderatedBy: null == moderatedBy
                ? _value.moderatedBy
                : moderatedBy // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            adminNotes: null == adminNotes
                ? _value.adminNotes
                : adminNotes // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ReviewModelImplCopyWith<$Res>
    implements $ReviewModelCopyWith<$Res> {
  factory _$$ReviewModelImplCopyWith(
    _$ReviewModelImpl value,
    $Res Function(_$ReviewModelImpl) then,
  ) = __$$ReviewModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String productId,
    String userId,
    String userName,
    int rating,
    String comment,
    List<String> images,
    int helpfulCount,
    int notHelpfulCount,
    bool isVerified,
    bool isHidden,
    List<String> reportedBy,
    List<String> moderatedBy,
    List<String> adminNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$ReviewModelImplCopyWithImpl<$Res>
    extends _$ReviewModelCopyWithImpl<$Res, _$ReviewModelImpl>
    implements _$$ReviewModelImplCopyWith<$Res> {
  __$$ReviewModelImplCopyWithImpl(
    _$ReviewModelImpl _value,
    $Res Function(_$ReviewModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ReviewModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? productId = null,
    Object? userId = null,
    Object? userName = null,
    Object? rating = null,
    Object? comment = null,
    Object? images = null,
    Object? helpfulCount = null,
    Object? notHelpfulCount = null,
    Object? isVerified = null,
    Object? isHidden = null,
    Object? reportedBy = null,
    Object? moderatedBy = null,
    Object? adminNotes = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$ReviewModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        productId: null == productId
            ? _value.productId
            : productId // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        userName: null == userName
            ? _value.userName
            : userName // ignore: cast_nullable_to_non_nullable
                  as String,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as int,
        comment: null == comment
            ? _value.comment
            : comment // ignore: cast_nullable_to_non_nullable
                  as String,
        images: null == images
            ? _value._images
            : images // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        helpfulCount: null == helpfulCount
            ? _value.helpfulCount
            : helpfulCount // ignore: cast_nullable_to_non_nullable
                  as int,
        notHelpfulCount: null == notHelpfulCount
            ? _value.notHelpfulCount
            : notHelpfulCount // ignore: cast_nullable_to_non_nullable
                  as int,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isHidden: null == isHidden
            ? _value.isHidden
            : isHidden // ignore: cast_nullable_to_non_nullable
                  as bool,
        reportedBy: null == reportedBy
            ? _value._reportedBy
            : reportedBy // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        moderatedBy: null == moderatedBy
            ? _value._moderatedBy
            : moderatedBy // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        adminNotes: null == adminNotes
            ? _value._adminNotes
            : adminNotes // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ReviewModelImpl implements _ReviewModel {
  const _$ReviewModelImpl({
    required this.id,
    required this.productId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.comment,
    final List<String> images = const [],
    this.helpfulCount = 0,
    this.notHelpfulCount = 0,
    this.isVerified = false,
    this.isHidden = false,
    final List<String> reportedBy = const [],
    final List<String> moderatedBy = const [],
    final List<String> adminNotes = const [],
    this.createdAt,
    this.updatedAt,
  }) : _images = images,
       _reportedBy = reportedBy,
       _moderatedBy = moderatedBy,
       _adminNotes = adminNotes;

  factory _$ReviewModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReviewModelImplFromJson(json);

  @override
  final String id;
  @override
  final String productId;
  @override
  final String userId;
  @override
  final String userName;
  @override
  final int rating;
  @override
  final String comment;
  final List<String> _images;
  @override
  @JsonKey()
  List<String> get images {
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_images);
  }

  @override
  @JsonKey()
  final int helpfulCount;
  @override
  @JsonKey()
  final int notHelpfulCount;
  @override
  @JsonKey()
  final bool isVerified;
  @override
  @JsonKey()
  final bool isHidden;
  final List<String> _reportedBy;
  @override
  @JsonKey()
  List<String> get reportedBy {
    if (_reportedBy is EqualUnmodifiableListView) return _reportedBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reportedBy);
  }

  final List<String> _moderatedBy;
  @override
  @JsonKey()
  List<String> get moderatedBy {
    if (_moderatedBy is EqualUnmodifiableListView) return _moderatedBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_moderatedBy);
  }

  final List<String> _adminNotes;
  @override
  @JsonKey()
  List<String> get adminNotes {
    if (_adminNotes is EqualUnmodifiableListView) return _adminNotes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_adminNotes);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ReviewModel(id: $id, productId: $productId, userId: $userId, userName: $userName, rating: $rating, comment: $comment, images: $images, helpfulCount: $helpfulCount, notHelpfulCount: $notHelpfulCount, isVerified: $isVerified, isHidden: $isHidden, reportedBy: $reportedBy, moderatedBy: $moderatedBy, adminNotes: $adminNotes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReviewModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            const DeepCollectionEquality().equals(other._images, _images) &&
            (identical(other.helpfulCount, helpfulCount) ||
                other.helpfulCount == helpfulCount) &&
            (identical(other.notHelpfulCount, notHelpfulCount) ||
                other.notHelpfulCount == notHelpfulCount) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            const DeepCollectionEquality().equals(
              other._reportedBy,
              _reportedBy,
            ) &&
            const DeepCollectionEquality().equals(
              other._moderatedBy,
              _moderatedBy,
            ) &&
            const DeepCollectionEquality().equals(
              other._adminNotes,
              _adminNotes,
            ) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    productId,
    userId,
    userName,
    rating,
    comment,
    const DeepCollectionEquality().hash(_images),
    helpfulCount,
    notHelpfulCount,
    isVerified,
    isHidden,
    const DeepCollectionEquality().hash(_reportedBy),
    const DeepCollectionEquality().hash(_moderatedBy),
    const DeepCollectionEquality().hash(_adminNotes),
    createdAt,
    updatedAt,
  );

  /// Create a copy of ReviewModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReviewModelImplCopyWith<_$ReviewModelImpl> get copyWith =>
      __$$ReviewModelImplCopyWithImpl<_$ReviewModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReviewModelImplToJson(this);
  }
}

abstract class _ReviewModel implements ReviewModel {
  const factory _ReviewModel({
    required final String id,
    required final String productId,
    required final String userId,
    required final String userName,
    required final int rating,
    required final String comment,
    final List<String> images,
    final int helpfulCount,
    final int notHelpfulCount,
    final bool isVerified,
    final bool isHidden,
    final List<String> reportedBy,
    final List<String> moderatedBy,
    final List<String> adminNotes,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$ReviewModelImpl;

  factory _ReviewModel.fromJson(Map<String, dynamic> json) =
      _$ReviewModelImpl.fromJson;

  @override
  String get id;
  @override
  String get productId;
  @override
  String get userId;
  @override
  String get userName;
  @override
  int get rating;
  @override
  String get comment;
  @override
  List<String> get images;
  @override
  int get helpfulCount;
  @override
  int get notHelpfulCount;
  @override
  bool get isVerified;
  @override
  bool get isHidden;
  @override
  List<String> get reportedBy;
  @override
  List<String> get moderatedBy;
  @override
  List<String> get adminNotes;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of ReviewModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReviewModelImplCopyWith<_$ReviewModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
