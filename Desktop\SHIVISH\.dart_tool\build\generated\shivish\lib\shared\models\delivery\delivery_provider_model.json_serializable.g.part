// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeliveryProviderModelImpl _$$DeliveryProviderModelImplFromJson(
  Map<String, dynamic> json,
) => _$DeliveryProviderModelImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  type: $enumDecode(_$DeliveryProviderTypeEnumMap, json['type']),
  isActive: json['isActive'] as bool,
  config: json['config'] as Map<String, dynamic>,
  description: json['description'] as String?,
  logoUrl: json['logoUrl'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$DeliveryProviderModelImplToJson(
  _$DeliveryProviderModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'type': _$DeliveryProviderTypeEnumMap[instance.type]!,
  'isActive': instance.isActive,
  'config': instance.config,
  'description': instance.description,
  'logoUrl': instance.logoUrl,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$DeliveryProviderTypeEnumMap = {
  DeliveryProviderType.ecomExpress: 'ecom_express',
  DeliveryProviderType.localDelivery: 'local_delivery',
};
