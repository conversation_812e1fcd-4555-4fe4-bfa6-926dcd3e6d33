[{"type": {"import": "package:shivish/apps/admin/bloc/product/product_bloc.dart", "name": "ProductBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/product/product_bloc.dart", "name": "ProductBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/product/product_service.dart", "name": "ProductService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_productService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]