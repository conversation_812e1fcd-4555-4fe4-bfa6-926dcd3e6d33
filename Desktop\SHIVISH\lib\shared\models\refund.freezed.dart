// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refund.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Refund _$RefundFromJson(Map<String, dynamic> json) {
  return _Refund.fromJson(json);
}

/// @nodoc
mixin _$Refund {
  String get id => throw _privateConstructorUsedError;
  String get orderId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get sellerId => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get reason => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String get createdAt => throw _privateConstructorUsedError;
  String get updatedAt => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  bool get isPartial => throw _privateConstructorUsedError;
  double get partialAmount => throw _privateConstructorUsedError;
  String? get partialReason => throw _privateConstructorUsedError;
  Map<String, dynamic> get refundMethod => throw _privateConstructorUsedError;
  Map<String, dynamic> get trackingInfo => throw _privateConstructorUsedError;
  String? get approvedBy => throw _privateConstructorUsedError;
  String? get approvedAt => throw _privateConstructorUsedError;
  String? get rejectedBy => throw _privateConstructorUsedError;
  String? get rejectedAt => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  String? get processedBy => throw _privateConstructorUsedError;
  String? get processedAt => throw _privateConstructorUsedError;
  String? get transactionId => throw _privateConstructorUsedError;
  bool get isDisputed => throw _privateConstructorUsedError;
  String? get disputeReason => throw _privateConstructorUsedError;
  String? get disputeCreatedAt => throw _privateConstructorUsedError;
  String? get disputeResolvedBy => throw _privateConstructorUsedError;
  String? get disputeResolvedAt => throw _privateConstructorUsedError;
  String? get disputeResolution => throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get auditLog => throw _privateConstructorUsedError;

  /// Serializes this Refund to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Refund
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RefundCopyWith<Refund> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RefundCopyWith<$Res> {
  factory $RefundCopyWith(Refund value, $Res Function(Refund) then) =
      _$RefundCopyWithImpl<$Res, Refund>;
  @useResult
  $Res call({
    String id,
    String orderId,
    String userId,
    String sellerId,
    double amount,
    String reason,
    String status,
    String createdAt,
    String updatedAt,
    String? notes,
    bool isPartial,
    double partialAmount,
    String? partialReason,
    Map<String, dynamic> refundMethod,
    Map<String, dynamic> trackingInfo,
    String? approvedBy,
    String? approvedAt,
    String? rejectedBy,
    String? rejectedAt,
    String? rejectionReason,
    String? processedBy,
    String? processedAt,
    String? transactionId,
    bool isDisputed,
    String? disputeReason,
    String? disputeCreatedAt,
    String? disputeResolvedBy,
    String? disputeResolvedAt,
    String? disputeResolution,
    List<Map<String, dynamic>> auditLog,
  });
}

/// @nodoc
class _$RefundCopyWithImpl<$Res, $Val extends Refund>
    implements $RefundCopyWith<$Res> {
  _$RefundCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Refund
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? orderId = null,
    Object? userId = null,
    Object? sellerId = null,
    Object? amount = null,
    Object? reason = null,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? notes = freezed,
    Object? isPartial = null,
    Object? partialAmount = null,
    Object? partialReason = freezed,
    Object? refundMethod = null,
    Object? trackingInfo = null,
    Object? approvedBy = freezed,
    Object? approvedAt = freezed,
    Object? rejectedBy = freezed,
    Object? rejectedAt = freezed,
    Object? rejectionReason = freezed,
    Object? processedBy = freezed,
    Object? processedAt = freezed,
    Object? transactionId = freezed,
    Object? isDisputed = null,
    Object? disputeReason = freezed,
    Object? disputeCreatedAt = freezed,
    Object? disputeResolvedBy = freezed,
    Object? disputeResolvedAt = freezed,
    Object? disputeResolution = freezed,
    Object? auditLog = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            orderId: null == orderId
                ? _value.orderId
                : orderId // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            sellerId: null == sellerId
                ? _value.sellerId
                : sellerId // ignore: cast_nullable_to_non_nullable
                      as String,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as double,
            reason: null == reason
                ? _value.reason
                : reason // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as String,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as String,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            isPartial: null == isPartial
                ? _value.isPartial
                : isPartial // ignore: cast_nullable_to_non_nullable
                      as bool,
            partialAmount: null == partialAmount
                ? _value.partialAmount
                : partialAmount // ignore: cast_nullable_to_non_nullable
                      as double,
            partialReason: freezed == partialReason
                ? _value.partialReason
                : partialReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            refundMethod: null == refundMethod
                ? _value.refundMethod
                : refundMethod // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            trackingInfo: null == trackingInfo
                ? _value.trackingInfo
                : trackingInfo // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            approvedBy: freezed == approvedBy
                ? _value.approvedBy
                : approvedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            approvedAt: freezed == approvedAt
                ? _value.approvedAt
                : approvedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectedBy: freezed == rejectedBy
                ? _value.rejectedBy
                : rejectedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectedAt: freezed == rejectedAt
                ? _value.rejectedAt
                : rejectedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            processedBy: freezed == processedBy
                ? _value.processedBy
                : processedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            processedAt: freezed == processedAt
                ? _value.processedAt
                : processedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            transactionId: freezed == transactionId
                ? _value.transactionId
                : transactionId // ignore: cast_nullable_to_non_nullable
                      as String?,
            isDisputed: null == isDisputed
                ? _value.isDisputed
                : isDisputed // ignore: cast_nullable_to_non_nullable
                      as bool,
            disputeReason: freezed == disputeReason
                ? _value.disputeReason
                : disputeReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            disputeCreatedAt: freezed == disputeCreatedAt
                ? _value.disputeCreatedAt
                : disputeCreatedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            disputeResolvedBy: freezed == disputeResolvedBy
                ? _value.disputeResolvedBy
                : disputeResolvedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            disputeResolvedAt: freezed == disputeResolvedAt
                ? _value.disputeResolvedAt
                : disputeResolvedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            disputeResolution: freezed == disputeResolution
                ? _value.disputeResolution
                : disputeResolution // ignore: cast_nullable_to_non_nullable
                      as String?,
            auditLog: null == auditLog
                ? _value.auditLog
                : auditLog // ignore: cast_nullable_to_non_nullable
                      as List<Map<String, dynamic>>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$RefundImplCopyWith<$Res> implements $RefundCopyWith<$Res> {
  factory _$$RefundImplCopyWith(
    _$RefundImpl value,
    $Res Function(_$RefundImpl) then,
  ) = __$$RefundImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String orderId,
    String userId,
    String sellerId,
    double amount,
    String reason,
    String status,
    String createdAt,
    String updatedAt,
    String? notes,
    bool isPartial,
    double partialAmount,
    String? partialReason,
    Map<String, dynamic> refundMethod,
    Map<String, dynamic> trackingInfo,
    String? approvedBy,
    String? approvedAt,
    String? rejectedBy,
    String? rejectedAt,
    String? rejectionReason,
    String? processedBy,
    String? processedAt,
    String? transactionId,
    bool isDisputed,
    String? disputeReason,
    String? disputeCreatedAt,
    String? disputeResolvedBy,
    String? disputeResolvedAt,
    String? disputeResolution,
    List<Map<String, dynamic>> auditLog,
  });
}

/// @nodoc
class __$$RefundImplCopyWithImpl<$Res>
    extends _$RefundCopyWithImpl<$Res, _$RefundImpl>
    implements _$$RefundImplCopyWith<$Res> {
  __$$RefundImplCopyWithImpl(
    _$RefundImpl _value,
    $Res Function(_$RefundImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Refund
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? orderId = null,
    Object? userId = null,
    Object? sellerId = null,
    Object? amount = null,
    Object? reason = null,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? notes = freezed,
    Object? isPartial = null,
    Object? partialAmount = null,
    Object? partialReason = freezed,
    Object? refundMethod = null,
    Object? trackingInfo = null,
    Object? approvedBy = freezed,
    Object? approvedAt = freezed,
    Object? rejectedBy = freezed,
    Object? rejectedAt = freezed,
    Object? rejectionReason = freezed,
    Object? processedBy = freezed,
    Object? processedAt = freezed,
    Object? transactionId = freezed,
    Object? isDisputed = null,
    Object? disputeReason = freezed,
    Object? disputeCreatedAt = freezed,
    Object? disputeResolvedBy = freezed,
    Object? disputeResolvedAt = freezed,
    Object? disputeResolution = freezed,
    Object? auditLog = null,
  }) {
    return _then(
      _$RefundImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        orderId: null == orderId
            ? _value.orderId
            : orderId // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        sellerId: null == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        reason: null == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as String,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as String,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        isPartial: null == isPartial
            ? _value.isPartial
            : isPartial // ignore: cast_nullable_to_non_nullable
                  as bool,
        partialAmount: null == partialAmount
            ? _value.partialAmount
            : partialAmount // ignore: cast_nullable_to_non_nullable
                  as double,
        partialReason: freezed == partialReason
            ? _value.partialReason
            : partialReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        refundMethod: null == refundMethod
            ? _value._refundMethod
            : refundMethod // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        trackingInfo: null == trackingInfo
            ? _value._trackingInfo
            : trackingInfo // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        approvedBy: freezed == approvedBy
            ? _value.approvedBy
            : approvedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        approvedAt: freezed == approvedAt
            ? _value.approvedAt
            : approvedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectedBy: freezed == rejectedBy
            ? _value.rejectedBy
            : rejectedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectedAt: freezed == rejectedAt
            ? _value.rejectedAt
            : rejectedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        processedBy: freezed == processedBy
            ? _value.processedBy
            : processedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        processedAt: freezed == processedAt
            ? _value.processedAt
            : processedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        transactionId: freezed == transactionId
            ? _value.transactionId
            : transactionId // ignore: cast_nullable_to_non_nullable
                  as String?,
        isDisputed: null == isDisputed
            ? _value.isDisputed
            : isDisputed // ignore: cast_nullable_to_non_nullable
                  as bool,
        disputeReason: freezed == disputeReason
            ? _value.disputeReason
            : disputeReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        disputeCreatedAt: freezed == disputeCreatedAt
            ? _value.disputeCreatedAt
            : disputeCreatedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        disputeResolvedBy: freezed == disputeResolvedBy
            ? _value.disputeResolvedBy
            : disputeResolvedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        disputeResolvedAt: freezed == disputeResolvedAt
            ? _value.disputeResolvedAt
            : disputeResolvedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        disputeResolution: freezed == disputeResolution
            ? _value.disputeResolution
            : disputeResolution // ignore: cast_nullable_to_non_nullable
                  as String?,
        auditLog: null == auditLog
            ? _value._auditLog
            : auditLog // ignore: cast_nullable_to_non_nullable
                  as List<Map<String, dynamic>>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$RefundImpl implements _Refund {
  const _$RefundImpl({
    required this.id,
    required this.orderId,
    required this.userId,
    required this.sellerId,
    required this.amount,
    required this.reason,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
    this.isPartial = false,
    this.partialAmount = 0,
    this.partialReason,
    final Map<String, dynamic> refundMethod = const {},
    final Map<String, dynamic> trackingInfo = const {},
    this.approvedBy,
    this.approvedAt,
    this.rejectedBy,
    this.rejectedAt,
    this.rejectionReason,
    this.processedBy,
    this.processedAt,
    this.transactionId,
    this.isDisputed = false,
    this.disputeReason,
    this.disputeCreatedAt,
    this.disputeResolvedBy,
    this.disputeResolvedAt,
    this.disputeResolution,
    final List<Map<String, dynamic>> auditLog = const [],
  }) : _refundMethod = refundMethod,
       _trackingInfo = trackingInfo,
       _auditLog = auditLog;

  factory _$RefundImpl.fromJson(Map<String, dynamic> json) =>
      _$$RefundImplFromJson(json);

  @override
  final String id;
  @override
  final String orderId;
  @override
  final String userId;
  @override
  final String sellerId;
  @override
  final double amount;
  @override
  final String reason;
  @override
  final String status;
  @override
  final String createdAt;
  @override
  final String updatedAt;
  @override
  final String? notes;
  @override
  @JsonKey()
  final bool isPartial;
  @override
  @JsonKey()
  final double partialAmount;
  @override
  final String? partialReason;
  final Map<String, dynamic> _refundMethod;
  @override
  @JsonKey()
  Map<String, dynamic> get refundMethod {
    if (_refundMethod is EqualUnmodifiableMapView) return _refundMethod;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_refundMethod);
  }

  final Map<String, dynamic> _trackingInfo;
  @override
  @JsonKey()
  Map<String, dynamic> get trackingInfo {
    if (_trackingInfo is EqualUnmodifiableMapView) return _trackingInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_trackingInfo);
  }

  @override
  final String? approvedBy;
  @override
  final String? approvedAt;
  @override
  final String? rejectedBy;
  @override
  final String? rejectedAt;
  @override
  final String? rejectionReason;
  @override
  final String? processedBy;
  @override
  final String? processedAt;
  @override
  final String? transactionId;
  @override
  @JsonKey()
  final bool isDisputed;
  @override
  final String? disputeReason;
  @override
  final String? disputeCreatedAt;
  @override
  final String? disputeResolvedBy;
  @override
  final String? disputeResolvedAt;
  @override
  final String? disputeResolution;
  final List<Map<String, dynamic>> _auditLog;
  @override
  @JsonKey()
  List<Map<String, dynamic>> get auditLog {
    if (_auditLog is EqualUnmodifiableListView) return _auditLog;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_auditLog);
  }

  @override
  String toString() {
    return 'Refund(id: $id, orderId: $orderId, userId: $userId, sellerId: $sellerId, amount: $amount, reason: $reason, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, notes: $notes, isPartial: $isPartial, partialAmount: $partialAmount, partialReason: $partialReason, refundMethod: $refundMethod, trackingInfo: $trackingInfo, approvedBy: $approvedBy, approvedAt: $approvedAt, rejectedBy: $rejectedBy, rejectedAt: $rejectedAt, rejectionReason: $rejectionReason, processedBy: $processedBy, processedAt: $processedAt, transactionId: $transactionId, isDisputed: $isDisputed, disputeReason: $disputeReason, disputeCreatedAt: $disputeCreatedAt, disputeResolvedBy: $disputeResolvedBy, disputeResolvedAt: $disputeResolvedAt, disputeResolution: $disputeResolution, auditLog: $auditLog)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefundImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.isPartial, isPartial) ||
                other.isPartial == isPartial) &&
            (identical(other.partialAmount, partialAmount) ||
                other.partialAmount == partialAmount) &&
            (identical(other.partialReason, partialReason) ||
                other.partialReason == partialReason) &&
            const DeepCollectionEquality().equals(
              other._refundMethod,
              _refundMethod,
            ) &&
            const DeepCollectionEquality().equals(
              other._trackingInfo,
              _trackingInfo,
            ) &&
            (identical(other.approvedBy, approvedBy) ||
                other.approvedBy == approvedBy) &&
            (identical(other.approvedAt, approvedAt) ||
                other.approvedAt == approvedAt) &&
            (identical(other.rejectedBy, rejectedBy) ||
                other.rejectedBy == rejectedBy) &&
            (identical(other.rejectedAt, rejectedAt) ||
                other.rejectedAt == rejectedAt) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.processedBy, processedBy) ||
                other.processedBy == processedBy) &&
            (identical(other.processedAt, processedAt) ||
                other.processedAt == processedAt) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.isDisputed, isDisputed) ||
                other.isDisputed == isDisputed) &&
            (identical(other.disputeReason, disputeReason) ||
                other.disputeReason == disputeReason) &&
            (identical(other.disputeCreatedAt, disputeCreatedAt) ||
                other.disputeCreatedAt == disputeCreatedAt) &&
            (identical(other.disputeResolvedBy, disputeResolvedBy) ||
                other.disputeResolvedBy == disputeResolvedBy) &&
            (identical(other.disputeResolvedAt, disputeResolvedAt) ||
                other.disputeResolvedAt == disputeResolvedAt) &&
            (identical(other.disputeResolution, disputeResolution) ||
                other.disputeResolution == disputeResolution) &&
            const DeepCollectionEquality().equals(other._auditLog, _auditLog));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    orderId,
    userId,
    sellerId,
    amount,
    reason,
    status,
    createdAt,
    updatedAt,
    notes,
    isPartial,
    partialAmount,
    partialReason,
    const DeepCollectionEquality().hash(_refundMethod),
    const DeepCollectionEquality().hash(_trackingInfo),
    approvedBy,
    approvedAt,
    rejectedBy,
    rejectedAt,
    rejectionReason,
    processedBy,
    processedAt,
    transactionId,
    isDisputed,
    disputeReason,
    disputeCreatedAt,
    disputeResolvedBy,
    disputeResolvedAt,
    disputeResolution,
    const DeepCollectionEquality().hash(_auditLog),
  ]);

  /// Create a copy of Refund
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RefundImplCopyWith<_$RefundImpl> get copyWith =>
      __$$RefundImplCopyWithImpl<_$RefundImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RefundImplToJson(this);
  }
}

abstract class _Refund implements Refund {
  const factory _Refund({
    required final String id,
    required final String orderId,
    required final String userId,
    required final String sellerId,
    required final double amount,
    required final String reason,
    required final String status,
    required final String createdAt,
    required final String updatedAt,
    final String? notes,
    final bool isPartial,
    final double partialAmount,
    final String? partialReason,
    final Map<String, dynamic> refundMethod,
    final Map<String, dynamic> trackingInfo,
    final String? approvedBy,
    final String? approvedAt,
    final String? rejectedBy,
    final String? rejectedAt,
    final String? rejectionReason,
    final String? processedBy,
    final String? processedAt,
    final String? transactionId,
    final bool isDisputed,
    final String? disputeReason,
    final String? disputeCreatedAt,
    final String? disputeResolvedBy,
    final String? disputeResolvedAt,
    final String? disputeResolution,
    final List<Map<String, dynamic>> auditLog,
  }) = _$RefundImpl;

  factory _Refund.fromJson(Map<String, dynamic> json) = _$RefundImpl.fromJson;

  @override
  String get id;
  @override
  String get orderId;
  @override
  String get userId;
  @override
  String get sellerId;
  @override
  double get amount;
  @override
  String get reason;
  @override
  String get status;
  @override
  String get createdAt;
  @override
  String get updatedAt;
  @override
  String? get notes;
  @override
  bool get isPartial;
  @override
  double get partialAmount;
  @override
  String? get partialReason;
  @override
  Map<String, dynamic> get refundMethod;
  @override
  Map<String, dynamic> get trackingInfo;
  @override
  String? get approvedBy;
  @override
  String? get approvedAt;
  @override
  String? get rejectedBy;
  @override
  String? get rejectedAt;
  @override
  String? get rejectionReason;
  @override
  String? get processedBy;
  @override
  String? get processedAt;
  @override
  String? get transactionId;
  @override
  bool get isDisputed;
  @override
  String? get disputeReason;
  @override
  String? get disputeCreatedAt;
  @override
  String? get disputeResolvedBy;
  @override
  String? get disputeResolvedAt;
  @override
  String? get disputeResolution;
  @override
  List<Map<String, dynamic>> get auditLog;

  /// Create a copy of Refund
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RefundImplCopyWith<_$RefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
