// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_gateway.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PaymentGateway _$PaymentGatewayFromJson(Map<String, dynamic> json) {
  return _PaymentGateway.fromJson(json);
}

/// @nodoc
mixin _$PaymentGateway {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  Map<String, dynamic> get credentials => throw _privateConstructorUsedError;
  double get transactionFee => throw _privateConstructorUsedError;
  double get minimumAmount => throw _privateConstructorUsedError;
  double get maximumAmount => throw _privateConstructorUsedError;
  List<String> get supportedCurrencies => throw _privateConstructorUsedError;
  List<String> get supportedPaymentMethods =>
      throw _privateConstructorUsedError;
  Map<String, dynamic> get transactionFees =>
      throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this PaymentGateway to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentGateway
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentGatewayCopyWith<PaymentGateway> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentGatewayCopyWith<$Res> {
  factory $PaymentGatewayCopyWith(
    PaymentGateway value,
    $Res Function(PaymentGateway) then,
  ) = _$PaymentGatewayCopyWithImpl<$Res, PaymentGateway>;
  @useResult
  $Res call({
    String id,
    String name,
    String type,
    bool isActive,
    Map<String, dynamic> credentials,
    double transactionFee,
    double minimumAmount,
    double maximumAmount,
    List<String> supportedCurrencies,
    List<String> supportedPaymentMethods,
    Map<String, dynamic> transactionFees,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$PaymentGatewayCopyWithImpl<$Res, $Val extends PaymentGateway>
    implements $PaymentGatewayCopyWith<$Res> {
  _$PaymentGatewayCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentGateway
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? isActive = null,
    Object? credentials = null,
    Object? transactionFee = null,
    Object? minimumAmount = null,
    Object? maximumAmount = null,
    Object? supportedCurrencies = null,
    Object? supportedPaymentMethods = null,
    Object? transactionFees = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as String,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            credentials: null == credentials
                ? _value.credentials
                : credentials // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            transactionFee: null == transactionFee
                ? _value.transactionFee
                : transactionFee // ignore: cast_nullable_to_non_nullable
                      as double,
            minimumAmount: null == minimumAmount
                ? _value.minimumAmount
                : minimumAmount // ignore: cast_nullable_to_non_nullable
                      as double,
            maximumAmount: null == maximumAmount
                ? _value.maximumAmount
                : maximumAmount // ignore: cast_nullable_to_non_nullable
                      as double,
            supportedCurrencies: null == supportedCurrencies
                ? _value.supportedCurrencies
                : supportedCurrencies // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            supportedPaymentMethods: null == supportedPaymentMethods
                ? _value.supportedPaymentMethods
                : supportedPaymentMethods // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            transactionFees: null == transactionFees
                ? _value.transactionFees
                : transactionFees // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentGatewayImplCopyWith<$Res>
    implements $PaymentGatewayCopyWith<$Res> {
  factory _$$PaymentGatewayImplCopyWith(
    _$PaymentGatewayImpl value,
    $Res Function(_$PaymentGatewayImpl) then,
  ) = __$$PaymentGatewayImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String type,
    bool isActive,
    Map<String, dynamic> credentials,
    double transactionFee,
    double minimumAmount,
    double maximumAmount,
    List<String> supportedCurrencies,
    List<String> supportedPaymentMethods,
    Map<String, dynamic> transactionFees,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$PaymentGatewayImplCopyWithImpl<$Res>
    extends _$PaymentGatewayCopyWithImpl<$Res, _$PaymentGatewayImpl>
    implements _$$PaymentGatewayImplCopyWith<$Res> {
  __$$PaymentGatewayImplCopyWithImpl(
    _$PaymentGatewayImpl _value,
    $Res Function(_$PaymentGatewayImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentGateway
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? isActive = null,
    Object? credentials = null,
    Object? transactionFee = null,
    Object? minimumAmount = null,
    Object? maximumAmount = null,
    Object? supportedCurrencies = null,
    Object? supportedPaymentMethods = null,
    Object? transactionFees = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$PaymentGatewayImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as String,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        credentials: null == credentials
            ? _value._credentials
            : credentials // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        transactionFee: null == transactionFee
            ? _value.transactionFee
            : transactionFee // ignore: cast_nullable_to_non_nullable
                  as double,
        minimumAmount: null == minimumAmount
            ? _value.minimumAmount
            : minimumAmount // ignore: cast_nullable_to_non_nullable
                  as double,
        maximumAmount: null == maximumAmount
            ? _value.maximumAmount
            : maximumAmount // ignore: cast_nullable_to_non_nullable
                  as double,
        supportedCurrencies: null == supportedCurrencies
            ? _value._supportedCurrencies
            : supportedCurrencies // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        supportedPaymentMethods: null == supportedPaymentMethods
            ? _value._supportedPaymentMethods
            : supportedPaymentMethods // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        transactionFees: null == transactionFees
            ? _value._transactionFees
            : transactionFees // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentGatewayImpl implements _PaymentGateway {
  const _$PaymentGatewayImpl({
    required this.id,
    required this.name,
    required this.type,
    required this.isActive,
    required final Map<String, dynamic> credentials,
    required this.transactionFee,
    required this.minimumAmount,
    required this.maximumAmount,
    required final List<String> supportedCurrencies,
    required final List<String> supportedPaymentMethods,
    final Map<String, dynamic> transactionFees = const {},
    required this.createdAt,
    required this.updatedAt,
  }) : _credentials = credentials,
       _supportedCurrencies = supportedCurrencies,
       _supportedPaymentMethods = supportedPaymentMethods,
       _transactionFees = transactionFees;

  factory _$PaymentGatewayImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentGatewayImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String type;
  @override
  final bool isActive;
  final Map<String, dynamic> _credentials;
  @override
  Map<String, dynamic> get credentials {
    if (_credentials is EqualUnmodifiableMapView) return _credentials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_credentials);
  }

  @override
  final double transactionFee;
  @override
  final double minimumAmount;
  @override
  final double maximumAmount;
  final List<String> _supportedCurrencies;
  @override
  List<String> get supportedCurrencies {
    if (_supportedCurrencies is EqualUnmodifiableListView)
      return _supportedCurrencies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_supportedCurrencies);
  }

  final List<String> _supportedPaymentMethods;
  @override
  List<String> get supportedPaymentMethods {
    if (_supportedPaymentMethods is EqualUnmodifiableListView)
      return _supportedPaymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_supportedPaymentMethods);
  }

  final Map<String, dynamic> _transactionFees;
  @override
  @JsonKey()
  Map<String, dynamic> get transactionFees {
    if (_transactionFees is EqualUnmodifiableMapView) return _transactionFees;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_transactionFees);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'PaymentGateway(id: $id, name: $name, type: $type, isActive: $isActive, credentials: $credentials, transactionFee: $transactionFee, minimumAmount: $minimumAmount, maximumAmount: $maximumAmount, supportedCurrencies: $supportedCurrencies, supportedPaymentMethods: $supportedPaymentMethods, transactionFees: $transactionFees, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentGatewayImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            const DeepCollectionEquality().equals(
              other._credentials,
              _credentials,
            ) &&
            (identical(other.transactionFee, transactionFee) ||
                other.transactionFee == transactionFee) &&
            (identical(other.minimumAmount, minimumAmount) ||
                other.minimumAmount == minimumAmount) &&
            (identical(other.maximumAmount, maximumAmount) ||
                other.maximumAmount == maximumAmount) &&
            const DeepCollectionEquality().equals(
              other._supportedCurrencies,
              _supportedCurrencies,
            ) &&
            const DeepCollectionEquality().equals(
              other._supportedPaymentMethods,
              _supportedPaymentMethods,
            ) &&
            const DeepCollectionEquality().equals(
              other._transactionFees,
              _transactionFees,
            ) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    type,
    isActive,
    const DeepCollectionEquality().hash(_credentials),
    transactionFee,
    minimumAmount,
    maximumAmount,
    const DeepCollectionEquality().hash(_supportedCurrencies),
    const DeepCollectionEquality().hash(_supportedPaymentMethods),
    const DeepCollectionEquality().hash(_transactionFees),
    createdAt,
    updatedAt,
  );

  /// Create a copy of PaymentGateway
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentGatewayImplCopyWith<_$PaymentGatewayImpl> get copyWith =>
      __$$PaymentGatewayImplCopyWithImpl<_$PaymentGatewayImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentGatewayImplToJson(this);
  }
}

abstract class _PaymentGateway implements PaymentGateway {
  const factory _PaymentGateway({
    required final String id,
    required final String name,
    required final String type,
    required final bool isActive,
    required final Map<String, dynamic> credentials,
    required final double transactionFee,
    required final double minimumAmount,
    required final double maximumAmount,
    required final List<String> supportedCurrencies,
    required final List<String> supportedPaymentMethods,
    final Map<String, dynamic> transactionFees,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$PaymentGatewayImpl;

  factory _PaymentGateway.fromJson(Map<String, dynamic> json) =
      _$PaymentGatewayImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get type;
  @override
  bool get isActive;
  @override
  Map<String, dynamic> get credentials;
  @override
  double get transactionFee;
  @override
  double get minimumAmount;
  @override
  double get maximumAmount;
  @override
  List<String> get supportedCurrencies;
  @override
  List<String> get supportedPaymentMethods;
  @override
  Map<String, dynamic> get transactionFees;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of PaymentGateway
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentGatewayImplCopyWith<_$PaymentGatewayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
