// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$iconServiceHash() => r'197e89cf5ed6abd471eb0dcb5590f3c622329ee3';

/// See also [iconService].
@ProviderFor(iconService)
final iconServiceProvider = AutoDisposeProvider<IconService>.internal(
  iconService,
  name: r'iconServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$iconServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IconServiceRef = AutoDisposeProviderRef<IconService>;
String _$iconsHash() => r'bcaef0a815fcc2952ea53687e160d0021995c999';

/// See also [icons].
@ProviderFor(icons)
final iconsProvider = AutoDisposeStreamProvider<List<IconModel>>.internal(
  icons,
  name: r'iconsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$iconsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IconsRef = AutoDisposeStreamProviderRef<List<IconModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
