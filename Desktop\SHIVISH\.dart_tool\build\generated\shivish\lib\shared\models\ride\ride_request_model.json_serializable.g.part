// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RideRequestModel _$RideRequestModelFromJson(Map<String, dynamic> json) =>
    RideRequestModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      driverId: json['driverId'] as String?,
      status: $enumDecode(_$RideStatusEnumMap, json['status']),
      pickupAddress: json['pickupAddress'] as String,
      dropAddress: json['dropAddress'] as String,
      pickupLocation: const GeoPointConverter().fromJson(
        json['pickupLocation'] as Map<String, dynamic>,
      ),
      dropLocation: const GeoPointConverter().fromJson(
        json['dropLocation'] as Map<String, dynamic>,
      ),
      userName: json['userName'] as String?,
      userPhone: json['userPhone'] as String?,
      estimatedFare: (json['estimatedFare'] as num).toDouble(),
      actualFare: (json['actualFare'] as num?)?.toDouble(),
      distance: (json['distance'] as num).toDouble(),
      estimatedDuration: (json['estimatedDuration'] as num).toInt(),
      paymentMethod: $enumDecode(
        _$RidePaymentMethodEnumMap,
        json['paymentMethod'],
      ),
      isPaid: json['isPaid'] as bool,
      verificationPin: json['verificationPin'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      acceptedAt: json['acceptedAt'] == null
          ? null
          : DateTime.parse(json['acceptedAt'] as String),
      arrivedAt: json['arrivedAt'] == null
          ? null
          : DateTime.parse(json['arrivedAt'] as String),
      startedAt: json['startedAt'] == null
          ? null
          : DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      cancelledAt: json['cancelledAt'] == null
          ? null
          : DateTime.parse(json['cancelledAt'] as String),
      cancelReason: json['cancelReason'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      review: json['review'] as String?,
      vehicleType: json['vehicleType'] as String?,
      statusUpdates: (json['statusUpdates'] as List<dynamic>)
          .map((e) => e as Map<String, dynamic>)
          .toList(),
    );

Map<String, dynamic> _$RideRequestModelToJson(
  RideRequestModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'driverId': instance.driverId,
  'status': _$RideStatusEnumMap[instance.status]!,
  'pickupAddress': instance.pickupAddress,
  'dropAddress': instance.dropAddress,
  'pickupLocation': const GeoPointConverter().toJson(instance.pickupLocation),
  'dropLocation': const GeoPointConverter().toJson(instance.dropLocation),
  'userName': instance.userName,
  'userPhone': instance.userPhone,
  'estimatedFare': instance.estimatedFare,
  'actualFare': instance.actualFare,
  'distance': instance.distance,
  'estimatedDuration': instance.estimatedDuration,
  'paymentMethod': _$RidePaymentMethodEnumMap[instance.paymentMethod]!,
  'isPaid': instance.isPaid,
  'verificationPin': instance.verificationPin,
  'createdAt': instance.createdAt.toIso8601String(),
  'acceptedAt': instance.acceptedAt?.toIso8601String(),
  'arrivedAt': instance.arrivedAt?.toIso8601String(),
  'startedAt': instance.startedAt?.toIso8601String(),
  'completedAt': instance.completedAt?.toIso8601String(),
  'cancelledAt': instance.cancelledAt?.toIso8601String(),
  'cancelReason': instance.cancelReason,
  'rating': instance.rating,
  'review': instance.review,
  'vehicleType': instance.vehicleType,
  'statusUpdates': instance.statusUpdates,
};

const _$RideStatusEnumMap = {
  RideStatus.pending: 'pending',
  RideStatus.accepted: 'accepted',
  RideStatus.arrived: 'arrived',
  RideStatus.inProgress: 'inProgress',
  RideStatus.completed: 'completed',
  RideStatus.cancelled: 'cancelled',
  RideStatus.expired: 'expired',
};

const _$RidePaymentMethodEnumMap = {
  RidePaymentMethod.cash: 'cash',
  RidePaymentMethod.card: 'card',
  RidePaymentMethod.wallet: 'wallet',
  RidePaymentMethod.upi: 'upi',
};
