// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BackupConfigModelImpl _$$BackupConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$BackupConfigModelImpl(
  storageLocation: json['storageLocation'] as String,
  backupFrequencyHours: (json['backupFrequencyHours'] as num).toInt(),
  retentionDays: (json['retentionDays'] as num).toInt(),
  autoBackup: json['autoBackup'] as bool,
);

Map<String, dynamic> _$$BackupConfigModelImplToJson(
  _$BackupConfigModelImpl instance,
) => <String, dynamic>{
  'storageLocation': instance.storageLocation,
  'backupFrequencyHours': instance.backupFrequencyHours,
  'retentionDays': instance.retentionDays,
  'autoBackup': instance.autoBackup,
};
