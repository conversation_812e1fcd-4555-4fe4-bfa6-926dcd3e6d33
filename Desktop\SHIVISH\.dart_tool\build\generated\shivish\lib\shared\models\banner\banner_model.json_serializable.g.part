// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BannerModelImpl _$$BannerModelImplFromJson(Map<String, dynamic> json) =>
    _$BannerModelImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      imageUrl: json['imageUrl'] as String,
      actionType: json['actionType'] as String,
      actionData: json['actionData'] as String?,
      priority: (json['priority'] as num).toInt(),
      isActive: json['isActive'] as bool,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      submitterId: json['submitterId'] as String,
      submitterRole: json['submitterRole'] as String,
      status:
          $enumDecodeNullable(_$BannerStatusEnumMap, json['status']) ??
          BannerStatus.pending,
      rejectionReason: json['rejectionReason'] as String?,
    );

Map<String, dynamic> _$$BannerModelImplToJson(_$BannerModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'actionType': instance.actionType,
      'actionData': instance.actionData,
      'priority': instance.priority,
      'isActive': instance.isActive,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'submitterId': instance.submitterId,
      'submitterRole': instance.submitterRole,
      'status': _$BannerStatusEnumMap[instance.status]!,
      'rejectionReason': instance.rejectionReason,
    };

const _$BannerStatusEnumMap = {
  BannerStatus.pending: 'pending',
  BannerStatus.approved: 'approved',
  BannerStatus.rejected: 'rejected',
};
