// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hospital_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HospitalModelImpl _$$HospitalModelImplFromJson(
  Map<String, dynamic> json,
) => _$HospitalModelImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  address: Address.fromJson(json['address'] as Map<String, dynamic>),
  phone: json['phone'] as String,
  email: json['email'] as String,
  website: json['website'] as String?,
  logoUrl: json['logoUrl'] as String?,
  images:
      (json['images'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  departments:
      (json['departments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  facilities:
      (json['facilities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  doctors:
      (json['doctors'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  isVerified: json['isVerified'] as bool? ?? false,
  verifiedAt: json['verifiedAt'] == null
      ? null
      : DateTime.parse(json['verifiedAt'] as String),
  location: LocationModel.fromJson(json['location'] as Map<String, dynamic>),
  type: $enumDecode(_$HospitalTypeEnumMap, json['type']),
  establishedDate: DateTime.parse(json['establishedDate'] as String),
  licenseNumber: json['licenseNumber'] as String,
  licenseExpiryDate: DateTime.parse(json['licenseExpiryDate'] as String),
  isActive: json['isActive'] as bool,
  rating: (json['rating'] as num).toDouble(),
  totalReviews: (json['totalReviews'] as num).toInt(),
  registrationNumber: json['registrationNumber'] as String?,
  gstNumber: json['gstNumber'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
  deletedAt: json['deletedAt'] == null
      ? null
      : DateTime.parse(json['deletedAt'] as String),
);

Map<String, dynamic> _$$HospitalModelImplToJson(_$HospitalModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'address': instance.address.toJson(),
      'phone': instance.phone,
      'email': instance.email,
      'website': instance.website,
      'logoUrl': instance.logoUrl,
      'images': instance.images,
      'departments': instance.departments,
      'facilities': instance.facilities,
      'doctors': instance.doctors,
      'isVerified': instance.isVerified,
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
      'location': instance.location.toJson(),
      'type': _$HospitalTypeEnumMap[instance.type]!,
      'establishedDate': instance.establishedDate.toIso8601String(),
      'licenseNumber': instance.licenseNumber,
      'licenseExpiryDate': instance.licenseExpiryDate.toIso8601String(),
      'isActive': instance.isActive,
      'rating': instance.rating,
      'totalReviews': instance.totalReviews,
      'registrationNumber': instance.registrationNumber,
      'gstNumber': instance.gstNumber,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'deletedAt': instance.deletedAt?.toIso8601String(),
    };

const _$HospitalTypeEnumMap = {
  HospitalType.general: 'general',
  HospitalType.speciality: 'speciality',
  HospitalType.multiSpeciality: 'multi_speciality',
  HospitalType.clinic: 'clinic',
  HospitalType.nursingHome: 'nursing_home',
  HospitalType.diagnosticCenter: 'diagnostic_center',
  HospitalType.other: 'other',
};
