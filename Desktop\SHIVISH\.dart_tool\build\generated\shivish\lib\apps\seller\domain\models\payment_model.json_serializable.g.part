// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentSummaryImpl _$$PaymentSummaryImplFromJson(Map<String, dynamic> json) =>
    _$PaymentSummaryImpl(
      totalBalance: (json['totalBalance'] as num).toDouble(),
      pendingAmount: (json['pendingAmount'] as num).toDouble(),
      availableAmount: (json['availableAmount'] as num).toDouble(),
    );

Map<String, dynamic> _$$PaymentSummaryImplToJson(
  _$PaymentSummaryImpl instance,
) => <String, dynamic>{
  'totalBalance': instance.totalBalance,
  'pendingAmount': instance.pendingAmount,
  'availableAmount': instance.availableAmount,
};

_$TransactionModelImpl _$$TransactionModelImplFromJson(
  Map<String, dynamic> json,
) => _$TransactionModelImpl(
  id: json['id'] as String,
  description: json['description'] as String,
  amount: (json['amount'] as num).toDouble(),
  date: DateTime.parse(json['date'] as String),
  isCredit: json['isCredit'] as bool,
  orderId: json['orderId'] as String?,
  settlementId: json['settlementId'] as String?,
);

Map<String, dynamic> _$$TransactionModelImplToJson(
  _$TransactionModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'description': instance.description,
  'amount': instance.amount,
  'date': instance.date.toIso8601String(),
  'isCredit': instance.isCredit,
  'orderId': instance.orderId,
  'settlementId': instance.settlementId,
};

_$SettlementModelImpl _$$SettlementModelImplFromJson(
  Map<String, dynamic> json,
) => _$SettlementModelImpl(
  id: json['id'] as String,
  amount: (json['amount'] as num).toDouble(),
  date: DateTime.parse(json['date'] as String),
  status: json['status'] as String,
  bankAccountId: json['bankAccountId'] as String?,
  transactionId: json['transactionId'] as String?,
  failureReason: json['failureReason'] as String?,
);

Map<String, dynamic> _$$SettlementModelImplToJson(
  _$SettlementModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'amount': instance.amount,
  'date': instance.date.toIso8601String(),
  'status': instance.status,
  'bankAccountId': instance.bankAccountId,
  'transactionId': instance.transactionId,
  'failureReason': instance.failureReason,
};

_$BankAccountModelImpl _$$BankAccountModelImplFromJson(
  Map<String, dynamic> json,
) => _$BankAccountModelImpl(
  id: json['id'] as String,
  accountNumber: json['accountNumber'] as String,
  bankName: json['bankName'] as String,
  accountHolderName: json['accountHolderName'] as String,
  ifscCode: json['ifscCode'] as String,
  isVerified: json['isVerified'] as bool? ?? false,
  isPrimary: json['isPrimary'] as bool? ?? false,
  verifiedAt: json['verifiedAt'] == null
      ? null
      : DateTime.parse(json['verifiedAt'] as String),
);

Map<String, dynamic> _$$BankAccountModelImplToJson(
  _$BankAccountModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'accountNumber': instance.accountNumber,
  'bankName': instance.bankName,
  'accountHolderName': instance.accountHolderName,
  'ifscCode': instance.ifscCode,
  'isVerified': instance.isVerified,
  'isPrimary': instance.isPrimary,
  'verifiedAt': instance.verifiedAt?.toIso8601String(),
};
