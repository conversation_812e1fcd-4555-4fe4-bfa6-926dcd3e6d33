// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$tonesHash() => r'6f4e20d434cf4bc48570942c8d6a5802354ddd71';

/// See also [tones].
@ProviderFor(tones)
final tonesProvider = AutoDisposeFutureProvider<List<ToneModel>>.internal(
  tones,
  name: r'tonesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$tonesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TonesRef = AutoDisposeFutureProviderRef<List<ToneModel>>;
String _$toneByIdHash() => r'a087a028a462e50522ff4e76a2097d5fdd4a6c8c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [toneById].
@ProviderFor(toneById)
const toneByIdProvider = ToneByIdFamily();

/// See also [toneById].
class ToneByIdFamily extends Family<AsyncValue<ToneModel?>> {
  /// See also [toneById].
  const ToneByIdFamily();

  /// See also [toneById].
  ToneByIdProvider call(String id) {
    return ToneByIdProvider(id);
  }

  @override
  ToneByIdProvider getProviderOverride(covariant ToneByIdProvider provider) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'toneByIdProvider';
}

/// See also [toneById].
class ToneByIdProvider extends AutoDisposeFutureProvider<ToneModel?> {
  /// See also [toneById].
  ToneByIdProvider(String id)
    : this._internal(
        (ref) => toneById(ref as ToneByIdRef, id),
        from: toneByIdProvider,
        name: r'toneByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$toneByIdHash,
        dependencies: ToneByIdFamily._dependencies,
        allTransitiveDependencies: ToneByIdFamily._allTransitiveDependencies,
        id: id,
      );

  ToneByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<ToneModel?> Function(ToneByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ToneByIdProvider._internal(
        (ref) => create(ref as ToneByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ToneModel?> createElement() {
    return _ToneByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ToneByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ToneByIdRef on AutoDisposeFutureProviderRef<ToneModel?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _ToneByIdProviderElement
    extends AutoDisposeFutureProviderElement<ToneModel?>
    with ToneByIdRef {
  _ToneByIdProviderElement(super.provider);

  @override
  String get id => (origin as ToneByIdProvider).id;
}

String _$tonesByCategoryHash() => r'02719fa407cf921f2e68eaa1444a341b5a96c12e';

/// See also [tonesByCategory].
@ProviderFor(tonesByCategory)
const tonesByCategoryProvider = TonesByCategoryFamily();

/// See also [tonesByCategory].
class TonesByCategoryFamily extends Family<AsyncValue<List<ToneModel>>> {
  /// See also [tonesByCategory].
  const TonesByCategoryFamily();

  /// See also [tonesByCategory].
  TonesByCategoryProvider call(String category) {
    return TonesByCategoryProvider(category);
  }

  @override
  TonesByCategoryProvider getProviderOverride(
    covariant TonesByCategoryProvider provider,
  ) {
    return call(provider.category);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'tonesByCategoryProvider';
}

/// See also [tonesByCategory].
class TonesByCategoryProvider
    extends AutoDisposeFutureProvider<List<ToneModel>> {
  /// See also [tonesByCategory].
  TonesByCategoryProvider(String category)
    : this._internal(
        (ref) => tonesByCategory(ref as TonesByCategoryRef, category),
        from: tonesByCategoryProvider,
        name: r'tonesByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$tonesByCategoryHash,
        dependencies: TonesByCategoryFamily._dependencies,
        allTransitiveDependencies:
            TonesByCategoryFamily._allTransitiveDependencies,
        category: category,
      );

  TonesByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.category,
  }) : super.internal();

  final String category;

  @override
  Override overrideWith(
    FutureOr<List<ToneModel>> Function(TonesByCategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TonesByCategoryProvider._internal(
        (ref) => create(ref as TonesByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        category: category,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ToneModel>> createElement() {
    return _TonesByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TonesByCategoryProvider && other.category == category;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, category.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TonesByCategoryRef on AutoDisposeFutureProviderRef<List<ToneModel>> {
  /// The parameter `category` of this provider.
  String get category;
}

class _TonesByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<ToneModel>>
    with TonesByCategoryRef {
  _TonesByCategoryProviderElement(super.provider);

  @override
  String get category => (origin as TonesByCategoryProvider).category;
}

String _$searchTonesHash() => r'219eaee59a3b567ab9e42a3b4e0a16139aa7bbec';

/// See also [searchTones].
@ProviderFor(searchTones)
const searchTonesProvider = SearchTonesFamily();

/// See also [searchTones].
class SearchTonesFamily extends Family<AsyncValue<List<ToneModel>>> {
  /// See also [searchTones].
  const SearchTonesFamily();

  /// See also [searchTones].
  SearchTonesProvider call(String query) {
    return SearchTonesProvider(query);
  }

  @override
  SearchTonesProvider getProviderOverride(
    covariant SearchTonesProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchTonesProvider';
}

/// See also [searchTones].
class SearchTonesProvider extends AutoDisposeFutureProvider<List<ToneModel>> {
  /// See also [searchTones].
  SearchTonesProvider(String query)
    : this._internal(
        (ref) => searchTones(ref as SearchTonesRef, query),
        from: searchTonesProvider,
        name: r'searchTonesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$searchTonesHash,
        dependencies: SearchTonesFamily._dependencies,
        allTransitiveDependencies: SearchTonesFamily._allTransitiveDependencies,
        query: query,
      );

  SearchTonesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<ToneModel>> Function(SearchTonesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchTonesProvider._internal(
        (ref) => create(ref as SearchTonesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ToneModel>> createElement() {
    return _SearchTonesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchTonesProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchTonesRef on AutoDisposeFutureProviderRef<List<ToneModel>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _SearchTonesProviderElement
    extends AutoDisposeFutureProviderElement<List<ToneModel>>
    with SearchTonesRef {
  _SearchTonesProviderElement(super.provider);

  @override
  String get query => (origin as SearchTonesProvider).query;
}

String _$pendingTonesHash() => r'61de48f0d22f91fb21771cd16c5981ba4a7882cc';

/// See also [pendingTones].
@ProviderFor(pendingTones)
final pendingTonesProvider =
    AutoDisposeFutureProvider<List<ToneModel>>.internal(
      pendingTones,
      name: r'pendingTonesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$pendingTonesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PendingTonesRef = AutoDisposeFutureProviderRef<List<ToneModel>>;
String _$uploadToneHash() => r'665c4f2a904b14e56ad86c45af6f2f030cbdc2d7';

/// See also [uploadTone].
@ProviderFor(uploadTone)
const uploadToneProvider = UploadToneFamily();

/// See also [uploadTone].
class UploadToneFamily extends Family<AsyncValue<void>> {
  /// See also [uploadTone].
  const UploadToneFamily();

  /// See also [uploadTone].
  UploadToneProvider call({
    required String toneName,
    required String category,
    required String filePath,
    required int duration,
    required bool isDefault,
    required String uploadedBy,
    String language = 'en',
    bool isMultiple = false,
    List<String> daysOfWeek = const [],
  }) {
    return UploadToneProvider(
      toneName: toneName,
      category: category,
      filePath: filePath,
      duration: duration,
      isDefault: isDefault,
      uploadedBy: uploadedBy,
      language: language,
      isMultiple: isMultiple,
      daysOfWeek: daysOfWeek,
    );
  }

  @override
  UploadToneProvider getProviderOverride(
    covariant UploadToneProvider provider,
  ) {
    return call(
      toneName: provider.toneName,
      category: provider.category,
      filePath: provider.filePath,
      duration: provider.duration,
      isDefault: provider.isDefault,
      uploadedBy: provider.uploadedBy,
      language: provider.language,
      isMultiple: provider.isMultiple,
      daysOfWeek: provider.daysOfWeek,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'uploadToneProvider';
}

/// See also [uploadTone].
class UploadToneProvider extends AutoDisposeFutureProvider<void> {
  /// See also [uploadTone].
  UploadToneProvider({
    required String toneName,
    required String category,
    required String filePath,
    required int duration,
    required bool isDefault,
    required String uploadedBy,
    String language = 'en',
    bool isMultiple = false,
    List<String> daysOfWeek = const [],
  }) : this._internal(
         (ref) => uploadTone(
           ref as UploadToneRef,
           toneName: toneName,
           category: category,
           filePath: filePath,
           duration: duration,
           isDefault: isDefault,
           uploadedBy: uploadedBy,
           language: language,
           isMultiple: isMultiple,
           daysOfWeek: daysOfWeek,
         ),
         from: uploadToneProvider,
         name: r'uploadToneProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$uploadToneHash,
         dependencies: UploadToneFamily._dependencies,
         allTransitiveDependencies: UploadToneFamily._allTransitiveDependencies,
         toneName: toneName,
         category: category,
         filePath: filePath,
         duration: duration,
         isDefault: isDefault,
         uploadedBy: uploadedBy,
         language: language,
         isMultiple: isMultiple,
         daysOfWeek: daysOfWeek,
       );

  UploadToneProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.toneName,
    required this.category,
    required this.filePath,
    required this.duration,
    required this.isDefault,
    required this.uploadedBy,
    required this.language,
    required this.isMultiple,
    required this.daysOfWeek,
  }) : super.internal();

  final String toneName;
  final String category;
  final String filePath;
  final int duration;
  final bool isDefault;
  final String uploadedBy;
  final String language;
  final bool isMultiple;
  final List<String> daysOfWeek;

  @override
  Override overrideWith(
    FutureOr<void> Function(UploadToneRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UploadToneProvider._internal(
        (ref) => create(ref as UploadToneRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        toneName: toneName,
        category: category,
        filePath: filePath,
        duration: duration,
        isDefault: isDefault,
        uploadedBy: uploadedBy,
        language: language,
        isMultiple: isMultiple,
        daysOfWeek: daysOfWeek,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _UploadToneProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UploadToneProvider &&
        other.toneName == toneName &&
        other.category == category &&
        other.filePath == filePath &&
        other.duration == duration &&
        other.isDefault == isDefault &&
        other.uploadedBy == uploadedBy &&
        other.language == language &&
        other.isMultiple == isMultiple &&
        other.daysOfWeek == daysOfWeek;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, toneName.hashCode);
    hash = _SystemHash.combine(hash, category.hashCode);
    hash = _SystemHash.combine(hash, filePath.hashCode);
    hash = _SystemHash.combine(hash, duration.hashCode);
    hash = _SystemHash.combine(hash, isDefault.hashCode);
    hash = _SystemHash.combine(hash, uploadedBy.hashCode);
    hash = _SystemHash.combine(hash, language.hashCode);
    hash = _SystemHash.combine(hash, isMultiple.hashCode);
    hash = _SystemHash.combine(hash, daysOfWeek.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UploadToneRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `toneName` of this provider.
  String get toneName;

  /// The parameter `category` of this provider.
  String get category;

  /// The parameter `filePath` of this provider.
  String get filePath;

  /// The parameter `duration` of this provider.
  int get duration;

  /// The parameter `isDefault` of this provider.
  bool get isDefault;

  /// The parameter `uploadedBy` of this provider.
  String get uploadedBy;

  /// The parameter `language` of this provider.
  String get language;

  /// The parameter `isMultiple` of this provider.
  bool get isMultiple;

  /// The parameter `daysOfWeek` of this provider.
  List<String> get daysOfWeek;
}

class _UploadToneProviderElement extends AutoDisposeFutureProviderElement<void>
    with UploadToneRef {
  _UploadToneProviderElement(super.provider);

  @override
  String get toneName => (origin as UploadToneProvider).toneName;
  @override
  String get category => (origin as UploadToneProvider).category;
  @override
  String get filePath => (origin as UploadToneProvider).filePath;
  @override
  int get duration => (origin as UploadToneProvider).duration;
  @override
  bool get isDefault => (origin as UploadToneProvider).isDefault;
  @override
  String get uploadedBy => (origin as UploadToneProvider).uploadedBy;
  @override
  String get language => (origin as UploadToneProvider).language;
  @override
  bool get isMultiple => (origin as UploadToneProvider).isMultiple;
  @override
  List<String> get daysOfWeek => (origin as UploadToneProvider).daysOfWeek;
}

String _$approveToneHash() => r'c2565dd4d4c9dc2612558660289707ab1e4773df';

/// See also [approveTone].
@ProviderFor(approveTone)
const approveToneProvider = ApproveToneFamily();

/// See also [approveTone].
class ApproveToneFamily extends Family<AsyncValue<void>> {
  /// See also [approveTone].
  const ApproveToneFamily();

  /// See also [approveTone].
  ApproveToneProvider call({required String toneId, required String adminId}) {
    return ApproveToneProvider(toneId: toneId, adminId: adminId);
  }

  @override
  ApproveToneProvider getProviderOverride(
    covariant ApproveToneProvider provider,
  ) {
    return call(toneId: provider.toneId, adminId: provider.adminId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'approveToneProvider';
}

/// See also [approveTone].
class ApproveToneProvider extends AutoDisposeFutureProvider<void> {
  /// See also [approveTone].
  ApproveToneProvider({required String toneId, required String adminId})
    : this._internal(
        (ref) => approveTone(
          ref as ApproveToneRef,
          toneId: toneId,
          adminId: adminId,
        ),
        from: approveToneProvider,
        name: r'approveToneProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$approveToneHash,
        dependencies: ApproveToneFamily._dependencies,
        allTransitiveDependencies: ApproveToneFamily._allTransitiveDependencies,
        toneId: toneId,
        adminId: adminId,
      );

  ApproveToneProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.toneId,
    required this.adminId,
  }) : super.internal();

  final String toneId;
  final String adminId;

  @override
  Override overrideWith(
    FutureOr<void> Function(ApproveToneRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ApproveToneProvider._internal(
        (ref) => create(ref as ApproveToneRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        toneId: toneId,
        adminId: adminId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _ApproveToneProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ApproveToneProvider &&
        other.toneId == toneId &&
        other.adminId == adminId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, toneId.hashCode);
    hash = _SystemHash.combine(hash, adminId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ApproveToneRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `toneId` of this provider.
  String get toneId;

  /// The parameter `adminId` of this provider.
  String get adminId;
}

class _ApproveToneProviderElement extends AutoDisposeFutureProviderElement<void>
    with ApproveToneRef {
  _ApproveToneProviderElement(super.provider);

  @override
  String get toneId => (origin as ApproveToneProvider).toneId;
  @override
  String get adminId => (origin as ApproveToneProvider).adminId;
}

String _$rejectToneHash() => r'd39c21f182cc7add6d024a9a8330dbfa0e8d9b3b';

/// See also [rejectTone].
@ProviderFor(rejectTone)
const rejectToneProvider = RejectToneFamily();

/// See also [rejectTone].
class RejectToneFamily extends Family<AsyncValue<void>> {
  /// See also [rejectTone].
  const RejectToneFamily();

  /// See also [rejectTone].
  RejectToneProvider call({
    required String toneId,
    required String adminId,
    required String reason,
  }) {
    return RejectToneProvider(toneId: toneId, adminId: adminId, reason: reason);
  }

  @override
  RejectToneProvider getProviderOverride(
    covariant RejectToneProvider provider,
  ) {
    return call(
      toneId: provider.toneId,
      adminId: provider.adminId,
      reason: provider.reason,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'rejectToneProvider';
}

/// See also [rejectTone].
class RejectToneProvider extends AutoDisposeFutureProvider<void> {
  /// See also [rejectTone].
  RejectToneProvider({
    required String toneId,
    required String adminId,
    required String reason,
  }) : this._internal(
         (ref) => rejectTone(
           ref as RejectToneRef,
           toneId: toneId,
           adminId: adminId,
           reason: reason,
         ),
         from: rejectToneProvider,
         name: r'rejectToneProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$rejectToneHash,
         dependencies: RejectToneFamily._dependencies,
         allTransitiveDependencies: RejectToneFamily._allTransitiveDependencies,
         toneId: toneId,
         adminId: adminId,
         reason: reason,
       );

  RejectToneProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.toneId,
    required this.adminId,
    required this.reason,
  }) : super.internal();

  final String toneId;
  final String adminId;
  final String reason;

  @override
  Override overrideWith(
    FutureOr<void> Function(RejectToneRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RejectToneProvider._internal(
        (ref) => create(ref as RejectToneRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        toneId: toneId,
        adminId: adminId,
        reason: reason,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _RejectToneProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RejectToneProvider &&
        other.toneId == toneId &&
        other.adminId == adminId &&
        other.reason == reason;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, toneId.hashCode);
    hash = _SystemHash.combine(hash, adminId.hashCode);
    hash = _SystemHash.combine(hash, reason.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RejectToneRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `toneId` of this provider.
  String get toneId;

  /// The parameter `adminId` of this provider.
  String get adminId;

  /// The parameter `reason` of this provider.
  String get reason;
}

class _RejectToneProviderElement extends AutoDisposeFutureProviderElement<void>
    with RejectToneRef {
  _RejectToneProviderElement(super.provider);

  @override
  String get toneId => (origin as RejectToneProvider).toneId;
  @override
  String get adminId => (origin as RejectToneProvider).adminId;
  @override
  String get reason => (origin as RejectToneProvider).reason;
}

String _$playToneHash() => r'f002a51eadabfa27a4b70f10bc6f5391782483dd';

/// See also [playTone].
@ProviderFor(playTone)
const playToneProvider = PlayToneFamily();

/// See also [playTone].
class PlayToneFamily extends Family<AsyncValue<void>> {
  /// See also [playTone].
  const PlayToneFamily();

  /// See also [playTone].
  PlayToneProvider call(String toneId) {
    return PlayToneProvider(toneId);
  }

  @override
  PlayToneProvider getProviderOverride(covariant PlayToneProvider provider) {
    return call(provider.toneId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'playToneProvider';
}

/// See also [playTone].
class PlayToneProvider extends AutoDisposeFutureProvider<void> {
  /// See also [playTone].
  PlayToneProvider(String toneId)
    : this._internal(
        (ref) => playTone(ref as PlayToneRef, toneId),
        from: playToneProvider,
        name: r'playToneProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$playToneHash,
        dependencies: PlayToneFamily._dependencies,
        allTransitiveDependencies: PlayToneFamily._allTransitiveDependencies,
        toneId: toneId,
      );

  PlayToneProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.toneId,
  }) : super.internal();

  final String toneId;

  @override
  Override overrideWith(FutureOr<void> Function(PlayToneRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: PlayToneProvider._internal(
        (ref) => create(ref as PlayToneRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        toneId: toneId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _PlayToneProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PlayToneProvider && other.toneId == toneId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, toneId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PlayToneRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `toneId` of this provider.
  String get toneId;
}

class _PlayToneProviderElement extends AutoDisposeFutureProviderElement<void>
    with PlayToneRef {
  _PlayToneProviderElement(super.provider);

  @override
  String get toneId => (origin as PlayToneProvider).toneId;
}

String _$stopToneHash() => r'd0e89ca6f5e84bec57ff7b22aad9b372ddf8c4c3';

/// See also [stopTone].
@ProviderFor(stopTone)
final stopToneProvider = AutoDisposeFutureProvider<void>.internal(
  stopTone,
  name: r'stopToneProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$stopToneHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef StopToneRef = AutoDisposeFutureProviderRef<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
