// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppointmentModelImpl _$$AppointmentModelImplFromJson(
  Map<String, dynamic> json,
) => _$AppointmentModelImpl(
  id: json['id'] as String,
  appointmentNumber: json['appointmentNumber'] as String,
  patientId: json['patientId'] as String,
  patientName: json['patientName'] as String,
  patientPhone: json['patientPhone'] as String,
  patientEmail: json['patientEmail'] as String?,
  hospitalId: json['hospitalId'] as String,
  hospitalName: json['hospitalName'] as String,
  doctorId: json['doctorId'] as String,
  doctorName: json['doctorName'] as String,
  departmentId: json['departmentId'] as String,
  departmentName: json['departmentName'] as String,
  appointmentDate: DateTime.parse(json['appointmentDate'] as String),
  timeSlotId: json['timeSlotId'] as String,
  startTime: json['startTime'] as String,
  endTime: json['endTime'] as String,
  type: $enumDecode(_$AppointmentTypeEnumMap, json['type']),
  status: $enumDecode(_$AppointmentStatusEnumMap, json['status']),
  symptoms: json['symptoms'] as String,
  diagnosis: json['diagnosis'] as String?,
  prescription: json['prescription'] as String?,
  notes: json['notes'] as String?,
  consultationFee: (json['consultationFee'] as num).toDouble(),
  isPaid: json['isPaid'] as bool,
  paymentId: json['paymentId'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
  deletedAt: json['deletedAt'] == null
      ? null
      : DateTime.parse(json['deletedAt'] as String),
);

Map<String, dynamic> _$$AppointmentModelImplToJson(
  _$AppointmentModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'appointmentNumber': instance.appointmentNumber,
  'patientId': instance.patientId,
  'patientName': instance.patientName,
  'patientPhone': instance.patientPhone,
  'patientEmail': instance.patientEmail,
  'hospitalId': instance.hospitalId,
  'hospitalName': instance.hospitalName,
  'doctorId': instance.doctorId,
  'doctorName': instance.doctorName,
  'departmentId': instance.departmentId,
  'departmentName': instance.departmentName,
  'appointmentDate': instance.appointmentDate.toIso8601String(),
  'timeSlotId': instance.timeSlotId,
  'startTime': instance.startTime,
  'endTime': instance.endTime,
  'type': _$AppointmentTypeEnumMap[instance.type]!,
  'status': _$AppointmentStatusEnumMap[instance.status]!,
  'symptoms': instance.symptoms,
  'diagnosis': instance.diagnosis,
  'prescription': instance.prescription,
  'notes': instance.notes,
  'consultationFee': instance.consultationFee,
  'isPaid': instance.isPaid,
  'paymentId': instance.paymentId,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
  'deletedAt': instance.deletedAt?.toIso8601String(),
};

const _$AppointmentTypeEnumMap = {
  AppointmentType.consultation: 'consultation',
  AppointmentType.followUp: 'follow_up',
  AppointmentType.emergency: 'emergency',
  AppointmentType.routineCheckup: 'routine_checkup',
  AppointmentType.labTest: 'lab_test',
  AppointmentType.vaccination: 'vaccination',
  AppointmentType.other: 'other',
};

const _$AppointmentStatusEnumMap = {
  AppointmentStatus.pending: 'pending',
  AppointmentStatus.confirmed: 'confirmed',
  AppointmentStatus.completed: 'completed',
  AppointmentStatus.cancelled: 'cancelled',
  AppointmentStatus.rescheduled: 'rescheduled',
  AppointmentStatus.noShow: 'no_show',
};
