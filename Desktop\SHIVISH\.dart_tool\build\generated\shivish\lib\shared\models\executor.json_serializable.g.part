// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExecutorImpl _$$ExecutorImplFromJson(Map<String, dynamic> json) =>
    _$ExecutorImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      role: json['role'] as String,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool,
      isActive: json['isActive'] as bool? ?? false,
      profileImage: json['profileImage'] as String?,
      address: json['address'] as String?,
      notes: json['notes'] as String?,
      performanceMetrics: json['performanceMetrics'] as Map<String, dynamic>?,
      assignedRegions: (json['assignedRegions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$ExecutorImplToJson(_$ExecutorImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'role': instance.role,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'isActive': instance.isActive,
      'profileImage': instance.profileImage,
      'address': instance.address,
      'notes': instance.notes,
      'performanceMetrics': instance.performanceMetrics,
      'assignedRegions': instance.assignedRegions,
      'permissions': instance.permissions,
    };
