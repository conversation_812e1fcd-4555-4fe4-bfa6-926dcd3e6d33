// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hospital_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

HospitalModel _$HospitalModelFromJson(Map<String, dynamic> json) {
  return _HospitalModel.fromJson(json);
}

/// @nodoc
mixin _$HospitalModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  Address get address => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;
  String? get logoUrl => throw _privateConstructorUsedError;
  List<String> get images => throw _privateConstructorUsedError;
  List<String> get departments => throw _privateConstructorUsedError;
  List<String> get facilities => throw _privateConstructorUsedError;
  List<String> get doctors => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  DateTime? get verifiedAt => throw _privateConstructorUsedError;
  LocationModel get location => throw _privateConstructorUsedError;
  HospitalType get type => throw _privateConstructorUsedError;
  DateTime get establishedDate => throw _privateConstructorUsedError;
  String get licenseNumber => throw _privateConstructorUsedError;
  DateTime get licenseExpiryDate => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get totalReviews => throw _privateConstructorUsedError;
  String? get registrationNumber => throw _privateConstructorUsedError;
  String? get gstNumber => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  DateTime? get deletedAt => throw _privateConstructorUsedError;

  /// Serializes this HospitalModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HospitalModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HospitalModelCopyWith<HospitalModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HospitalModelCopyWith<$Res> {
  factory $HospitalModelCopyWith(
    HospitalModel value,
    $Res Function(HospitalModel) then,
  ) = _$HospitalModelCopyWithImpl<$Res, HospitalModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    Address address,
    String phone,
    String email,
    String? website,
    String? logoUrl,
    List<String> images,
    List<String> departments,
    List<String> facilities,
    List<String> doctors,
    bool isVerified,
    DateTime? verifiedAt,
    LocationModel location,
    HospitalType type,
    DateTime establishedDate,
    String licenseNumber,
    DateTime licenseExpiryDate,
    bool isActive,
    double rating,
    int totalReviews,
    String? registrationNumber,
    String? gstNumber,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });

  $AddressCopyWith<$Res> get address;
  $LocationModelCopyWith<$Res> get location;
}

/// @nodoc
class _$HospitalModelCopyWithImpl<$Res, $Val extends HospitalModel>
    implements $HospitalModelCopyWith<$Res> {
  _$HospitalModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HospitalModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? address = null,
    Object? phone = null,
    Object? email = null,
    Object? website = freezed,
    Object? logoUrl = freezed,
    Object? images = null,
    Object? departments = null,
    Object? facilities = null,
    Object? doctors = null,
    Object? isVerified = null,
    Object? verifiedAt = freezed,
    Object? location = null,
    Object? type = null,
    Object? establishedDate = null,
    Object? licenseNumber = null,
    Object? licenseExpiryDate = null,
    Object? isActive = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? registrationNumber = freezed,
    Object? gstNumber = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            address: null == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as Address,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            website: freezed == website
                ? _value.website
                : website // ignore: cast_nullable_to_non_nullable
                      as String?,
            logoUrl: freezed == logoUrl
                ? _value.logoUrl
                : logoUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            images: null == images
                ? _value.images
                : images // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            departments: null == departments
                ? _value.departments
                : departments // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            facilities: null == facilities
                ? _value.facilities
                : facilities // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            doctors: null == doctors
                ? _value.doctors
                : doctors // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            verifiedAt: freezed == verifiedAt
                ? _value.verifiedAt
                : verifiedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            location: null == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as LocationModel,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as HospitalType,
            establishedDate: null == establishedDate
                ? _value.establishedDate
                : establishedDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            licenseNumber: null == licenseNumber
                ? _value.licenseNumber
                : licenseNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            licenseExpiryDate: null == licenseExpiryDate
                ? _value.licenseExpiryDate
                : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            totalReviews: null == totalReviews
                ? _value.totalReviews
                : totalReviews // ignore: cast_nullable_to_non_nullable
                      as int,
            registrationNumber: freezed == registrationNumber
                ? _value.registrationNumber
                : registrationNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            gstNumber: freezed == gstNumber
                ? _value.gstNumber
                : gstNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            deletedAt: freezed == deletedAt
                ? _value.deletedAt
                : deletedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }

  /// Create a copy of HospitalModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AddressCopyWith<$Res> get address {
    return $AddressCopyWith<$Res>(_value.address, (value) {
      return _then(_value.copyWith(address: value) as $Val);
    });
  }

  /// Create a copy of HospitalModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationModelCopyWith<$Res> get location {
    return $LocationModelCopyWith<$Res>(_value.location, (value) {
      return _then(_value.copyWith(location: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HospitalModelImplCopyWith<$Res>
    implements $HospitalModelCopyWith<$Res> {
  factory _$$HospitalModelImplCopyWith(
    _$HospitalModelImpl value,
    $Res Function(_$HospitalModelImpl) then,
  ) = __$$HospitalModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    Address address,
    String phone,
    String email,
    String? website,
    String? logoUrl,
    List<String> images,
    List<String> departments,
    List<String> facilities,
    List<String> doctors,
    bool isVerified,
    DateTime? verifiedAt,
    LocationModel location,
    HospitalType type,
    DateTime establishedDate,
    String licenseNumber,
    DateTime licenseExpiryDate,
    bool isActive,
    double rating,
    int totalReviews,
    String? registrationNumber,
    String? gstNumber,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });

  @override
  $AddressCopyWith<$Res> get address;
  @override
  $LocationModelCopyWith<$Res> get location;
}

/// @nodoc
class __$$HospitalModelImplCopyWithImpl<$Res>
    extends _$HospitalModelCopyWithImpl<$Res, _$HospitalModelImpl>
    implements _$$HospitalModelImplCopyWith<$Res> {
  __$$HospitalModelImplCopyWithImpl(
    _$HospitalModelImpl _value,
    $Res Function(_$HospitalModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of HospitalModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? address = null,
    Object? phone = null,
    Object? email = null,
    Object? website = freezed,
    Object? logoUrl = freezed,
    Object? images = null,
    Object? departments = null,
    Object? facilities = null,
    Object? doctors = null,
    Object? isVerified = null,
    Object? verifiedAt = freezed,
    Object? location = null,
    Object? type = null,
    Object? establishedDate = null,
    Object? licenseNumber = null,
    Object? licenseExpiryDate = null,
    Object? isActive = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? registrationNumber = freezed,
    Object? gstNumber = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _$HospitalModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as Address,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        website: freezed == website
            ? _value.website
            : website // ignore: cast_nullable_to_non_nullable
                  as String?,
        logoUrl: freezed == logoUrl
            ? _value.logoUrl
            : logoUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        images: null == images
            ? _value._images
            : images // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        departments: null == departments
            ? _value._departments
            : departments // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        facilities: null == facilities
            ? _value._facilities
            : facilities // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        doctors: null == doctors
            ? _value._doctors
            : doctors // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        verifiedAt: freezed == verifiedAt
            ? _value.verifiedAt
            : verifiedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        location: null == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as LocationModel,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as HospitalType,
        establishedDate: null == establishedDate
            ? _value.establishedDate
            : establishedDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        licenseNumber: null == licenseNumber
            ? _value.licenseNumber
            : licenseNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        licenseExpiryDate: null == licenseExpiryDate
            ? _value.licenseExpiryDate
            : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        totalReviews: null == totalReviews
            ? _value.totalReviews
            : totalReviews // ignore: cast_nullable_to_non_nullable
                  as int,
        registrationNumber: freezed == registrationNumber
            ? _value.registrationNumber
            : registrationNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        gstNumber: freezed == gstNumber
            ? _value.gstNumber
            : gstNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        deletedAt: freezed == deletedAt
            ? _value.deletedAt
            : deletedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$HospitalModelImpl implements _HospitalModel {
  const _$HospitalModelImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.phone,
    required this.email,
    this.website,
    this.logoUrl,
    final List<String> images = const [],
    final List<String> departments = const [],
    final List<String> facilities = const [],
    final List<String> doctors = const [],
    this.isVerified = false,
    this.verifiedAt,
    required this.location,
    required this.type,
    required this.establishedDate,
    required this.licenseNumber,
    required this.licenseExpiryDate,
    required this.isActive,
    required this.rating,
    required this.totalReviews,
    this.registrationNumber,
    this.gstNumber,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    this.deletedAt,
  }) : _images = images,
       _departments = departments,
       _facilities = facilities,
       _doctors = doctors;

  factory _$HospitalModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$HospitalModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final Address address;
  @override
  final String phone;
  @override
  final String email;
  @override
  final String? website;
  @override
  final String? logoUrl;
  final List<String> _images;
  @override
  @JsonKey()
  List<String> get images {
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_images);
  }

  final List<String> _departments;
  @override
  @JsonKey()
  List<String> get departments {
    if (_departments is EqualUnmodifiableListView) return _departments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_departments);
  }

  final List<String> _facilities;
  @override
  @JsonKey()
  List<String> get facilities {
    if (_facilities is EqualUnmodifiableListView) return _facilities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_facilities);
  }

  final List<String> _doctors;
  @override
  @JsonKey()
  List<String> get doctors {
    if (_doctors is EqualUnmodifiableListView) return _doctors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_doctors);
  }

  @override
  @JsonKey()
  final bool isVerified;
  @override
  final DateTime? verifiedAt;
  @override
  final LocationModel location;
  @override
  final HospitalType type;
  @override
  final DateTime establishedDate;
  @override
  final String licenseNumber;
  @override
  final DateTime licenseExpiryDate;
  @override
  final bool isActive;
  @override
  final double rating;
  @override
  final int totalReviews;
  @override
  final String? registrationNumber;
  @override
  final String? gstNumber;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  final DateTime? deletedAt;

  @override
  String toString() {
    return 'HospitalModel(id: $id, name: $name, description: $description, address: $address, phone: $phone, email: $email, website: $website, logoUrl: $logoUrl, images: $images, departments: $departments, facilities: $facilities, doctors: $doctors, isVerified: $isVerified, verifiedAt: $verifiedAt, location: $location, type: $type, establishedDate: $establishedDate, licenseNumber: $licenseNumber, licenseExpiryDate: $licenseExpiryDate, isActive: $isActive, rating: $rating, totalReviews: $totalReviews, registrationNumber: $registrationNumber, gstNumber: $gstNumber, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HospitalModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.website, website) || other.website == website) &&
            (identical(other.logoUrl, logoUrl) || other.logoUrl == logoUrl) &&
            const DeepCollectionEquality().equals(other._images, _images) &&
            const DeepCollectionEquality().equals(
              other._departments,
              _departments,
            ) &&
            const DeepCollectionEquality().equals(
              other._facilities,
              _facilities,
            ) &&
            const DeepCollectionEquality().equals(other._doctors, _doctors) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.verifiedAt, verifiedAt) ||
                other.verifiedAt == verifiedAt) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.establishedDate, establishedDate) ||
                other.establishedDate == establishedDate) &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.licenseExpiryDate, licenseExpiryDate) ||
                other.licenseExpiryDate == licenseExpiryDate) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalReviews, totalReviews) ||
                other.totalReviews == totalReviews) &&
            (identical(other.registrationNumber, registrationNumber) ||
                other.registrationNumber == registrationNumber) &&
            (identical(other.gstNumber, gstNumber) ||
                other.gstNumber == gstNumber) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    description,
    address,
    phone,
    email,
    website,
    logoUrl,
    const DeepCollectionEquality().hash(_images),
    const DeepCollectionEquality().hash(_departments),
    const DeepCollectionEquality().hash(_facilities),
    const DeepCollectionEquality().hash(_doctors),
    isVerified,
    verifiedAt,
    location,
    type,
    establishedDate,
    licenseNumber,
    licenseExpiryDate,
    isActive,
    rating,
    totalReviews,
    registrationNumber,
    gstNumber,
    createdAt,
    updatedAt,
    isDeleted,
    deletedAt,
  ]);

  /// Create a copy of HospitalModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HospitalModelImplCopyWith<_$HospitalModelImpl> get copyWith =>
      __$$HospitalModelImplCopyWithImpl<_$HospitalModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HospitalModelImplToJson(this);
  }
}

abstract class _HospitalModel implements HospitalModel {
  const factory _HospitalModel({
    required final String id,
    required final String name,
    required final String description,
    required final Address address,
    required final String phone,
    required final String email,
    final String? website,
    final String? logoUrl,
    final List<String> images,
    final List<String> departments,
    final List<String> facilities,
    final List<String> doctors,
    final bool isVerified,
    final DateTime? verifiedAt,
    required final LocationModel location,
    required final HospitalType type,
    required final DateTime establishedDate,
    required final String licenseNumber,
    required final DateTime licenseExpiryDate,
    required final bool isActive,
    required final double rating,
    required final int totalReviews,
    final String? registrationNumber,
    final String? gstNumber,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    final DateTime? deletedAt,
  }) = _$HospitalModelImpl;

  factory _HospitalModel.fromJson(Map<String, dynamic> json) =
      _$HospitalModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  Address get address;
  @override
  String get phone;
  @override
  String get email;
  @override
  String? get website;
  @override
  String? get logoUrl;
  @override
  List<String> get images;
  @override
  List<String> get departments;
  @override
  List<String> get facilities;
  @override
  List<String> get doctors;
  @override
  bool get isVerified;
  @override
  DateTime? get verifiedAt;
  @override
  LocationModel get location;
  @override
  HospitalType get type;
  @override
  DateTime get establishedDate;
  @override
  String get licenseNumber;
  @override
  DateTime get licenseExpiryDate;
  @override
  bool get isActive;
  @override
  double get rating;
  @override
  int get totalReviews;
  @override
  String? get registrationNumber;
  @override
  String? get gstNumber;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  DateTime? get deletedAt;

  /// Create a copy of HospitalModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HospitalModelImplCopyWith<_$HospitalModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
