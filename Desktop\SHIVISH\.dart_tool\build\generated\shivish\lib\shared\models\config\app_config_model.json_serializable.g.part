// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppConfigModelImpl _$$AppConfigModelImplFromJson(Map<String, dynamic> json) =>
    _$AppConfigModelImpl(
      supportEmail: json['supportEmail'] as String,
      placeholderImageUrl: json['placeholderImageUrl'] as String,
      apiBaseUrl: json['apiBaseUrl'] as String,
      chatUrl: json['chatUrl'] as String,
      paymentApiUrl: json['paymentApiUrl'] as String,
      razorpayKeyId: json['razorpayKeyId'] as String?,
      razorpayKeySecret: json['razorpayKeySecret'] as String?,
    );

Map<String, dynamic> _$$AppConfigModelImplToJson(
  _$AppConfigModelImpl instance,
) => <String, dynamic>{
  'supportEmail': instance.supportEmail,
  'placeholderImageUrl': instance.placeholderImageUrl,
  'apiBaseUrl': instance.apiBaseUrl,
  'chatUrl': instance.chatUrl,
  'paymentApiUrl': instance.paymentApiUrl,
  'razorpayKeyId': instance.razorpayKeyId,
  'razorpayKeySecret': instance.razorpayKeySecret,
};
