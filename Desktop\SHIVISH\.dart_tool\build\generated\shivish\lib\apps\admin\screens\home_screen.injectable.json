[{"type": {"import": "package:shivish/apps/admin/screens/home_screen.dart", "name": "HomeScreen", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/screens/home_screen.dart", "name": "HomeScreen", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/notification_service.dart", "name": "NotificationService", "isNullable": true, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_notificationService", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:flutter/material.dart", "name": "Key", "isNullable": true, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:flutter/widgets.dart", "package:flutter/foundation.dart", "package:flutter/cupertino.dart", "package:flutter/rendering.dart", "package:flutter/src/widgets/framework.dart", "package:flutter/src/foundation/key.dart", "package:flutter/semantics.dart", "package:flutter/src/semantics/semantics.dart"]}, "instanceName": null, "paramName": "key", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]