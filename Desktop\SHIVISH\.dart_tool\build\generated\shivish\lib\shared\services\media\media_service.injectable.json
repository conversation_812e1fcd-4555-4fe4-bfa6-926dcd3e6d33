[{"type": {"import": "package:shivish/shared/services/media/media_service.dart", "name": "MediaService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/shared/services/media/media_service.dart", "name": "MediaService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": true, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "firestore", "isFactoryParam": false, "isPositional": false}, {"type": {"import": "package:firebase_storage/firebase_storage.dart", "name": "FirebaseStorage", "isNullable": true, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "storage", "isFactoryParam": false, "isPositional": false}], "constructorName": "", "orderPosition": 0}]