// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recommended_products_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecommendedProductsModelImpl _$$RecommendedProductsModelImplFromJson(
  Map<String, dynamic> json,
) => _$RecommendedProductsModelImpl(
  productId: json['productId'] as String,
  recommendedProductIds: (json['recommendedProductIds'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  type: $enumDecode(_$RecommendationTypeEnumMap, json['type']),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$RecommendedProductsModelImplToJson(
  _$RecommendedProductsModelImpl instance,
) => <String, dynamic>{
  'productId': instance.productId,
  'recommendedProductIds': instance.recommendedProductIds,
  'type': _$RecommendationTypeEnumMap[instance.type]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$RecommendationTypeEnumMap = {
  RecommendationType.similar: 0,
  RecommendationType.frequentlyBoughtTogether: 1,
  RecommendationType.sponsored: 2,
  RecommendationType.trending: 3,
  RecommendationType.newArrivals: 4,
};
