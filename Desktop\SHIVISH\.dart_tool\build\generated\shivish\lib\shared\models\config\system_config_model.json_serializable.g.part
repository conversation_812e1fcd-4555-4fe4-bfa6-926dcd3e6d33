// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SystemConfigModelImpl _$$SystemConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$SystemConfigModelImpl(
  security: SecurityConfigModel.fromJson(
    json['security'] as Map<String, dynamic>,
  ),
  backup: BackupConfigModel.fromJson(json['backup'] as Map<String, dynamic>),
  refund: RefundConfigModel.from<PERSON><PERSON>(json['refund'] as Map<String, dynamic>),
  ai: AIConfigModel.fromJson(json['ai'] as Map<String, dynamic>),
  voiceCommand: VoiceCommandConfigModel.from<PERSON><PERSON>(
    json['voiceCommand'] as Map<String, dynamic>,
  ),
  chatbot: ChatbotConfigModel.fromJson(json['chatbot'] as Map<String, dynamic>),
  additionalSettings: json['additionalSettings'] as Map<String, dynamic>,
);

Map<String, dynamic> _$$SystemConfigModelImplToJson(
  _$SystemConfigModelImpl instance,
) => <String, dynamic>{
  'security': instance.security.toJson(),
  'backup': instance.backup.toJson(),
  'refund': instance.refund.toJson(),
  'ai': instance.ai.toJson(),
  'voiceCommand': instance.voiceCommand.toJson(),
  'chatbot': instance.chatbot.toJson(),
  'additionalSettings': instance.additionalSettings,
};
