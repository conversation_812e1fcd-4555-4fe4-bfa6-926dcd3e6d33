// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LabTestModelImpl _$$LabTestModelImplFromJson(Map<String, dynamic> json) =>
    _$LabTestModelImpl(
      id: json['id'] as String,
      testNumber: json['testNumber'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      hospitalId: json['hospitalId'] as String,
      hospitalName: json['hospitalName'] as String,
      patientId: json['patientId'] as String,
      patientName: json['patientName'] as String,
      patientPhone: json['patientPhone'] as String,
      patientEmail: json['patientEmail'] as String?,
      doctorId: json['doctorId'] as String?,
      doctorName: json['doctorName'] as String?,
      testDate: DateTime.parse(json['testDate'] as String),
      timeSlot: json['timeSlot'] as String?,
      status: $enumDecode(_$LabTestStatusEnumMap, json['status']),
      category: $enumDecode(_$LabTestCategoryEnumMap, json['category']),
      price: (json['price'] as num).toDouble(),
      isPaid: json['isPaid'] as bool,
      paymentId: json['paymentId'] as String?,
      reportUrl: json['reportUrl'] as String?,
      notes: json['notes'] as String?,
      sampleType: json['sampleType'] as String?,
      turnaroundTime: (json['turnaroundTime'] as num?)?.toInt(),
      instructions: json['instructions'] as String?,
      isAvailable: json['isAvailable'] as bool?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
    );

Map<String, dynamic> _$$LabTestModelImplToJson(_$LabTestModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'testNumber': instance.testNumber,
      'name': instance.name,
      'description': instance.description,
      'hospitalId': instance.hospitalId,
      'hospitalName': instance.hospitalName,
      'patientId': instance.patientId,
      'patientName': instance.patientName,
      'patientPhone': instance.patientPhone,
      'patientEmail': instance.patientEmail,
      'doctorId': instance.doctorId,
      'doctorName': instance.doctorName,
      'testDate': instance.testDate.toIso8601String(),
      'timeSlot': instance.timeSlot,
      'status': _$LabTestStatusEnumMap[instance.status]!,
      'category': _$LabTestCategoryEnumMap[instance.category]!,
      'price': instance.price,
      'isPaid': instance.isPaid,
      'paymentId': instance.paymentId,
      'reportUrl': instance.reportUrl,
      'notes': instance.notes,
      'sampleType': instance.sampleType,
      'turnaroundTime': instance.turnaroundTime,
      'instructions': instance.instructions,
      'isAvailable': instance.isAvailable,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'deletedAt': instance.deletedAt?.toIso8601String(),
    };

const _$LabTestStatusEnumMap = {
  LabTestStatus.pending: 'pending',
  LabTestStatus.scheduled: 'scheduled',
  LabTestStatus.sampleCollected: 'sample_collected',
  LabTestStatus.processing: 'processing',
  LabTestStatus.completed: 'completed',
  LabTestStatus.cancelled: 'cancelled',
};

const _$LabTestCategoryEnumMap = {
  LabTestCategory.hematology: 'hematology',
  LabTestCategory.biochemistry: 'biochemistry',
  LabTestCategory.microbiology: 'microbiology',
  LabTestCategory.immunology: 'immunology',
  LabTestCategory.pathology: 'pathology',
  LabTestCategory.radiology: 'radiology',
  LabTestCategory.cardiology: 'cardiology',
  LabTestCategory.neurology: 'neurology',
  LabTestCategory.other: 'other',
};
