// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'phonepe_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PhonePeConfigModelImpl _$$PhonePeConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$PhonePeConfigModelImpl(
  isEnabled: json['isEnabled'] as bool? ?? false,
  merchantId: json['merchantId'] as String? ?? '',
  apiKey: json['apiKey'] as String? ?? '',
  saltKey: json['saltKey'] as String? ?? '',
  apiEndpoint:
      json['apiEndpoint'] as String? ??
      'https://api-preprod.phonepe.com/apis/pg-sandbox',
  isProduction: json['isProduction'] as bool? ?? false,
  callbackUrl: json['callbackUrl'] as String? ?? '',
  redirectUrl: json['redirectUrl'] as String? ?? '',
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$PhonePeConfigModelImplToJson(
  _$PhonePeConfigModelImpl instance,
) => <String, dynamic>{
  'isEnabled': instance.isEnabled,
  'merchantId': instance.merchantId,
  'apiKey': instance.apiKey,
  'saltKey': instance.saltKey,
  'apiEndpoint': instance.apiEndpoint,
  'isProduction': instance.isProduction,
  'callbackUrl': instance.callbackUrl,
  'redirectUrl': instance.redirectUrl,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};
