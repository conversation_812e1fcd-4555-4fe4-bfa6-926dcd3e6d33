// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SecurityConfigModelImpl _$$SecurityConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$SecurityConfigModelImpl(
  sessionTimeoutMinutes: (json['sessionTimeoutMinutes'] as num).toInt(),
  maxLoginAttempts: (json['maxLoginAttempts'] as num).toInt(),
  passwordMinLength: (json['passwordMinLength'] as num).toInt(),
  passwordExpiryDays: (json['passwordExpiryDays'] as num).toInt(),
);

Map<String, dynamic> _$$SecurityConfigModelImplToJson(
  _$SecurityConfigModelImpl instance,
) => <String, dynamic>{
  'sessionTimeoutMinutes': instance.sessionTimeoutMinutes,
  'maxLoginAttempts': instance.maxLoginAttempts,
  'passwordMinLength': instance.passwordMinLength,
  'passwordExpiryDays': instance.passwordExpiryDays,
};
