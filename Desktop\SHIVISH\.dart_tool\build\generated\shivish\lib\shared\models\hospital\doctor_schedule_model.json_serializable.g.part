// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DoctorScheduleModelImpl _$$DoctorScheduleModelImplFromJson(
  Map<String, dynamic> json,
) => _$DoctorScheduleModelImpl(
  id: json['id'] as String,
  doctorId: json['doctorId'] as String,
  weeklySchedule: (json['weeklySchedule'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(k, DaySchedule.fromJson(e as Map<String, dynamic>)),
  ),
  holidays: (json['holidays'] as List<dynamic>)
      .map((e) => DateTime.parse(e as String))
      .toList(),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$DoctorScheduleModelImplToJson(
  _$DoctorScheduleModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'doctorId': instance.doctorId,
  'weeklySchedule': instance.weeklySchedule.map(
    (k, e) => MapEntry(k, e.toJson()),
  ),
  'holidays': instance.holidays.map((e) => e.toIso8601String()).toList(),
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

_$DayScheduleImpl _$$DayScheduleImplFromJson(Map<String, dynamic> json) =>
    _$DayScheduleImpl(
      isAvailable: json['isAvailable'] as bool,
      slots: (json['slots'] as List<dynamic>)
          .map((e) => TimeSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$DayScheduleImplToJson(_$DayScheduleImpl instance) =>
    <String, dynamic>{
      'isAvailable': instance.isAvailable,
      'slots': instance.slots.map((e) => e.toJson()).toList(),
    };

_$TimeSlotImpl _$$TimeSlotImplFromJson(Map<String, dynamic> json) =>
    _$TimeSlotImpl(
      id: json['id'] as String,
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      maxPatients: (json['maxPatients'] as num).toInt(),
      bookedPatients: (json['bookedPatients'] as num?)?.toInt() ?? 0,
      isAvailable: json['isAvailable'] as bool? ?? true,
    );

Map<String, dynamic> _$$TimeSlotImplToJson(_$TimeSlotImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'maxPatients': instance.maxPatients,
      'bookedPatients': instance.bookedPatients,
      'isAvailable': instance.isAvailable,
    };
