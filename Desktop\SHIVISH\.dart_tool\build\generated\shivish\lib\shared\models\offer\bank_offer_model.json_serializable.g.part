// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BankOfferModelImpl _$$BankOfferModelImplFromJson(Map<String, dynamic> json) =>
    _$BankOfferModelImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      bankName: json['bankName'] as String,
      bankLogoUrl: json['bankLogoUrl'] as String?,
      offerType: $enumDecode(_$BankOfferTypeEnumMap, json['offerType']),
      value: (json['value'] as num).toDouble(),
      code: json['code'] as String?,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      applicableProductIds: (json['applicableProductIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      applicableCategories: (json['applicableCategories'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      minimumPurchaseAmount: (json['minimumPurchaseAmount'] as num).toDouble(),
      maximumDiscountAmount: (json['maximumDiscountAmount'] as num?)
          ?.toDouble(),
      isActive: json['isActive'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      termsAndConditions: json['termsAndConditions'] as String?,
    );

Map<String, dynamic> _$$BankOfferModelImplToJson(
  _$BankOfferModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'bankName': instance.bankName,
  'bankLogoUrl': instance.bankLogoUrl,
  'offerType': _$BankOfferTypeEnumMap[instance.offerType]!,
  'value': instance.value,
  'code': instance.code,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate.toIso8601String(),
  'applicableProductIds': instance.applicableProductIds,
  'applicableCategories': instance.applicableCategories,
  'minimumPurchaseAmount': instance.minimumPurchaseAmount,
  'maximumDiscountAmount': instance.maximumDiscountAmount,
  'isActive': instance.isActive,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
  'termsAndConditions': instance.termsAndConditions,
};

const _$BankOfferTypeEnumMap = {
  BankOfferType.cashback: 0,
  BankOfferType.discount: 1,
  BankOfferType.emi: 2,
  BankOfferType.reward: 3,
};
