// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'panchangam_notification_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PanchangamNotificationSettings _$PanchangamNotificationSettingsFromJson(
  Map<String, dynamic> json,
) {
  return _PanchangamNotificationSettings.fromJson(json);
}

/// @nodoc
mixin _$PanchangamNotificationSettings {
  bool get isEnabled => throw _privateConstructorUsedError;
  int get notificationHour => throw _privateConstructorUsedError;
  int get notificationMinute => throw _privateConstructorUsedError;
  String get language => throw _privateConstructorUsedError;
  String get location => throw _privateConstructorUsedError;

  /// Serializes this PanchangamNotificationSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PanchangamNotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PanchangamNotificationSettingsCopyWith<PanchangamNotificationSettings>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PanchangamNotificationSettingsCopyWith<$Res> {
  factory $PanchangamNotificationSettingsCopyWith(
    PanchangamNotificationSettings value,
    $Res Function(PanchangamNotificationSettings) then,
  ) =
      _$PanchangamNotificationSettingsCopyWithImpl<
        $Res,
        PanchangamNotificationSettings
      >;
  @useResult
  $Res call({
    bool isEnabled,
    int notificationHour,
    int notificationMinute,
    String language,
    String location,
  });
}

/// @nodoc
class _$PanchangamNotificationSettingsCopyWithImpl<
  $Res,
  $Val extends PanchangamNotificationSettings
>
    implements $PanchangamNotificationSettingsCopyWith<$Res> {
  _$PanchangamNotificationSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PanchangamNotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEnabled = null,
    Object? notificationHour = null,
    Object? notificationMinute = null,
    Object? language = null,
    Object? location = null,
  }) {
    return _then(
      _value.copyWith(
            isEnabled: null == isEnabled
                ? _value.isEnabled
                : isEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            notificationHour: null == notificationHour
                ? _value.notificationHour
                : notificationHour // ignore: cast_nullable_to_non_nullable
                      as int,
            notificationMinute: null == notificationMinute
                ? _value.notificationMinute
                : notificationMinute // ignore: cast_nullable_to_non_nullable
                      as int,
            language: null == language
                ? _value.language
                : language // ignore: cast_nullable_to_non_nullable
                      as String,
            location: null == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PanchangamNotificationSettingsImplCopyWith<$Res>
    implements $PanchangamNotificationSettingsCopyWith<$Res> {
  factory _$$PanchangamNotificationSettingsImplCopyWith(
    _$PanchangamNotificationSettingsImpl value,
    $Res Function(_$PanchangamNotificationSettingsImpl) then,
  ) = __$$PanchangamNotificationSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool isEnabled,
    int notificationHour,
    int notificationMinute,
    String language,
    String location,
  });
}

/// @nodoc
class __$$PanchangamNotificationSettingsImplCopyWithImpl<$Res>
    extends
        _$PanchangamNotificationSettingsCopyWithImpl<
          $Res,
          _$PanchangamNotificationSettingsImpl
        >
    implements _$$PanchangamNotificationSettingsImplCopyWith<$Res> {
  __$$PanchangamNotificationSettingsImplCopyWithImpl(
    _$PanchangamNotificationSettingsImpl _value,
    $Res Function(_$PanchangamNotificationSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PanchangamNotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEnabled = null,
    Object? notificationHour = null,
    Object? notificationMinute = null,
    Object? language = null,
    Object? location = null,
  }) {
    return _then(
      _$PanchangamNotificationSettingsImpl(
        isEnabled: null == isEnabled
            ? _value.isEnabled
            : isEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        notificationHour: null == notificationHour
            ? _value.notificationHour
            : notificationHour // ignore: cast_nullable_to_non_nullable
                  as int,
        notificationMinute: null == notificationMinute
            ? _value.notificationMinute
            : notificationMinute // ignore: cast_nullable_to_non_nullable
                  as int,
        language: null == language
            ? _value.language
            : language // ignore: cast_nullable_to_non_nullable
                  as String,
        location: null == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PanchangamNotificationSettingsImpl
    implements _PanchangamNotificationSettings {
  const _$PanchangamNotificationSettingsImpl({
    this.isEnabled = true,
    this.notificationHour = 7,
    this.notificationMinute = 0,
    this.language = 'en',
    this.location = 'Mumbai',
  });

  factory _$PanchangamNotificationSettingsImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$PanchangamNotificationSettingsImplFromJson(json);

  @override
  @JsonKey()
  final bool isEnabled;
  @override
  @JsonKey()
  final int notificationHour;
  @override
  @JsonKey()
  final int notificationMinute;
  @override
  @JsonKey()
  final String language;
  @override
  @JsonKey()
  final String location;

  @override
  String toString() {
    return 'PanchangamNotificationSettings(isEnabled: $isEnabled, notificationHour: $notificationHour, notificationMinute: $notificationMinute, language: $language, location: $location)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PanchangamNotificationSettingsImpl &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled) &&
            (identical(other.notificationHour, notificationHour) ||
                other.notificationHour == notificationHour) &&
            (identical(other.notificationMinute, notificationMinute) ||
                other.notificationMinute == notificationMinute) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.location, location) ||
                other.location == location));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    isEnabled,
    notificationHour,
    notificationMinute,
    language,
    location,
  );

  /// Create a copy of PanchangamNotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PanchangamNotificationSettingsImplCopyWith<
    _$PanchangamNotificationSettingsImpl
  >
  get copyWith =>
      __$$PanchangamNotificationSettingsImplCopyWithImpl<
        _$PanchangamNotificationSettingsImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PanchangamNotificationSettingsImplToJson(this);
  }
}

abstract class _PanchangamNotificationSettings
    implements PanchangamNotificationSettings {
  const factory _PanchangamNotificationSettings({
    final bool isEnabled,
    final int notificationHour,
    final int notificationMinute,
    final String language,
    final String location,
  }) = _$PanchangamNotificationSettingsImpl;

  factory _PanchangamNotificationSettings.fromJson(Map<String, dynamic> json) =
      _$PanchangamNotificationSettingsImpl.fromJson;

  @override
  bool get isEnabled;
  @override
  int get notificationHour;
  @override
  int get notificationMinute;
  @override
  String get language;
  @override
  String get location;

  /// Create a copy of PanchangamNotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PanchangamNotificationSettingsImplCopyWith<
    _$PanchangamNotificationSettingsImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}
