// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'whatsapp_contact_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessageTemplateImpl _$$MessageTemplateImplFromJson(
  Map<String, dynamic> json,
) => _$MessageTemplateImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  content: json['content'] as String,
  type: $enumDecode(_$MessageTemplateTypeEnumMap, json['type']),
  description: json['description'] as String?,
  isActive: json['isActive'] as bool? ?? true,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
);

Map<String, dynamic> _$$MessageTemplateImplToJson(
  _$MessageTemplateImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'content': instance.content,
  'type': _$MessageTemplateTypeEnumMap[instance.type]!,
  'description': instance.description,
  'isActive': instance.isActive,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
};

const _$MessageTemplateTypeEnumMap = {
  MessageTemplateType.birthday: 0,
  MessageTemplateType.anniversary: 1,
  MessageTemplateType.festival: 2,
  MessageTemplateType.promotion: 3,
  MessageTemplateType.reminder: 4,
  MessageTemplateType.custom: 5,
};

_$MessageHistoryImpl _$$MessageHistoryImplFromJson(Map<String, dynamic> json) =>
    _$MessageHistoryImpl(
      id: json['id'] as String,
      message: json['message'] as String,
      status: $enumDecode(_$MessageStatusEnumMap, json['status']),
      templateId: json['templateId'] as String?,
      sentAt: json['sentAt'] == null
          ? null
          : DateTime.parse(json['sentAt'] as String),
      deliveredAt: json['deliveredAt'] == null
          ? null
          : DateTime.parse(json['deliveredAt'] as String),
      readAt: json['readAt'] == null
          ? null
          : DateTime.parse(json['readAt'] as String),
      errorMessage: json['errorMessage'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$MessageHistoryImplToJson(
  _$MessageHistoryImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'message': instance.message,
  'status': _$MessageStatusEnumMap[instance.status]!,
  'templateId': instance.templateId,
  'sentAt': instance.sentAt?.toIso8601String(),
  'deliveredAt': instance.deliveredAt?.toIso8601String(),
  'readAt': instance.readAt?.toIso8601String(),
  'errorMessage': instance.errorMessage,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
};

const _$MessageStatusEnumMap = {
  MessageStatus.pending: 0,
  MessageStatus.sent: 1,
  MessageStatus.delivered: 2,
  MessageStatus.read: 3,
  MessageStatus.failed: 4,
};

_$WhatsAppContactModelImpl _$$WhatsAppContactModelImplFromJson(
  Map<String, dynamic> json,
) => _$WhatsAppContactModelImpl(
  id: json['id'] as String? ?? '',
  name: json['name'] as String,
  phoneNumber: json['phoneNumber'] as String,
  userId: json['userId'] as String,
  type: $enumDecode(_$ContactTypeEnumMap, json['type']),
  status:
      $enumDecodeNullable(_$ContactStatusEnumMap, json['status']) ??
      ContactStatus.active,
  isDeleted: json['isDeleted'] as bool? ?? false,
  messageTemplates:
      (json['messageTemplates'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  messageHistory:
      (json['messageHistory'] as List<dynamic>?)
          ?.map((e) => MessageHistory.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  profilePicture: json['profilePicture'] as String?,
  about: json['about'] as String?,
  notes: json['notes'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$WhatsAppContactModelImplToJson(
  _$WhatsAppContactModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'phoneNumber': instance.phoneNumber,
  'userId': instance.userId,
  'type': _$ContactTypeEnumMap[instance.type]!,
  'status': _$ContactStatusEnumMap[instance.status]!,
  'isDeleted': instance.isDeleted,
  'messageTemplates': instance.messageTemplates,
  'messageHistory': instance.messageHistory.map((e) => e.toJson()).toList(),
  'metadata': instance.metadata,
  'profilePicture': instance.profilePicture,
  'about': instance.about,
  'notes': instance.notes,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$ContactTypeEnumMap = {
  ContactType.personal: 0,
  ContactType.business: 1,
  ContactType.group: 2,
  ContactType.broadcast: 3,
};

const _$ContactStatusEnumMap = {
  ContactStatus.active: 0,
  ContactStatus.blocked: 1,
  ContactStatus.deleted: 2,
  ContactStatus.pending: 3,
};
