// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'doctor_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

DoctorModel _$DoctorModelFromJson(Map<String, dynamic> json) {
  return _DoctorModel.fromJson(json);
}

/// @nodoc
mixin _$DoctorModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String get hospitalId => throw _privateConstructorUsedError;
  String get departmentId => throw _privateConstructorUsedError;
  String get designation => throw _privateConstructorUsedError;
  String get qualification => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<DoctorSpecialty> get specialties => throw _privateConstructorUsedError;
  int get experienceYears => throw _privateConstructorUsedError;
  String get registrationNumber => throw _privateConstructorUsedError;
  String get profileImage => throw _privateConstructorUsedError;
  bool get isAvailable => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get totalReviews => throw _privateConstructorUsedError;
  double get consultationFee => throw _privateConstructorUsedError;
  int get consultationDuration =>
      throw _privateConstructorUsedError; // in minutes
  DoctorScheduleModel? get schedule => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  DateTime? get deletedAt => throw _privateConstructorUsedError;

  /// Serializes this DoctorModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DoctorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DoctorModelCopyWith<DoctorModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DoctorModelCopyWith<$Res> {
  factory $DoctorModelCopyWith(
    DoctorModel value,
    $Res Function(DoctorModel) then,
  ) = _$DoctorModelCopyWithImpl<$Res, DoctorModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String email,
    String phone,
    String hospitalId,
    String departmentId,
    String designation,
    String qualification,
    String description,
    List<DoctorSpecialty> specialties,
    int experienceYears,
    String registrationNumber,
    String profileImage,
    bool isAvailable,
    bool isVerified,
    double rating,
    int totalReviews,
    double consultationFee,
    int consultationDuration,
    DoctorScheduleModel? schedule,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });

  $DoctorScheduleModelCopyWith<$Res>? get schedule;
}

/// @nodoc
class _$DoctorModelCopyWithImpl<$Res, $Val extends DoctorModel>
    implements $DoctorModelCopyWith<$Res> {
  _$DoctorModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DoctorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? hospitalId = null,
    Object? departmentId = null,
    Object? designation = null,
    Object? qualification = null,
    Object? description = null,
    Object? specialties = null,
    Object? experienceYears = null,
    Object? registrationNumber = null,
    Object? profileImage = null,
    Object? isAvailable = null,
    Object? isVerified = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? consultationFee = null,
    Object? consultationDuration = null,
    Object? schedule = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            hospitalId: null == hospitalId
                ? _value.hospitalId
                : hospitalId // ignore: cast_nullable_to_non_nullable
                      as String,
            departmentId: null == departmentId
                ? _value.departmentId
                : departmentId // ignore: cast_nullable_to_non_nullable
                      as String,
            designation: null == designation
                ? _value.designation
                : designation // ignore: cast_nullable_to_non_nullable
                      as String,
            qualification: null == qualification
                ? _value.qualification
                : qualification // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            specialties: null == specialties
                ? _value.specialties
                : specialties // ignore: cast_nullable_to_non_nullable
                      as List<DoctorSpecialty>,
            experienceYears: null == experienceYears
                ? _value.experienceYears
                : experienceYears // ignore: cast_nullable_to_non_nullable
                      as int,
            registrationNumber: null == registrationNumber
                ? _value.registrationNumber
                : registrationNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            profileImage: null == profileImage
                ? _value.profileImage
                : profileImage // ignore: cast_nullable_to_non_nullable
                      as String,
            isAvailable: null == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            totalReviews: null == totalReviews
                ? _value.totalReviews
                : totalReviews // ignore: cast_nullable_to_non_nullable
                      as int,
            consultationFee: null == consultationFee
                ? _value.consultationFee
                : consultationFee // ignore: cast_nullable_to_non_nullable
                      as double,
            consultationDuration: null == consultationDuration
                ? _value.consultationDuration
                : consultationDuration // ignore: cast_nullable_to_non_nullable
                      as int,
            schedule: freezed == schedule
                ? _value.schedule
                : schedule // ignore: cast_nullable_to_non_nullable
                      as DoctorScheduleModel?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            deletedAt: freezed == deletedAt
                ? _value.deletedAt
                : deletedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }

  /// Create a copy of DoctorModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DoctorScheduleModelCopyWith<$Res>? get schedule {
    if (_value.schedule == null) {
      return null;
    }

    return $DoctorScheduleModelCopyWith<$Res>(_value.schedule!, (value) {
      return _then(_value.copyWith(schedule: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DoctorModelImplCopyWith<$Res>
    implements $DoctorModelCopyWith<$Res> {
  factory _$$DoctorModelImplCopyWith(
    _$DoctorModelImpl value,
    $Res Function(_$DoctorModelImpl) then,
  ) = __$$DoctorModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String email,
    String phone,
    String hospitalId,
    String departmentId,
    String designation,
    String qualification,
    String description,
    List<DoctorSpecialty> specialties,
    int experienceYears,
    String registrationNumber,
    String profileImage,
    bool isAvailable,
    bool isVerified,
    double rating,
    int totalReviews,
    double consultationFee,
    int consultationDuration,
    DoctorScheduleModel? schedule,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });

  @override
  $DoctorScheduleModelCopyWith<$Res>? get schedule;
}

/// @nodoc
class __$$DoctorModelImplCopyWithImpl<$Res>
    extends _$DoctorModelCopyWithImpl<$Res, _$DoctorModelImpl>
    implements _$$DoctorModelImplCopyWith<$Res> {
  __$$DoctorModelImplCopyWithImpl(
    _$DoctorModelImpl _value,
    $Res Function(_$DoctorModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DoctorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? hospitalId = null,
    Object? departmentId = null,
    Object? designation = null,
    Object? qualification = null,
    Object? description = null,
    Object? specialties = null,
    Object? experienceYears = null,
    Object? registrationNumber = null,
    Object? profileImage = null,
    Object? isAvailable = null,
    Object? isVerified = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? consultationFee = null,
    Object? consultationDuration = null,
    Object? schedule = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _$DoctorModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        hospitalId: null == hospitalId
            ? _value.hospitalId
            : hospitalId // ignore: cast_nullable_to_non_nullable
                  as String,
        departmentId: null == departmentId
            ? _value.departmentId
            : departmentId // ignore: cast_nullable_to_non_nullable
                  as String,
        designation: null == designation
            ? _value.designation
            : designation // ignore: cast_nullable_to_non_nullable
                  as String,
        qualification: null == qualification
            ? _value.qualification
            : qualification // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        specialties: null == specialties
            ? _value._specialties
            : specialties // ignore: cast_nullable_to_non_nullable
                  as List<DoctorSpecialty>,
        experienceYears: null == experienceYears
            ? _value.experienceYears
            : experienceYears // ignore: cast_nullable_to_non_nullable
                  as int,
        registrationNumber: null == registrationNumber
            ? _value.registrationNumber
            : registrationNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        profileImage: null == profileImage
            ? _value.profileImage
            : profileImage // ignore: cast_nullable_to_non_nullable
                  as String,
        isAvailable: null == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        totalReviews: null == totalReviews
            ? _value.totalReviews
            : totalReviews // ignore: cast_nullable_to_non_nullable
                  as int,
        consultationFee: null == consultationFee
            ? _value.consultationFee
            : consultationFee // ignore: cast_nullable_to_non_nullable
                  as double,
        consultationDuration: null == consultationDuration
            ? _value.consultationDuration
            : consultationDuration // ignore: cast_nullable_to_non_nullable
                  as int,
        schedule: freezed == schedule
            ? _value.schedule
            : schedule // ignore: cast_nullable_to_non_nullable
                  as DoctorScheduleModel?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        deletedAt: freezed == deletedAt
            ? _value.deletedAt
            : deletedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DoctorModelImpl implements _DoctorModel {
  const _$DoctorModelImpl({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.hospitalId,
    required this.departmentId,
    required this.designation,
    required this.qualification,
    required this.description,
    required final List<DoctorSpecialty> specialties,
    required this.experienceYears,
    required this.registrationNumber,
    required this.profileImage,
    required this.isAvailable,
    required this.isVerified,
    required this.rating,
    required this.totalReviews,
    required this.consultationFee,
    required this.consultationDuration,
    this.schedule,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    this.deletedAt,
  }) : _specialties = specialties;

  factory _$DoctorModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DoctorModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String email;
  @override
  final String phone;
  @override
  final String hospitalId;
  @override
  final String departmentId;
  @override
  final String designation;
  @override
  final String qualification;
  @override
  final String description;
  final List<DoctorSpecialty> _specialties;
  @override
  List<DoctorSpecialty> get specialties {
    if (_specialties is EqualUnmodifiableListView) return _specialties;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_specialties);
  }

  @override
  final int experienceYears;
  @override
  final String registrationNumber;
  @override
  final String profileImage;
  @override
  final bool isAvailable;
  @override
  final bool isVerified;
  @override
  final double rating;
  @override
  final int totalReviews;
  @override
  final double consultationFee;
  @override
  final int consultationDuration;
  // in minutes
  @override
  final DoctorScheduleModel? schedule;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  final DateTime? deletedAt;

  @override
  String toString() {
    return 'DoctorModel(id: $id, name: $name, email: $email, phone: $phone, hospitalId: $hospitalId, departmentId: $departmentId, designation: $designation, qualification: $qualification, description: $description, specialties: $specialties, experienceYears: $experienceYears, registrationNumber: $registrationNumber, profileImage: $profileImage, isAvailable: $isAvailable, isVerified: $isVerified, rating: $rating, totalReviews: $totalReviews, consultationFee: $consultationFee, consultationDuration: $consultationDuration, schedule: $schedule, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DoctorModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.hospitalId, hospitalId) ||
                other.hospitalId == hospitalId) &&
            (identical(other.departmentId, departmentId) ||
                other.departmentId == departmentId) &&
            (identical(other.designation, designation) ||
                other.designation == designation) &&
            (identical(other.qualification, qualification) ||
                other.qualification == qualification) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(
              other._specialties,
              _specialties,
            ) &&
            (identical(other.experienceYears, experienceYears) ||
                other.experienceYears == experienceYears) &&
            (identical(other.registrationNumber, registrationNumber) ||
                other.registrationNumber == registrationNumber) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalReviews, totalReviews) ||
                other.totalReviews == totalReviews) &&
            (identical(other.consultationFee, consultationFee) ||
                other.consultationFee == consultationFee) &&
            (identical(other.consultationDuration, consultationDuration) ||
                other.consultationDuration == consultationDuration) &&
            (identical(other.schedule, schedule) ||
                other.schedule == schedule) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    email,
    phone,
    hospitalId,
    departmentId,
    designation,
    qualification,
    description,
    const DeepCollectionEquality().hash(_specialties),
    experienceYears,
    registrationNumber,
    profileImage,
    isAvailable,
    isVerified,
    rating,
    totalReviews,
    consultationFee,
    consultationDuration,
    schedule,
    createdAt,
    updatedAt,
    isDeleted,
    deletedAt,
  ]);

  /// Create a copy of DoctorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DoctorModelImplCopyWith<_$DoctorModelImpl> get copyWith =>
      __$$DoctorModelImplCopyWithImpl<_$DoctorModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DoctorModelImplToJson(this);
  }
}

abstract class _DoctorModel implements DoctorModel {
  const factory _DoctorModel({
    required final String id,
    required final String name,
    required final String email,
    required final String phone,
    required final String hospitalId,
    required final String departmentId,
    required final String designation,
    required final String qualification,
    required final String description,
    required final List<DoctorSpecialty> specialties,
    required final int experienceYears,
    required final String registrationNumber,
    required final String profileImage,
    required final bool isAvailable,
    required final bool isVerified,
    required final double rating,
    required final int totalReviews,
    required final double consultationFee,
    required final int consultationDuration,
    final DoctorScheduleModel? schedule,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    final DateTime? deletedAt,
  }) = _$DoctorModelImpl;

  factory _DoctorModel.fromJson(Map<String, dynamic> json) =
      _$DoctorModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get email;
  @override
  String get phone;
  @override
  String get hospitalId;
  @override
  String get departmentId;
  @override
  String get designation;
  @override
  String get qualification;
  @override
  String get description;
  @override
  List<DoctorSpecialty> get specialties;
  @override
  int get experienceYears;
  @override
  String get registrationNumber;
  @override
  String get profileImage;
  @override
  bool get isAvailable;
  @override
  bool get isVerified;
  @override
  double get rating;
  @override
  int get totalReviews;
  @override
  double get consultationFee;
  @override
  int get consultationDuration; // in minutes
  @override
  DoctorScheduleModel? get schedule;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  DateTime? get deletedAt;

  /// Create a copy of DoctorModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DoctorModelImplCopyWith<_$DoctorModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
