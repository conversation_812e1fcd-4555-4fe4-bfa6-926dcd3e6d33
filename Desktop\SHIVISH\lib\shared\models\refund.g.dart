// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'refund.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RefundImpl _$$RefundImplFromJson(Map<String, dynamic> json) => _$RefundImpl(
  id: json['id'] as String,
  orderId: json['orderId'] as String,
  userId: json['userId'] as String,
  sellerId: json['sellerId'] as String,
  amount: (json['amount'] as num).toDouble(),
  reason: json['reason'] as String,
  status: json['status'] as String,
  createdAt: json['createdAt'] as String,
  updatedAt: json['updatedAt'] as String,
  notes: json['notes'] as String?,
  isPartial: json['isPartial'] as bool? ?? false,
  partialAmount: (json['partialAmount'] as num?)?.toDouble() ?? 0,
  partialReason: json['partialReason'] as String?,
  refundMethod: json['refundMethod'] as Map<String, dynamic>? ?? const {},
  trackingInfo: json['trackingInfo'] as Map<String, dynamic>? ?? const {},
  approvedBy: json['approvedBy'] as String?,
  approvedAt: json['approvedAt'] as String?,
  rejectedBy: json['rejectedBy'] as String?,
  rejectedAt: json['rejectedAt'] as String?,
  rejectionReason: json['rejectionReason'] as String?,
  processedBy: json['processedBy'] as String?,
  processedAt: json['processedAt'] as String?,
  transactionId: json['transactionId'] as String?,
  isDisputed: json['isDisputed'] as bool? ?? false,
  disputeReason: json['disputeReason'] as String?,
  disputeCreatedAt: json['disputeCreatedAt'] as String?,
  disputeResolvedBy: json['disputeResolvedBy'] as String?,
  disputeResolvedAt: json['disputeResolvedAt'] as String?,
  disputeResolution: json['disputeResolution'] as String?,
  auditLog:
      (json['auditLog'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList() ??
      const [],
);

Map<String, dynamic> _$$RefundImplToJson(_$RefundImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'userId': instance.userId,
      'sellerId': instance.sellerId,
      'amount': instance.amount,
      'reason': instance.reason,
      'status': instance.status,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'notes': instance.notes,
      'isPartial': instance.isPartial,
      'partialAmount': instance.partialAmount,
      'partialReason': instance.partialReason,
      'refundMethod': instance.refundMethod,
      'trackingInfo': instance.trackingInfo,
      'approvedBy': instance.approvedBy,
      'approvedAt': instance.approvedAt,
      'rejectedBy': instance.rejectedBy,
      'rejectedAt': instance.rejectedAt,
      'rejectionReason': instance.rejectionReason,
      'processedBy': instance.processedBy,
      'processedAt': instance.processedAt,
      'transactionId': instance.transactionId,
      'isDisputed': instance.isDisputed,
      'disputeReason': instance.disputeReason,
      'disputeCreatedAt': instance.disputeCreatedAt,
      'disputeResolvedBy': instance.disputeResolvedBy,
      'disputeResolvedAt': instance.disputeResolvedAt,
      'disputeResolution': instance.disputeResolution,
      'auditLog': instance.auditLog,
    };
