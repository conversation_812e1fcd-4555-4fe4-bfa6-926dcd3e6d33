// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BuyerImpl _$$BuyerImplFromJson(Map<String, dynamic> json) => _$BuyerImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  phoneNumber: json['phoneNumber'] as String?,
  profileImage: json['profileImage'] as String?,
  addresses:
      (json['addresses'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  savedProducts:
      (json['savedProducts'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  orders:
      (json['orders'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  isEmailVerified: json['isEmailVerified'] as bool? ?? false,
  isPhoneVerified: json['isPhoneVerified'] as bool? ?? false,
  isProfileComplete: json['isProfileComplete'] as bool? ?? false,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  lastLoginAt: json['lastLoginAt'] == null
      ? null
      : DateTime.parse(json['lastLoginAt'] as String),
  isActive: json['isActive'] as bool? ?? false,
  isDeleted: json['isDeleted'] as bool? ?? false,
);

Map<String, dynamic> _$$BuyerImplToJson(_$BuyerImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'profileImage': instance.profileImage,
      'addresses': instance.addresses,
      'savedProducts': instance.savedProducts,
      'orders': instance.orders,
      'isEmailVerified': instance.isEmailVerified,
      'isPhoneVerified': instance.isPhoneVerified,
      'isProfileComplete': instance.isProfileComplete,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'isActive': instance.isActive,
      'isDeleted': instance.isDeleted,
    };
