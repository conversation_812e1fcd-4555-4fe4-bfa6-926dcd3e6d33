[{"type": {"import": "package:shivish/apps/seller/domain/use_cases/get_seller_use_case.dart", "name": "GetSellerUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/seller/domain/use_cases/get_seller_use_case.dart", "name": "GetSellerUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/apps/seller/domain/repositories/seller_repository.dart", "name": "SellerRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_repository", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]