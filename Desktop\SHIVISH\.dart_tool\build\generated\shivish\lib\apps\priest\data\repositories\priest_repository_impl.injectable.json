[{"type": {"import": "package:shivish/apps/priest/domain/repositories/priest_repository.dart", "name": "PriestRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/priest/data/repositories/priest_repository_impl.dart", "name": "PriestRepositoryImpl", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_firestore", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:shivish/shared/services/auth/auth_service.dart", "name": "AuthService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_authService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]