// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_tracking_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

OrderTrackingModel _$OrderTrackingModelFromJson(Map<String, dynamic> json) {
  return _OrderTrackingModel.fromJson(json);
}

/// @nodoc
mixin _$OrderTrackingModel {
  String get id => throw _privateConstructorUsedError;
  String get orderId => throw _privateConstructorUsedError;
  OrderTrackingStatus get status => throw _privateConstructorUsedError;
  String get location => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  String? get updatedBy => throw _privateConstructorUsedError;
  double? get latitude => throw _privateConstructorUsedError;
  double? get longitude => throw _privateConstructorUsedError;
  String? get estimatedDeliveryTime => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this OrderTrackingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderTrackingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderTrackingModelCopyWith<OrderTrackingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderTrackingModelCopyWith<$Res> {
  factory $OrderTrackingModelCopyWith(
    OrderTrackingModel value,
    $Res Function(OrderTrackingModel) then,
  ) = _$OrderTrackingModelCopyWithImpl<$Res, OrderTrackingModel>;
  @useResult
  $Res call({
    String id,
    String orderId,
    OrderTrackingStatus status,
    String location,
    String? description,
    DateTime timestamp,
    String? updatedBy,
    double? latitude,
    double? longitude,
    String? estimatedDeliveryTime,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$OrderTrackingModelCopyWithImpl<$Res, $Val extends OrderTrackingModel>
    implements $OrderTrackingModelCopyWith<$Res> {
  _$OrderTrackingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderTrackingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? orderId = null,
    Object? status = null,
    Object? location = null,
    Object? description = freezed,
    Object? timestamp = null,
    Object? updatedBy = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? estimatedDeliveryTime = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            orderId: null == orderId
                ? _value.orderId
                : orderId // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as OrderTrackingStatus,
            location: null == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedBy: freezed == updatedBy
                ? _value.updatedBy
                : updatedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            latitude: freezed == latitude
                ? _value.latitude
                : latitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            longitude: freezed == longitude
                ? _value.longitude
                : longitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            estimatedDeliveryTime: freezed == estimatedDeliveryTime
                ? _value.estimatedDeliveryTime
                : estimatedDeliveryTime // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$OrderTrackingModelImplCopyWith<$Res>
    implements $OrderTrackingModelCopyWith<$Res> {
  factory _$$OrderTrackingModelImplCopyWith(
    _$OrderTrackingModelImpl value,
    $Res Function(_$OrderTrackingModelImpl) then,
  ) = __$$OrderTrackingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String orderId,
    OrderTrackingStatus status,
    String location,
    String? description,
    DateTime timestamp,
    String? updatedBy,
    double? latitude,
    double? longitude,
    String? estimatedDeliveryTime,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$OrderTrackingModelImplCopyWithImpl<$Res>
    extends _$OrderTrackingModelCopyWithImpl<$Res, _$OrderTrackingModelImpl>
    implements _$$OrderTrackingModelImplCopyWith<$Res> {
  __$$OrderTrackingModelImplCopyWithImpl(
    _$OrderTrackingModelImpl _value,
    $Res Function(_$OrderTrackingModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of OrderTrackingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? orderId = null,
    Object? status = null,
    Object? location = null,
    Object? description = freezed,
    Object? timestamp = null,
    Object? updatedBy = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? estimatedDeliveryTime = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$OrderTrackingModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        orderId: null == orderId
            ? _value.orderId
            : orderId // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as OrderTrackingStatus,
        location: null == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedBy: freezed == updatedBy
            ? _value.updatedBy
            : updatedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        latitude: freezed == latitude
            ? _value.latitude
            : latitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        longitude: freezed == longitude
            ? _value.longitude
            : longitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        estimatedDeliveryTime: freezed == estimatedDeliveryTime
            ? _value.estimatedDeliveryTime
            : estimatedDeliveryTime // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderTrackingModelImpl implements _OrderTrackingModel {
  const _$OrderTrackingModelImpl({
    required this.id,
    required this.orderId,
    required this.status,
    required this.location,
    this.description,
    required this.timestamp,
    this.updatedBy,
    this.latitude,
    this.longitude,
    this.estimatedDeliveryTime,
    required this.createdAt,
    required this.updatedAt,
  });

  factory _$OrderTrackingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderTrackingModelImplFromJson(json);

  @override
  final String id;
  @override
  final String orderId;
  @override
  final OrderTrackingStatus status;
  @override
  final String location;
  @override
  final String? description;
  @override
  final DateTime timestamp;
  @override
  final String? updatedBy;
  @override
  final double? latitude;
  @override
  final double? longitude;
  @override
  final String? estimatedDeliveryTime;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'OrderTrackingModel(id: $id, orderId: $orderId, status: $status, location: $location, description: $description, timestamp: $timestamp, updatedBy: $updatedBy, latitude: $latitude, longitude: $longitude, estimatedDeliveryTime: $estimatedDeliveryTime, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderTrackingModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.estimatedDeliveryTime, estimatedDeliveryTime) ||
                other.estimatedDeliveryTime == estimatedDeliveryTime) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    orderId,
    status,
    location,
    description,
    timestamp,
    updatedBy,
    latitude,
    longitude,
    estimatedDeliveryTime,
    createdAt,
    updatedAt,
  );

  /// Create a copy of OrderTrackingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderTrackingModelImplCopyWith<_$OrderTrackingModelImpl> get copyWith =>
      __$$OrderTrackingModelImplCopyWithImpl<_$OrderTrackingModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderTrackingModelImplToJson(this);
  }
}

abstract class _OrderTrackingModel implements OrderTrackingModel {
  const factory _OrderTrackingModel({
    required final String id,
    required final String orderId,
    required final OrderTrackingStatus status,
    required final String location,
    final String? description,
    required final DateTime timestamp,
    final String? updatedBy,
    final double? latitude,
    final double? longitude,
    final String? estimatedDeliveryTime,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$OrderTrackingModelImpl;

  factory _OrderTrackingModel.fromJson(Map<String, dynamic> json) =
      _$OrderTrackingModelImpl.fromJson;

  @override
  String get id;
  @override
  String get orderId;
  @override
  OrderTrackingStatus get status;
  @override
  String get location;
  @override
  String? get description;
  @override
  DateTime get timestamp;
  @override
  String? get updatedBy;
  @override
  double? get latitude;
  @override
  double? get longitude;
  @override
  String? get estimatedDeliveryTime;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of OrderTrackingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderTrackingModelImplCopyWith<_$OrderTrackingModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
