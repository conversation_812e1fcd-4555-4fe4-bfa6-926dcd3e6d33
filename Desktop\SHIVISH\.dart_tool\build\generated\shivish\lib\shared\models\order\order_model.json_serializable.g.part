// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderItemImpl _$$OrderItemImplFromJson(Map<String, dynamic> json) =>
    _$OrderItemImpl(
      id: json['id'] as String,
      productId: json['productId'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      imageUrl: json['imageUrl'] as String?,
      description: json['description'] as String?,
      productName: json['productName'] as String?,
      productImage: json['productImage'] as String?,
    );

Map<String, dynamic> _$$OrderItemImplToJson(_$OrderItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'name': instance.name,
      'price': instance.price,
      'quantity': instance.quantity,
      'imageUrl': instance.imageUrl,
      'description': instance.description,
      'productName': instance.productName,
      'productImage': instance.productImage,
    };

_$OrderAddressImpl _$$OrderAddressImplFromJson(Map<String, dynamic> json) =>
    _$OrderAddressImpl(
      street: json['street'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      country: json['country'] as String,
      postalCode: json['postalCode'] as String,
      landmark: json['landmark'] as String?,
      contactName: json['contactName'] as String,
      contactPhone: json['contactPhone'] as String,
      contactEmail: json['contactEmail'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$OrderAddressImplToJson(_$OrderAddressImpl instance) =>
    <String, dynamic>{
      'street': instance.street,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'postalCode': instance.postalCode,
      'landmark': instance.landmark,
      'contactName': instance.contactName,
      'contactPhone': instance.contactPhone,
      'contactEmail': instance.contactEmail,
      'isDefault': instance.isDefault,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

_$PaymentDetailsImpl _$$PaymentDetailsImplFromJson(Map<String, dynamic> json) =>
    _$PaymentDetailsImpl(
      method: json['method'] as String,
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      amount: (json['amount'] as num).toDouble(),
      transactionId: json['transactionId'] as String?,
      paidAt: json['paidAt'] == null
          ? null
          : DateTime.parse(json['paidAt'] as String),
      cardLast4: json['cardLast4'] as String?,
      cardBrand: json['cardBrand'] as String?,
      paymentId: json['paymentId'] as String?,
      paymentDate: json['paymentDate'] == null
          ? null
          : DateTime.parse(json['paymentDate'] as String),
    );

Map<String, dynamic> _$$PaymentDetailsImplToJson(
  _$PaymentDetailsImpl instance,
) => <String, dynamic>{
  'method': instance.method,
  'status': _$PaymentStatusEnumMap[instance.status]!,
  'amount': instance.amount,
  'transactionId': instance.transactionId,
  'paidAt': instance.paidAt?.toIso8601String(),
  'cardLast4': instance.cardLast4,
  'cardBrand': instance.cardBrand,
  'paymentId': instance.paymentId,
  'paymentDate': instance.paymentDate?.toIso8601String(),
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.completed: 'completed',
  PaymentStatus.failed: 'failed',
  PaymentStatus.refunded: 'refunded',
  PaymentStatus.partiallyRefunded: 'partiallyRefunded',
};

_$ShippingDetailsImpl _$$ShippingDetailsImplFromJson(
  Map<String, dynamic> json,
) => _$ShippingDetailsImpl(
  trackingNumber: json['trackingNumber'] as String,
  provider: json['provider'] as String,
  providerType: $enumDecode(
    _$DeliveryProviderTypeEnumMap,
    json['providerType'],
  ),
  estimatedDeliveryDate: json['estimatedDeliveryDate'] == null
      ? null
      : DateTime.parse(json['estimatedDeliveryDate'] as String),
  notes: json['notes'] as String?,
  deliveryPersonId: json['deliveryPersonId'] as String?,
  deliveryCharge: (json['deliveryCharge'] as num?)?.toDouble(),
  deliveryRequestId: json['deliveryRequestId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$ShippingDetailsImplToJson(
  _$ShippingDetailsImpl instance,
) => <String, dynamic>{
  'trackingNumber': instance.trackingNumber,
  'provider': instance.provider,
  'providerType': _$DeliveryProviderTypeEnumMap[instance.providerType]!,
  'estimatedDeliveryDate': instance.estimatedDeliveryDate?.toIso8601String(),
  'notes': instance.notes,
  'deliveryPersonId': instance.deliveryPersonId,
  'deliveryCharge': instance.deliveryCharge,
  'deliveryRequestId': instance.deliveryRequestId,
  'metadata': instance.metadata,
};

const _$DeliveryProviderTypeEnumMap = {
  DeliveryProviderType.ecomExpress: 'ecom_express',
  DeliveryProviderType.localDelivery: 'local_delivery',
};
