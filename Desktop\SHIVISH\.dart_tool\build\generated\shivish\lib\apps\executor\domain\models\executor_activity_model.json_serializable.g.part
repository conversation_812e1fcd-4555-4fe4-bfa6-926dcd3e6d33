// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExecutorActivityImpl _$$ExecutorActivityImplFromJson(
  Map<String, dynamic> json,
) => _$ExecutorActivityImpl(
  id: json['id'] as String,
  executorId: json['executorId'] as String,
  action: json['action'] as String,
  category: json['category'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  details: json['details'] as Map<String, dynamic>,
  targetId: json['targetId'] as String?,
  targetType: json['targetType'] as String?,
);

Map<String, dynamic> _$$ExecutorActivityImplToJson(
  _$ExecutorActivityImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'executorId': instance.executorId,
  'action': instance.action,
  'category': instance.category,
  'timestamp': instance.timestamp.toIso8601String(),
  'details': instance.details,
  'targetId': instance.targetId,
  'targetType': instance.targetType,
};

_$VerificationStatisticsImpl _$$VerificationStatisticsImplFromJson(
  Map<String, dynamic> json,
) => _$VerificationStatisticsImpl(
  totalVerifications: (json['totalVerifications'] as num).toInt(),
  pendingVerifications: (json['pendingVerifications'] as num).toInt(),
  approvedVerifications: (json['approvedVerifications'] as num).toInt(),
  rejectedVerifications: (json['rejectedVerifications'] as num).toInt(),
  verificationsByType: Map<String, int>.from(
    json['verificationsByType'] as Map,
  ),
  dailyVerifications: (json['dailyVerifications'] as List<dynamic>)
      .map((e) => VerificationPoint.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$$VerificationStatisticsImplToJson(
  _$VerificationStatisticsImpl instance,
) => <String, dynamic>{
  'totalVerifications': instance.totalVerifications,
  'pendingVerifications': instance.pendingVerifications,
  'approvedVerifications': instance.approvedVerifications,
  'rejectedVerifications': instance.rejectedVerifications,
  'verificationsByType': instance.verificationsByType,
  'dailyVerifications': instance.dailyVerifications
      .map((e) => e.toJson())
      .toList(),
};

_$VerificationPointImpl _$$VerificationPointImplFromJson(
  Map<String, dynamic> json,
) => _$VerificationPointImpl(
  date: DateTime.parse(json['date'] as String),
  count: (json['count'] as num).toInt(),
  approved: (json['approved'] as num).toInt(),
  rejected: (json['rejected'] as num).toInt(),
);

Map<String, dynamic> _$$VerificationPointImplToJson(
  _$VerificationPointImpl instance,
) => <String, dynamic>{
  'date': instance.date.toIso8601String(),
  'count': instance.count,
  'approved': instance.approved,
  'rejected': instance.rejected,
};

_$TimeTrackingImpl _$$TimeTrackingImplFromJson(Map<String, dynamic> json) =>
    _$TimeTrackingImpl(
      id: json['id'] as String,
      executorId: json['executorId'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      taskType: json['taskType'] as String,
      taskId: json['taskId'] as String,
      status: json['status'] as String,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$TimeTrackingImplToJson(_$TimeTrackingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'executorId': instance.executorId,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'taskType': instance.taskType,
      'taskId': instance.taskId,
      'status': instance.status,
      'notes': instance.notes,
    };
