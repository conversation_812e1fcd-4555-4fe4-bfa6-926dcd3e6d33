// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reminder_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ReminderSettingsModel _$ReminderSettingsModelFromJson(
  Map<String, dynamic> json,
) {
  return _ReminderSettingsModel.fromJson(json);
}

/// @nodoc
mixin _$ReminderSettingsModel {
  bool get enableReminders => throw _privateConstructorUsedError;
  bool get enableEmailReminders => throw _privateConstructorUsedError;
  bool get enablePushReminders => throw _privateConstructorUsedError;
  bool get enableSMSReminders => throw _privateConstructorUsedError;
  int get defaultReminderTime => throw _privateConstructorUsedError;
  bool get enableCustomReminders => throw _privateConstructorUsedError;
  List<CustomReminderModel> get customReminders =>
      throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ReminderSettingsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReminderSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReminderSettingsModelCopyWith<ReminderSettingsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReminderSettingsModelCopyWith<$Res> {
  factory $ReminderSettingsModelCopyWith(
    ReminderSettingsModel value,
    $Res Function(ReminderSettingsModel) then,
  ) = _$ReminderSettingsModelCopyWithImpl<$Res, ReminderSettingsModel>;
  @useResult
  $Res call({
    bool enableReminders,
    bool enableEmailReminders,
    bool enablePushReminders,
    bool enableSMSReminders,
    int defaultReminderTime,
    bool enableCustomReminders,
    List<CustomReminderModel> customReminders,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$ReminderSettingsModelCopyWithImpl<
  $Res,
  $Val extends ReminderSettingsModel
>
    implements $ReminderSettingsModelCopyWith<$Res> {
  _$ReminderSettingsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReminderSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enableReminders = null,
    Object? enableEmailReminders = null,
    Object? enablePushReminders = null,
    Object? enableSMSReminders = null,
    Object? defaultReminderTime = null,
    Object? enableCustomReminders = null,
    Object? customReminders = null,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            enableReminders: null == enableReminders
                ? _value.enableReminders
                : enableReminders // ignore: cast_nullable_to_non_nullable
                      as bool,
            enableEmailReminders: null == enableEmailReminders
                ? _value.enableEmailReminders
                : enableEmailReminders // ignore: cast_nullable_to_non_nullable
                      as bool,
            enablePushReminders: null == enablePushReminders
                ? _value.enablePushReminders
                : enablePushReminders // ignore: cast_nullable_to_non_nullable
                      as bool,
            enableSMSReminders: null == enableSMSReminders
                ? _value.enableSMSReminders
                : enableSMSReminders // ignore: cast_nullable_to_non_nullable
                      as bool,
            defaultReminderTime: null == defaultReminderTime
                ? _value.defaultReminderTime
                : defaultReminderTime // ignore: cast_nullable_to_non_nullable
                      as int,
            enableCustomReminders: null == enableCustomReminders
                ? _value.enableCustomReminders
                : enableCustomReminders // ignore: cast_nullable_to_non_nullable
                      as bool,
            customReminders: null == customReminders
                ? _value.customReminders
                : customReminders // ignore: cast_nullable_to_non_nullable
                      as List<CustomReminderModel>,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ReminderSettingsModelImplCopyWith<$Res>
    implements $ReminderSettingsModelCopyWith<$Res> {
  factory _$$ReminderSettingsModelImplCopyWith(
    _$ReminderSettingsModelImpl value,
    $Res Function(_$ReminderSettingsModelImpl) then,
  ) = __$$ReminderSettingsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool enableReminders,
    bool enableEmailReminders,
    bool enablePushReminders,
    bool enableSMSReminders,
    int defaultReminderTime,
    bool enableCustomReminders,
    List<CustomReminderModel> customReminders,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$ReminderSettingsModelImplCopyWithImpl<$Res>
    extends
        _$ReminderSettingsModelCopyWithImpl<$Res, _$ReminderSettingsModelImpl>
    implements _$$ReminderSettingsModelImplCopyWith<$Res> {
  __$$ReminderSettingsModelImplCopyWithImpl(
    _$ReminderSettingsModelImpl _value,
    $Res Function(_$ReminderSettingsModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ReminderSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enableReminders = null,
    Object? enableEmailReminders = null,
    Object? enablePushReminders = null,
    Object? enableSMSReminders = null,
    Object? defaultReminderTime = null,
    Object? enableCustomReminders = null,
    Object? customReminders = null,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$ReminderSettingsModelImpl(
        enableReminders: null == enableReminders
            ? _value.enableReminders
            : enableReminders // ignore: cast_nullable_to_non_nullable
                  as bool,
        enableEmailReminders: null == enableEmailReminders
            ? _value.enableEmailReminders
            : enableEmailReminders // ignore: cast_nullable_to_non_nullable
                  as bool,
        enablePushReminders: null == enablePushReminders
            ? _value.enablePushReminders
            : enablePushReminders // ignore: cast_nullable_to_non_nullable
                  as bool,
        enableSMSReminders: null == enableSMSReminders
            ? _value.enableSMSReminders
            : enableSMSReminders // ignore: cast_nullable_to_non_nullable
                  as bool,
        defaultReminderTime: null == defaultReminderTime
            ? _value.defaultReminderTime
            : defaultReminderTime // ignore: cast_nullable_to_non_nullable
                  as int,
        enableCustomReminders: null == enableCustomReminders
            ? _value.enableCustomReminders
            : enableCustomReminders // ignore: cast_nullable_to_non_nullable
                  as bool,
        customReminders: null == customReminders
            ? _value._customReminders
            : customReminders // ignore: cast_nullable_to_non_nullable
                  as List<CustomReminderModel>,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ReminderSettingsModelImpl implements _ReminderSettingsModel {
  const _$ReminderSettingsModelImpl({
    required this.enableReminders,
    required this.enableEmailReminders,
    required this.enablePushReminders,
    required this.enableSMSReminders,
    required this.defaultReminderTime,
    required this.enableCustomReminders,
    required final List<CustomReminderModel> customReminders,
    this.updatedAt,
  }) : _customReminders = customReminders;

  factory _$ReminderSettingsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReminderSettingsModelImplFromJson(json);

  @override
  final bool enableReminders;
  @override
  final bool enableEmailReminders;
  @override
  final bool enablePushReminders;
  @override
  final bool enableSMSReminders;
  @override
  final int defaultReminderTime;
  @override
  final bool enableCustomReminders;
  final List<CustomReminderModel> _customReminders;
  @override
  List<CustomReminderModel> get customReminders {
    if (_customReminders is EqualUnmodifiableListView) return _customReminders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_customReminders);
  }

  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ReminderSettingsModel(enableReminders: $enableReminders, enableEmailReminders: $enableEmailReminders, enablePushReminders: $enablePushReminders, enableSMSReminders: $enableSMSReminders, defaultReminderTime: $defaultReminderTime, enableCustomReminders: $enableCustomReminders, customReminders: $customReminders, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReminderSettingsModelImpl &&
            (identical(other.enableReminders, enableReminders) ||
                other.enableReminders == enableReminders) &&
            (identical(other.enableEmailReminders, enableEmailReminders) ||
                other.enableEmailReminders == enableEmailReminders) &&
            (identical(other.enablePushReminders, enablePushReminders) ||
                other.enablePushReminders == enablePushReminders) &&
            (identical(other.enableSMSReminders, enableSMSReminders) ||
                other.enableSMSReminders == enableSMSReminders) &&
            (identical(other.defaultReminderTime, defaultReminderTime) ||
                other.defaultReminderTime == defaultReminderTime) &&
            (identical(other.enableCustomReminders, enableCustomReminders) ||
                other.enableCustomReminders == enableCustomReminders) &&
            const DeepCollectionEquality().equals(
              other._customReminders,
              _customReminders,
            ) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    enableReminders,
    enableEmailReminders,
    enablePushReminders,
    enableSMSReminders,
    defaultReminderTime,
    enableCustomReminders,
    const DeepCollectionEquality().hash(_customReminders),
    updatedAt,
  );

  /// Create a copy of ReminderSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReminderSettingsModelImplCopyWith<_$ReminderSettingsModelImpl>
  get copyWith =>
      __$$ReminderSettingsModelImplCopyWithImpl<_$ReminderSettingsModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ReminderSettingsModelImplToJson(this);
  }
}

abstract class _ReminderSettingsModel implements ReminderSettingsModel {
  const factory _ReminderSettingsModel({
    required final bool enableReminders,
    required final bool enableEmailReminders,
    required final bool enablePushReminders,
    required final bool enableSMSReminders,
    required final int defaultReminderTime,
    required final bool enableCustomReminders,
    required final List<CustomReminderModel> customReminders,
    final DateTime? updatedAt,
  }) = _$ReminderSettingsModelImpl;

  factory _ReminderSettingsModel.fromJson(Map<String, dynamic> json) =
      _$ReminderSettingsModelImpl.fromJson;

  @override
  bool get enableReminders;
  @override
  bool get enableEmailReminders;
  @override
  bool get enablePushReminders;
  @override
  bool get enableSMSReminders;
  @override
  int get defaultReminderTime;
  @override
  bool get enableCustomReminders;
  @override
  List<CustomReminderModel> get customReminders;
  @override
  DateTime? get updatedAt;

  /// Create a copy of ReminderSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReminderSettingsModelImplCopyWith<_$ReminderSettingsModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

CustomReminderModel _$CustomReminderModelFromJson(Map<String, dynamic> json) {
  return _CustomReminderModel.fromJson(json);
}

/// @nodoc
mixin _$CustomReminderModel {
  int get minutesBefore => throw _privateConstructorUsedError;
  bool get isEnabled => throw _privateConstructorUsedError;

  /// Serializes this CustomReminderModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomReminderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomReminderModelCopyWith<CustomReminderModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomReminderModelCopyWith<$Res> {
  factory $CustomReminderModelCopyWith(
    CustomReminderModel value,
    $Res Function(CustomReminderModel) then,
  ) = _$CustomReminderModelCopyWithImpl<$Res, CustomReminderModel>;
  @useResult
  $Res call({int minutesBefore, bool isEnabled});
}

/// @nodoc
class _$CustomReminderModelCopyWithImpl<$Res, $Val extends CustomReminderModel>
    implements $CustomReminderModelCopyWith<$Res> {
  _$CustomReminderModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomReminderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? minutesBefore = null, Object? isEnabled = null}) {
    return _then(
      _value.copyWith(
            minutesBefore: null == minutesBefore
                ? _value.minutesBefore
                : minutesBefore // ignore: cast_nullable_to_non_nullable
                      as int,
            isEnabled: null == isEnabled
                ? _value.isEnabled
                : isEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CustomReminderModelImplCopyWith<$Res>
    implements $CustomReminderModelCopyWith<$Res> {
  factory _$$CustomReminderModelImplCopyWith(
    _$CustomReminderModelImpl value,
    $Res Function(_$CustomReminderModelImpl) then,
  ) = __$$CustomReminderModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int minutesBefore, bool isEnabled});
}

/// @nodoc
class __$$CustomReminderModelImplCopyWithImpl<$Res>
    extends _$CustomReminderModelCopyWithImpl<$Res, _$CustomReminderModelImpl>
    implements _$$CustomReminderModelImplCopyWith<$Res> {
  __$$CustomReminderModelImplCopyWithImpl(
    _$CustomReminderModelImpl _value,
    $Res Function(_$CustomReminderModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CustomReminderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? minutesBefore = null, Object? isEnabled = null}) {
    return _then(
      _$CustomReminderModelImpl(
        minutesBefore: null == minutesBefore
            ? _value.minutesBefore
            : minutesBefore // ignore: cast_nullable_to_non_nullable
                  as int,
        isEnabled: null == isEnabled
            ? _value.isEnabled
            : isEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomReminderModelImpl implements _CustomReminderModel {
  const _$CustomReminderModelImpl({
    required this.minutesBefore,
    required this.isEnabled,
  });

  factory _$CustomReminderModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomReminderModelImplFromJson(json);

  @override
  final int minutesBefore;
  @override
  final bool isEnabled;

  @override
  String toString() {
    return 'CustomReminderModel(minutesBefore: $minutesBefore, isEnabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomReminderModelImpl &&
            (identical(other.minutesBefore, minutesBefore) ||
                other.minutesBefore == minutesBefore) &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, minutesBefore, isEnabled);

  /// Create a copy of CustomReminderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomReminderModelImplCopyWith<_$CustomReminderModelImpl> get copyWith =>
      __$$CustomReminderModelImplCopyWithImpl<_$CustomReminderModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomReminderModelImplToJson(this);
  }
}

abstract class _CustomReminderModel implements CustomReminderModel {
  const factory _CustomReminderModel({
    required final int minutesBefore,
    required final bool isEnabled,
  }) = _$CustomReminderModelImpl;

  factory _CustomReminderModel.fromJson(Map<String, dynamic> json) =
      _$CustomReminderModelImpl.fromJson;

  @override
  int get minutesBefore;
  @override
  bool get isEnabled;

  /// Create a copy of CustomReminderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomReminderModelImplCopyWith<_$CustomReminderModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
