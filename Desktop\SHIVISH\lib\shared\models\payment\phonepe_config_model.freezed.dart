// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'phonepe_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PhonePeConfigModel _$PhonePeConfigModelFromJson(Map<String, dynamic> json) {
  return _PhonePeConfigModel.fromJson(json);
}

/// @nodoc
mixin _$PhonePeConfigModel {
  /// Whether PhonePe is enabled
  bool get isEnabled => throw _privateConstructorUsedError;

  /// Merchant ID for PhonePe
  String get merchantId => throw _privateConstructorUsedError;

  /// API Key for PhonePe
  String get apiKey => throw _privateConstructorUsedError;

  /// Salt Key for PhonePe (used for checksum calculation)
  String get saltKey => throw _privateConstructorUsedError;

  /// API Endpoint for PhonePe
  String get apiEndpoint => throw _privateConstructorUsedError;

  /// Whether to use production environment
  bool get isProduction => throw _privateConstructorUsedError;

  /// Callback URL for PhonePe
  String get callbackUrl => throw _privateConstructorUsedError;

  /// Redirect URL for PhonePe
  String get redirectUrl => throw _privateConstructorUsedError;

  /// Created timestamp
  DateTime? get createdAt => throw _privateConstructorUsedError;

  /// Updated timestamp
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this PhonePeConfigModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PhonePeConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhonePeConfigModelCopyWith<PhonePeConfigModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhonePeConfigModelCopyWith<$Res> {
  factory $PhonePeConfigModelCopyWith(
    PhonePeConfigModel value,
    $Res Function(PhonePeConfigModel) then,
  ) = _$PhonePeConfigModelCopyWithImpl<$Res, PhonePeConfigModel>;
  @useResult
  $Res call({
    bool isEnabled,
    String merchantId,
    String apiKey,
    String saltKey,
    String apiEndpoint,
    bool isProduction,
    String callbackUrl,
    String redirectUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$PhonePeConfigModelCopyWithImpl<$Res, $Val extends PhonePeConfigModel>
    implements $PhonePeConfigModelCopyWith<$Res> {
  _$PhonePeConfigModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhonePeConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEnabled = null,
    Object? merchantId = null,
    Object? apiKey = null,
    Object? saltKey = null,
    Object? apiEndpoint = null,
    Object? isProduction = null,
    Object? callbackUrl = null,
    Object? redirectUrl = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            isEnabled: null == isEnabled
                ? _value.isEnabled
                : isEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            merchantId: null == merchantId
                ? _value.merchantId
                : merchantId // ignore: cast_nullable_to_non_nullable
                      as String,
            apiKey: null == apiKey
                ? _value.apiKey
                : apiKey // ignore: cast_nullable_to_non_nullable
                      as String,
            saltKey: null == saltKey
                ? _value.saltKey
                : saltKey // ignore: cast_nullable_to_non_nullable
                      as String,
            apiEndpoint: null == apiEndpoint
                ? _value.apiEndpoint
                : apiEndpoint // ignore: cast_nullable_to_non_nullable
                      as String,
            isProduction: null == isProduction
                ? _value.isProduction
                : isProduction // ignore: cast_nullable_to_non_nullable
                      as bool,
            callbackUrl: null == callbackUrl
                ? _value.callbackUrl
                : callbackUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            redirectUrl: null == redirectUrl
                ? _value.redirectUrl
                : redirectUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PhonePeConfigModelImplCopyWith<$Res>
    implements $PhonePeConfigModelCopyWith<$Res> {
  factory _$$PhonePeConfigModelImplCopyWith(
    _$PhonePeConfigModelImpl value,
    $Res Function(_$PhonePeConfigModelImpl) then,
  ) = __$$PhonePeConfigModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool isEnabled,
    String merchantId,
    String apiKey,
    String saltKey,
    String apiEndpoint,
    bool isProduction,
    String callbackUrl,
    String redirectUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$PhonePeConfigModelImplCopyWithImpl<$Res>
    extends _$PhonePeConfigModelCopyWithImpl<$Res, _$PhonePeConfigModelImpl>
    implements _$$PhonePeConfigModelImplCopyWith<$Res> {
  __$$PhonePeConfigModelImplCopyWithImpl(
    _$PhonePeConfigModelImpl _value,
    $Res Function(_$PhonePeConfigModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PhonePeConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEnabled = null,
    Object? merchantId = null,
    Object? apiKey = null,
    Object? saltKey = null,
    Object? apiEndpoint = null,
    Object? isProduction = null,
    Object? callbackUrl = null,
    Object? redirectUrl = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$PhonePeConfigModelImpl(
        isEnabled: null == isEnabled
            ? _value.isEnabled
            : isEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        merchantId: null == merchantId
            ? _value.merchantId
            : merchantId // ignore: cast_nullable_to_non_nullable
                  as String,
        apiKey: null == apiKey
            ? _value.apiKey
            : apiKey // ignore: cast_nullable_to_non_nullable
                  as String,
        saltKey: null == saltKey
            ? _value.saltKey
            : saltKey // ignore: cast_nullable_to_non_nullable
                  as String,
        apiEndpoint: null == apiEndpoint
            ? _value.apiEndpoint
            : apiEndpoint // ignore: cast_nullable_to_non_nullable
                  as String,
        isProduction: null == isProduction
            ? _value.isProduction
            : isProduction // ignore: cast_nullable_to_non_nullable
                  as bool,
        callbackUrl: null == callbackUrl
            ? _value.callbackUrl
            : callbackUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        redirectUrl: null == redirectUrl
            ? _value.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PhonePeConfigModelImpl implements _PhonePeConfigModel {
  const _$PhonePeConfigModelImpl({
    this.isEnabled = false,
    this.merchantId = '',
    this.apiKey = '',
    this.saltKey = '',
    this.apiEndpoint = 'https://api-preprod.phonepe.com/apis/pg-sandbox',
    this.isProduction = false,
    this.callbackUrl = '',
    this.redirectUrl = '',
    this.createdAt,
    this.updatedAt,
  });

  factory _$PhonePeConfigModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PhonePeConfigModelImplFromJson(json);

  /// Whether PhonePe is enabled
  @override
  @JsonKey()
  final bool isEnabled;

  /// Merchant ID for PhonePe
  @override
  @JsonKey()
  final String merchantId;

  /// API Key for PhonePe
  @override
  @JsonKey()
  final String apiKey;

  /// Salt Key for PhonePe (used for checksum calculation)
  @override
  @JsonKey()
  final String saltKey;

  /// API Endpoint for PhonePe
  @override
  @JsonKey()
  final String apiEndpoint;

  /// Whether to use production environment
  @override
  @JsonKey()
  final bool isProduction;

  /// Callback URL for PhonePe
  @override
  @JsonKey()
  final String callbackUrl;

  /// Redirect URL for PhonePe
  @override
  @JsonKey()
  final String redirectUrl;

  /// Created timestamp
  @override
  final DateTime? createdAt;

  /// Updated timestamp
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'PhonePeConfigModel(isEnabled: $isEnabled, merchantId: $merchantId, apiKey: $apiKey, saltKey: $saltKey, apiEndpoint: $apiEndpoint, isProduction: $isProduction, callbackUrl: $callbackUrl, redirectUrl: $redirectUrl, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhonePeConfigModelImpl &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled) &&
            (identical(other.merchantId, merchantId) ||
                other.merchantId == merchantId) &&
            (identical(other.apiKey, apiKey) || other.apiKey == apiKey) &&
            (identical(other.saltKey, saltKey) || other.saltKey == saltKey) &&
            (identical(other.apiEndpoint, apiEndpoint) ||
                other.apiEndpoint == apiEndpoint) &&
            (identical(other.isProduction, isProduction) ||
                other.isProduction == isProduction) &&
            (identical(other.callbackUrl, callbackUrl) ||
                other.callbackUrl == callbackUrl) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    isEnabled,
    merchantId,
    apiKey,
    saltKey,
    apiEndpoint,
    isProduction,
    callbackUrl,
    redirectUrl,
    createdAt,
    updatedAt,
  );

  /// Create a copy of PhonePeConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhonePeConfigModelImplCopyWith<_$PhonePeConfigModelImpl> get copyWith =>
      __$$PhonePeConfigModelImplCopyWithImpl<_$PhonePeConfigModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PhonePeConfigModelImplToJson(this);
  }
}

abstract class _PhonePeConfigModel implements PhonePeConfigModel {
  const factory _PhonePeConfigModel({
    final bool isEnabled,
    final String merchantId,
    final String apiKey,
    final String saltKey,
    final String apiEndpoint,
    final bool isProduction,
    final String callbackUrl,
    final String redirectUrl,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$PhonePeConfigModelImpl;

  factory _PhonePeConfigModel.fromJson(Map<String, dynamic> json) =
      _$PhonePeConfigModelImpl.fromJson;

  /// Whether PhonePe is enabled
  @override
  bool get isEnabled;

  /// Merchant ID for PhonePe
  @override
  String get merchantId;

  /// API Key for PhonePe
  @override
  String get apiKey;

  /// Salt Key for PhonePe (used for checksum calculation)
  @override
  String get saltKey;

  /// API Endpoint for PhonePe
  @override
  String get apiEndpoint;

  /// Whether to use production environment
  @override
  bool get isProduction;

  /// Callback URL for PhonePe
  @override
  String get callbackUrl;

  /// Redirect URL for PhonePe
  @override
  String get redirectUrl;

  /// Created timestamp
  @override
  DateTime? get createdAt;

  /// Updated timestamp
  @override
  DateTime? get updatedAt;

  /// Create a copy of PhonePeConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhonePeConfigModelImplCopyWith<_$PhonePeConfigModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
