// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AlarmModelImpl _$$AlarmModelImplFromJson(Map<String, dynamic> json) =>
    _$AlarmModelImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$AlarmTypeEnumMap, json['type']),
      time: DateTime.parse(json['time'] as String),
      isEnabled: json['isEnabled'] as bool,
      toneId: json['toneId'] as String,
      volume: (json['volume'] as num).toDouble(),
      vibrate: json['vibrate'] as bool,
      repeat: $enumDecode(_$AlarmRepeatEnumMap, json['repeat']),
      repeatDays: (json['repeatDays'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      snoozeDuration: (json['snoozeDuration'] as num).toInt(),
      maxSnoozeCount: (json['maxSnoozeCount'] as num).toInt(),
      label: json['label'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      currentSnoozeCount: (json['currentSnoozeCount'] as num?)?.toInt() ?? 0,
      isSnoozed: json['isSnoozed'] as bool? ?? false,
      snoozeTime: json['snoozeTime'] == null
          ? null
          : DateTime.parse(json['snoozeTime'] as String),
      isLearning: json['isLearning'] as bool? ?? false,
      usagePatterns: json['usagePatterns'] as Map<String, dynamic>? ?? const {},
      smartWakeUpOffset: (json['smartWakeUpOffset'] as num?)?.toInt() ?? 0,
      isCustom: json['isCustom'] as bool? ?? false,
      customSettings:
          json['customSettings'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$AlarmModelImplToJson(_$AlarmModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$AlarmTypeEnumMap[instance.type]!,
      'time': instance.time.toIso8601String(),
      'isEnabled': instance.isEnabled,
      'toneId': instance.toneId,
      'volume': instance.volume,
      'vibrate': instance.vibrate,
      'repeat': _$AlarmRepeatEnumMap[instance.repeat]!,
      'repeatDays': instance.repeatDays,
      'snoozeDuration': instance.snoozeDuration,
      'maxSnoozeCount': instance.maxSnoozeCount,
      'label': instance.label,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'currentSnoozeCount': instance.currentSnoozeCount,
      'isSnoozed': instance.isSnoozed,
      'snoozeTime': instance.snoozeTime?.toIso8601String(),
      'isLearning': instance.isLearning,
      'usagePatterns': instance.usagePatterns,
      'smartWakeUpOffset': instance.smartWakeUpOffset,
      'isCustom': instance.isCustom,
      'customSettings': instance.customSettings,
    };

const _$AlarmTypeEnumMap = {
  AlarmType.defaultAlarm: 0,
  AlarmType.aiAlarm: 1,
  AlarmType.customAlarm: 2,
};

const _$AlarmRepeatEnumMap = {
  AlarmRepeat.once: 0,
  AlarmRepeat.daily: 1,
  AlarmRepeat.weekly: 2,
  AlarmRepeat.monthly: 3,
  AlarmRepeat.weekdays: 4,
  AlarmRepeat.weekends: 5,
};
