// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PriceHistoryEntryImpl _$$PriceHistoryEntryImplFromJson(
  Map<String, dynamic> json,
) => _$PriceHistoryEntryImpl(
  productId: json['productId'] as String,
  productName: json['productName'] as String,
  oldPrice: (json['oldPrice'] as num).toDouble(),
  newPrice: (json['newPrice'] as num).toDouble(),
  reason: json['reason'] as String,
  updatedBy: json['updatedBy'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
);

Map<String, dynamic> _$$PriceHistoryEntryImplToJson(
  _$PriceHistoryEntryImpl instance,
) => <String, dynamic>{
  'productId': instance.productId,
  'productName': instance.productName,
  'oldPrice': instance.oldPrice,
  'newPrice': instance.newPrice,
  'reason': instance.reason,
  'updatedBy': instance.updatedBy,
  'timestamp': instance.timestamp.toIso8601String(),
};
