// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BreakTimeSettingsModelImpl _$$BreakTimeSettingsModelImplFromJson(
  Map<String, dynamic> json,
) => _$BreakTimeSettingsModelImpl(
  lunchStartTime: json['lunchStartTime'] as String,
  lunchEndTime: json['lunchEndTime'] as String,
  teaStartTime: json['teaStartTime'] as String,
  teaEndTime: json['teaEndTime'] as String,
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$BreakTimeSettingsModelImplToJson(
  _$BreakTimeSettingsModelImpl instance,
) => <String, dynamic>{
  'lunchStartTime': instance.lunchStartTime,
  'lunchEndTime': instance.lunchEndTime,
  'teaStartTime': instance.teaStartTime,
  'teaEndTime': instance.teaEndTime,
  'updatedAt': instance.updatedAt?.toIso8601String(),
};
