import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/user/user_model.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class AuthUser with _$AuthUser {
  const factory AuthUser({
    required String id,
    required String name,
    required String email,
    required String role,
    String? profileImage,
    @Default(false) bool isActive,
    @Default(false) bool isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _User;

  factory AuthUser.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  factory AuthUser.fromUserModel(UserModel model) => AuthUser(
        id: model.id,
        name: _getUserDisplayName(model),
        email: model.email,
        role: model.role.name,
        profileImage: null,
        isActive: model.status == UserStatus.active,
        isVerified: model.verificationStatus == VerificationStatus.verified,
        createdAt: model.createdAt,
        updatedAt: model.updatedAt,
      );

  static String _getUserDisplayName(UserModel model) {
    final displayName = model.displayName;
    if (displayName.isEmpty) {
      return model.email;
    }
    return displayName;
  }
}

extension UserX on AuthUser {
  UserModel toUserModel() {
    return UserModel(
      id: id,
      email: email,
      displayName: name,
      firstName: name.split(' ').first,
      lastName: name.split(' ').length > 1 ? name.split(' ').last : '',
      role: UserRole.values.firstWhere(
        (role) => role.name.toLowerCase() == this.role.toLowerCase(),
        orElse: () => UserRole.buyer,
      ),
      status: this.isActive ? UserStatus.active : UserStatus.inactive,
      verificationStatus: this.isVerified
          ? VerificationStatus.verified
          : VerificationStatus.unverified,
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}

extension AdminUserX on UserModel {
  String get fullName => '$firstName $lastName'.trim();

  bool get isActive => status == UserStatus.active;

  bool get isVerified => verificationStatus == VerificationStatus.verified;

  bool get isPending => verificationStatus == VerificationStatus.pending;

  bool get isRejected => verificationStatus == VerificationStatus.rejected;

  bool get isInactive => status == UserStatus.inactive;

  bool get isBlocked => status == UserStatus.blocked;

  bool get isDeleted => status == UserStatus.deleted;

  String get roleName => role.name;

  String get statusName => status.name;

  String get verificationStatusName => verificationStatus.name;

  String? get rejectionReasonText => rejectionReason ?? suspensionReason;

  String? get verifiedByText => approvedBy ?? rejectedBy ?? suspendedBy;

  DateTime? get verifiedAtText => approvedAt ?? rejectedAt ?? suspendedAt;
}
