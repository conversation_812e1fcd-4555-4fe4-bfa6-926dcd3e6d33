// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'doctor_schedule_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

DoctorScheduleModel _$DoctorScheduleModelFromJson(Map<String, dynamic> json) {
  return _DoctorScheduleModel.fromJson(json);
}

/// @nodoc
mixin _$DoctorScheduleModel {
  String get id => throw _privateConstructorUsedError;
  String get doctorId => throw _privateConstructorUsedError;
  Map<String, DaySchedule> get weeklySchedule =>
      throw _privateConstructorUsedError;
  List<DateTime> get holidays => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this DoctorScheduleModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DoctorScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DoctorScheduleModelCopyWith<DoctorScheduleModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DoctorScheduleModelCopyWith<$Res> {
  factory $DoctorScheduleModelCopyWith(
    DoctorScheduleModel value,
    $Res Function(DoctorScheduleModel) then,
  ) = _$DoctorScheduleModelCopyWithImpl<$Res, DoctorScheduleModel>;
  @useResult
  $Res call({
    String id,
    String doctorId,
    Map<String, DaySchedule> weeklySchedule,
    List<DateTime> holidays,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$DoctorScheduleModelCopyWithImpl<$Res, $Val extends DoctorScheduleModel>
    implements $DoctorScheduleModelCopyWith<$Res> {
  _$DoctorScheduleModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DoctorScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? doctorId = null,
    Object? weeklySchedule = null,
    Object? holidays = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            doctorId: null == doctorId
                ? _value.doctorId
                : doctorId // ignore: cast_nullable_to_non_nullable
                      as String,
            weeklySchedule: null == weeklySchedule
                ? _value.weeklySchedule
                : weeklySchedule // ignore: cast_nullable_to_non_nullable
                      as Map<String, DaySchedule>,
            holidays: null == holidays
                ? _value.holidays
                : holidays // ignore: cast_nullable_to_non_nullable
                      as List<DateTime>,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DoctorScheduleModelImplCopyWith<$Res>
    implements $DoctorScheduleModelCopyWith<$Res> {
  factory _$$DoctorScheduleModelImplCopyWith(
    _$DoctorScheduleModelImpl value,
    $Res Function(_$DoctorScheduleModelImpl) then,
  ) = __$$DoctorScheduleModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String doctorId,
    Map<String, DaySchedule> weeklySchedule,
    List<DateTime> holidays,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$DoctorScheduleModelImplCopyWithImpl<$Res>
    extends _$DoctorScheduleModelCopyWithImpl<$Res, _$DoctorScheduleModelImpl>
    implements _$$DoctorScheduleModelImplCopyWith<$Res> {
  __$$DoctorScheduleModelImplCopyWithImpl(
    _$DoctorScheduleModelImpl _value,
    $Res Function(_$DoctorScheduleModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DoctorScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? doctorId = null,
    Object? weeklySchedule = null,
    Object? holidays = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$DoctorScheduleModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        doctorId: null == doctorId
            ? _value.doctorId
            : doctorId // ignore: cast_nullable_to_non_nullable
                  as String,
        weeklySchedule: null == weeklySchedule
            ? _value._weeklySchedule
            : weeklySchedule // ignore: cast_nullable_to_non_nullable
                  as Map<String, DaySchedule>,
        holidays: null == holidays
            ? _value._holidays
            : holidays // ignore: cast_nullable_to_non_nullable
                  as List<DateTime>,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DoctorScheduleModelImpl implements _DoctorScheduleModel {
  const _$DoctorScheduleModelImpl({
    required this.id,
    required this.doctorId,
    required final Map<String, DaySchedule> weeklySchedule,
    required final List<DateTime> holidays,
    required this.createdAt,
    required this.updatedAt,
  }) : _weeklySchedule = weeklySchedule,
       _holidays = holidays;

  factory _$DoctorScheduleModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DoctorScheduleModelImplFromJson(json);

  @override
  final String id;
  @override
  final String doctorId;
  final Map<String, DaySchedule> _weeklySchedule;
  @override
  Map<String, DaySchedule> get weeklySchedule {
    if (_weeklySchedule is EqualUnmodifiableMapView) return _weeklySchedule;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_weeklySchedule);
  }

  final List<DateTime> _holidays;
  @override
  List<DateTime> get holidays {
    if (_holidays is EqualUnmodifiableListView) return _holidays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_holidays);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'DoctorScheduleModel(id: $id, doctorId: $doctorId, weeklySchedule: $weeklySchedule, holidays: $holidays, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DoctorScheduleModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.doctorId, doctorId) ||
                other.doctorId == doctorId) &&
            const DeepCollectionEquality().equals(
              other._weeklySchedule,
              _weeklySchedule,
            ) &&
            const DeepCollectionEquality().equals(other._holidays, _holidays) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    doctorId,
    const DeepCollectionEquality().hash(_weeklySchedule),
    const DeepCollectionEquality().hash(_holidays),
    createdAt,
    updatedAt,
  );

  /// Create a copy of DoctorScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DoctorScheduleModelImplCopyWith<_$DoctorScheduleModelImpl> get copyWith =>
      __$$DoctorScheduleModelImplCopyWithImpl<_$DoctorScheduleModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$DoctorScheduleModelImplToJson(this);
  }
}

abstract class _DoctorScheduleModel implements DoctorScheduleModel {
  const factory _DoctorScheduleModel({
    required final String id,
    required final String doctorId,
    required final Map<String, DaySchedule> weeklySchedule,
    required final List<DateTime> holidays,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$DoctorScheduleModelImpl;

  factory _DoctorScheduleModel.fromJson(Map<String, dynamic> json) =
      _$DoctorScheduleModelImpl.fromJson;

  @override
  String get id;
  @override
  String get doctorId;
  @override
  Map<String, DaySchedule> get weeklySchedule;
  @override
  List<DateTime> get holidays;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of DoctorScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DoctorScheduleModelImplCopyWith<_$DoctorScheduleModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DaySchedule _$DayScheduleFromJson(Map<String, dynamic> json) {
  return _DaySchedule.fromJson(json);
}

/// @nodoc
mixin _$DaySchedule {
  bool get isAvailable => throw _privateConstructorUsedError;
  List<TimeSlot> get slots => throw _privateConstructorUsedError;

  /// Serializes this DaySchedule to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DaySchedule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DayScheduleCopyWith<DaySchedule> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DayScheduleCopyWith<$Res> {
  factory $DayScheduleCopyWith(
    DaySchedule value,
    $Res Function(DaySchedule) then,
  ) = _$DayScheduleCopyWithImpl<$Res, DaySchedule>;
  @useResult
  $Res call({bool isAvailable, List<TimeSlot> slots});
}

/// @nodoc
class _$DayScheduleCopyWithImpl<$Res, $Val extends DaySchedule>
    implements $DayScheduleCopyWith<$Res> {
  _$DayScheduleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DaySchedule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? isAvailable = null, Object? slots = null}) {
    return _then(
      _value.copyWith(
            isAvailable: null == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool,
            slots: null == slots
                ? _value.slots
                : slots // ignore: cast_nullable_to_non_nullable
                      as List<TimeSlot>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DayScheduleImplCopyWith<$Res>
    implements $DayScheduleCopyWith<$Res> {
  factory _$$DayScheduleImplCopyWith(
    _$DayScheduleImpl value,
    $Res Function(_$DayScheduleImpl) then,
  ) = __$$DayScheduleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isAvailable, List<TimeSlot> slots});
}

/// @nodoc
class __$$DayScheduleImplCopyWithImpl<$Res>
    extends _$DayScheduleCopyWithImpl<$Res, _$DayScheduleImpl>
    implements _$$DayScheduleImplCopyWith<$Res> {
  __$$DayScheduleImplCopyWithImpl(
    _$DayScheduleImpl _value,
    $Res Function(_$DayScheduleImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DaySchedule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? isAvailable = null, Object? slots = null}) {
    return _then(
      _$DayScheduleImpl(
        isAvailable: null == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool,
        slots: null == slots
            ? _value._slots
            : slots // ignore: cast_nullable_to_non_nullable
                  as List<TimeSlot>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DayScheduleImpl implements _DaySchedule {
  const _$DayScheduleImpl({
    required this.isAvailable,
    required final List<TimeSlot> slots,
  }) : _slots = slots;

  factory _$DayScheduleImpl.fromJson(Map<String, dynamic> json) =>
      _$$DayScheduleImplFromJson(json);

  @override
  final bool isAvailable;
  final List<TimeSlot> _slots;
  @override
  List<TimeSlot> get slots {
    if (_slots is EqualUnmodifiableListView) return _slots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_slots);
  }

  @override
  String toString() {
    return 'DaySchedule(isAvailable: $isAvailable, slots: $slots)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DayScheduleImpl &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            const DeepCollectionEquality().equals(other._slots, _slots));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    isAvailable,
    const DeepCollectionEquality().hash(_slots),
  );

  /// Create a copy of DaySchedule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DayScheduleImplCopyWith<_$DayScheduleImpl> get copyWith =>
      __$$DayScheduleImplCopyWithImpl<_$DayScheduleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DayScheduleImplToJson(this);
  }
}

abstract class _DaySchedule implements DaySchedule {
  const factory _DaySchedule({
    required final bool isAvailable,
    required final List<TimeSlot> slots,
  }) = _$DayScheduleImpl;

  factory _DaySchedule.fromJson(Map<String, dynamic> json) =
      _$DayScheduleImpl.fromJson;

  @override
  bool get isAvailable;
  @override
  List<TimeSlot> get slots;

  /// Create a copy of DaySchedule
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DayScheduleImplCopyWith<_$DayScheduleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TimeSlot _$TimeSlotFromJson(Map<String, dynamic> json) {
  return _TimeSlot.fromJson(json);
}

/// @nodoc
mixin _$TimeSlot {
  String get id => throw _privateConstructorUsedError;
  String get startTime =>
      throw _privateConstructorUsedError; // 24-hour format HH:mm
  String get endTime =>
      throw _privateConstructorUsedError; // 24-hour format HH:mm
  int get maxPatients => throw _privateConstructorUsedError;
  int get bookedPatients => throw _privateConstructorUsedError;
  bool get isAvailable => throw _privateConstructorUsedError;

  /// Serializes this TimeSlot to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TimeSlotCopyWith<TimeSlot> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimeSlotCopyWith<$Res> {
  factory $TimeSlotCopyWith(TimeSlot value, $Res Function(TimeSlot) then) =
      _$TimeSlotCopyWithImpl<$Res, TimeSlot>;
  @useResult
  $Res call({
    String id,
    String startTime,
    String endTime,
    int maxPatients,
    int bookedPatients,
    bool isAvailable,
  });
}

/// @nodoc
class _$TimeSlotCopyWithImpl<$Res, $Val extends TimeSlot>
    implements $TimeSlotCopyWith<$Res> {
  _$TimeSlotCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? maxPatients = null,
    Object? bookedPatients = null,
    Object? isAvailable = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            startTime: null == startTime
                ? _value.startTime
                : startTime // ignore: cast_nullable_to_non_nullable
                      as String,
            endTime: null == endTime
                ? _value.endTime
                : endTime // ignore: cast_nullable_to_non_nullable
                      as String,
            maxPatients: null == maxPatients
                ? _value.maxPatients
                : maxPatients // ignore: cast_nullable_to_non_nullable
                      as int,
            bookedPatients: null == bookedPatients
                ? _value.bookedPatients
                : bookedPatients // ignore: cast_nullable_to_non_nullable
                      as int,
            isAvailable: null == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TimeSlotImplCopyWith<$Res>
    implements $TimeSlotCopyWith<$Res> {
  factory _$$TimeSlotImplCopyWith(
    _$TimeSlotImpl value,
    $Res Function(_$TimeSlotImpl) then,
  ) = __$$TimeSlotImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String startTime,
    String endTime,
    int maxPatients,
    int bookedPatients,
    bool isAvailable,
  });
}

/// @nodoc
class __$$TimeSlotImplCopyWithImpl<$Res>
    extends _$TimeSlotCopyWithImpl<$Res, _$TimeSlotImpl>
    implements _$$TimeSlotImplCopyWith<$Res> {
  __$$TimeSlotImplCopyWithImpl(
    _$TimeSlotImpl _value,
    $Res Function(_$TimeSlotImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? maxPatients = null,
    Object? bookedPatients = null,
    Object? isAvailable = null,
  }) {
    return _then(
      _$TimeSlotImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        startTime: null == startTime
            ? _value.startTime
            : startTime // ignore: cast_nullable_to_non_nullable
                  as String,
        endTime: null == endTime
            ? _value.endTime
            : endTime // ignore: cast_nullable_to_non_nullable
                  as String,
        maxPatients: null == maxPatients
            ? _value.maxPatients
            : maxPatients // ignore: cast_nullable_to_non_nullable
                  as int,
        bookedPatients: null == bookedPatients
            ? _value.bookedPatients
            : bookedPatients // ignore: cast_nullable_to_non_nullable
                  as int,
        isAvailable: null == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TimeSlotImpl implements _TimeSlot {
  const _$TimeSlotImpl({
    required this.id,
    required this.startTime,
    required this.endTime,
    required this.maxPatients,
    this.bookedPatients = 0,
    this.isAvailable = true,
  });

  factory _$TimeSlotImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimeSlotImplFromJson(json);

  @override
  final String id;
  @override
  final String startTime;
  // 24-hour format HH:mm
  @override
  final String endTime;
  // 24-hour format HH:mm
  @override
  final int maxPatients;
  @override
  @JsonKey()
  final int bookedPatients;
  @override
  @JsonKey()
  final bool isAvailable;

  @override
  String toString() {
    return 'TimeSlot(id: $id, startTime: $startTime, endTime: $endTime, maxPatients: $maxPatients, bookedPatients: $bookedPatients, isAvailable: $isAvailable)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimeSlotImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.maxPatients, maxPatients) ||
                other.maxPatients == maxPatients) &&
            (identical(other.bookedPatients, bookedPatients) ||
                other.bookedPatients == bookedPatients) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    startTime,
    endTime,
    maxPatients,
    bookedPatients,
    isAvailable,
  );

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimeSlotImplCopyWith<_$TimeSlotImpl> get copyWith =>
      __$$TimeSlotImplCopyWithImpl<_$TimeSlotImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimeSlotImplToJson(this);
  }
}

abstract class _TimeSlot implements TimeSlot {
  const factory _TimeSlot({
    required final String id,
    required final String startTime,
    required final String endTime,
    required final int maxPatients,
    final int bookedPatients,
    final bool isAvailable,
  }) = _$TimeSlotImpl;

  factory _TimeSlot.fromJson(Map<String, dynamic> json) =
      _$TimeSlotImpl.fromJson;

  @override
  String get id;
  @override
  String get startTime; // 24-hour format HH:mm
  @override
  String get endTime; // 24-hour format HH:mm
  @override
  int get maxPatients;
  @override
  int get bookedPatients;
  @override
  bool get isAvailable;

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimeSlotImplCopyWith<_$TimeSlotImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
