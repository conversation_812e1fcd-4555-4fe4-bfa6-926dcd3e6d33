// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AIConfigModelImpl _$$AIConfigModelImplFromJson(Map<String, dynamic> json) =>
    _$AIConfigModelImpl(
      modelVersion: json['modelVersion'] as String,
      languageModel: json['languageModel'] as String,
      enableVoiceCommands: json['enableVoiceCommands'] as bool,
      enableChatbot: json['enableChatbot'] as bool,
      enableRecommendations: json['enableRecommendations'] as bool,
      modelParameters: json['modelParameters'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$$AIConfigModelImplToJson(_$AIConfigModelImpl instance) =>
    <String, dynamic>{
      'modelVersion': instance.modelVersion,
      'languageModel': instance.languageModel,
      'enableVoiceCommands': instance.enableVoiceCommands,
      'enableChatbot': instance.enableChatbot,
      'enableRecommendations': instance.enableRecommendations,
      'modelParameters': instance.modelParameters,
    };
