[{"type": {"import": "package:shivish/shared/repositories/media_repository.dart", "name": "MediaRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/shared/repositories/media_repository.dart", "name": "MediaRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_firestore", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:firebase_storage/firebase_storage.dart", "name": "FirebaseStorage", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_storage", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]