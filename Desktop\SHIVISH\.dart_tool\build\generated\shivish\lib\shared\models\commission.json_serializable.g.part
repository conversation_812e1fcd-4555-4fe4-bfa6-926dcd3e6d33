// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CommissionImpl _$$CommissionImplFromJson(Map<String, dynamic> json) =>
    _$CommissionImpl(
      id: json['id'] as String,
      categoryId: json['categoryId'] as String,
      categoryName: json['categoryName'] as String,
      percentage: (json['percentage'] as num).toDouble(),
      minimumAmount: (json['minimumAmount'] as num).toDouble(),
      maximumAmount: (json['maximumAmount'] as num).toDouble(),
      isActive: json['isActive'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      description: json['description'] as String?,
      excludedProducts:
          (json['excludedProducts'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      specialRates:
          (json['specialRates'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CommissionImplToJson(_$CommissionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'categoryId': instance.categoryId,
      'categoryName': instance.categoryName,
      'percentage': instance.percentage,
      'minimumAmount': instance.minimumAmount,
      'maximumAmount': instance.maximumAmount,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'description': instance.description,
      'excludedProducts': instance.excludedProducts,
      'specialRates': instance.specialRates,
    };
