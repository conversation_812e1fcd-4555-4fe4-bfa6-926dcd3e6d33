// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shopping_list_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ShoppingListModel _$ShoppingListModelFromJson(Map<String, dynamic> json) {
  return _ShoppingListModel.fromJson(json);
}

/// @nodoc
mixin _$ShoppingListModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  int get itemCount => throw _privateConstructorUsedError;
  double get totalPrice => throw _privateConstructorUsedError;
  bool get isShared => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  String get createdBy => throw _privateConstructorUsedError;
  bool get isCompleted => throw _privateConstructorUsedError;
  bool get isTemplate =>
      throw _privateConstructorUsedError; // Whether this list is a template
  List<String> get sharedWith => throw _privateConstructorUsedError;
  List<ShoppingListItem> get items =>
      throw _privateConstructorUsedError; // New fields for seller selection and price visibility
  String? get selectedSellerId => throw _privateConstructorUsedError;
  String? get selectedSellerName => throw _privateConstructorUsedError;
  bool get hasPriceList => throw _privateConstructorUsedError;
  bool get priceListViewed => throw _privateConstructorUsedError;
  String get status =>
      throw _privateConstructorUsedError; // pending, requested, priced, completed
  String get priceType =>
      throw _privateConstructorUsedError; // 'itemized' or 'total_only'
  double? get overallTotalPrice => throw _privateConstructorUsedError;

  /// Serializes this ShoppingListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShoppingListModelCopyWith<ShoppingListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShoppingListModelCopyWith<$Res> {
  factory $ShoppingListModelCopyWith(
    ShoppingListModel value,
    $Res Function(ShoppingListModel) then,
  ) = _$ShoppingListModelCopyWithImpl<$Res, ShoppingListModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    int itemCount,
    double totalPrice,
    bool isShared,
    DateTime createdAt,
    DateTime updatedAt,
    String createdBy,
    bool isCompleted,
    bool isTemplate,
    List<String> sharedWith,
    List<ShoppingListItem> items,
    String? selectedSellerId,
    String? selectedSellerName,
    bool hasPriceList,
    bool priceListViewed,
    String status,
    String priceType,
    double? overallTotalPrice,
  });
}

/// @nodoc
class _$ShoppingListModelCopyWithImpl<$Res, $Val extends ShoppingListModel>
    implements $ShoppingListModelCopyWith<$Res> {
  _$ShoppingListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? itemCount = null,
    Object? totalPrice = null,
    Object? isShared = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? createdBy = null,
    Object? isCompleted = null,
    Object? isTemplate = null,
    Object? sharedWith = null,
    Object? items = null,
    Object? selectedSellerId = freezed,
    Object? selectedSellerName = freezed,
    Object? hasPriceList = null,
    Object? priceListViewed = null,
    Object? status = null,
    Object? priceType = null,
    Object? overallTotalPrice = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            itemCount: null == itemCount
                ? _value.itemCount
                : itemCount // ignore: cast_nullable_to_non_nullable
                      as int,
            totalPrice: null == totalPrice
                ? _value.totalPrice
                : totalPrice // ignore: cast_nullable_to_non_nullable
                      as double,
            isShared: null == isShared
                ? _value.isShared
                : isShared // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            createdBy: null == createdBy
                ? _value.createdBy
                : createdBy // ignore: cast_nullable_to_non_nullable
                      as String,
            isCompleted: null == isCompleted
                ? _value.isCompleted
                : isCompleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isTemplate: null == isTemplate
                ? _value.isTemplate
                : isTemplate // ignore: cast_nullable_to_non_nullable
                      as bool,
            sharedWith: null == sharedWith
                ? _value.sharedWith
                : sharedWith // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            items: null == items
                ? _value.items
                : items // ignore: cast_nullable_to_non_nullable
                      as List<ShoppingListItem>,
            selectedSellerId: freezed == selectedSellerId
                ? _value.selectedSellerId
                : selectedSellerId // ignore: cast_nullable_to_non_nullable
                      as String?,
            selectedSellerName: freezed == selectedSellerName
                ? _value.selectedSellerName
                : selectedSellerName // ignore: cast_nullable_to_non_nullable
                      as String?,
            hasPriceList: null == hasPriceList
                ? _value.hasPriceList
                : hasPriceList // ignore: cast_nullable_to_non_nullable
                      as bool,
            priceListViewed: null == priceListViewed
                ? _value.priceListViewed
                : priceListViewed // ignore: cast_nullable_to_non_nullable
                      as bool,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            priceType: null == priceType
                ? _value.priceType
                : priceType // ignore: cast_nullable_to_non_nullable
                      as String,
            overallTotalPrice: freezed == overallTotalPrice
                ? _value.overallTotalPrice
                : overallTotalPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ShoppingListModelImplCopyWith<$Res>
    implements $ShoppingListModelCopyWith<$Res> {
  factory _$$ShoppingListModelImplCopyWith(
    _$ShoppingListModelImpl value,
    $Res Function(_$ShoppingListModelImpl) then,
  ) = __$$ShoppingListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    int itemCount,
    double totalPrice,
    bool isShared,
    DateTime createdAt,
    DateTime updatedAt,
    String createdBy,
    bool isCompleted,
    bool isTemplate,
    List<String> sharedWith,
    List<ShoppingListItem> items,
    String? selectedSellerId,
    String? selectedSellerName,
    bool hasPriceList,
    bool priceListViewed,
    String status,
    String priceType,
    double? overallTotalPrice,
  });
}

/// @nodoc
class __$$ShoppingListModelImplCopyWithImpl<$Res>
    extends _$ShoppingListModelCopyWithImpl<$Res, _$ShoppingListModelImpl>
    implements _$$ShoppingListModelImplCopyWith<$Res> {
  __$$ShoppingListModelImplCopyWithImpl(
    _$ShoppingListModelImpl _value,
    $Res Function(_$ShoppingListModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? itemCount = null,
    Object? totalPrice = null,
    Object? isShared = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? createdBy = null,
    Object? isCompleted = null,
    Object? isTemplate = null,
    Object? sharedWith = null,
    Object? items = null,
    Object? selectedSellerId = freezed,
    Object? selectedSellerName = freezed,
    Object? hasPriceList = null,
    Object? priceListViewed = null,
    Object? status = null,
    Object? priceType = null,
    Object? overallTotalPrice = freezed,
  }) {
    return _then(
      _$ShoppingListModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        itemCount: null == itemCount
            ? _value.itemCount
            : itemCount // ignore: cast_nullable_to_non_nullable
                  as int,
        totalPrice: null == totalPrice
            ? _value.totalPrice
            : totalPrice // ignore: cast_nullable_to_non_nullable
                  as double,
        isShared: null == isShared
            ? _value.isShared
            : isShared // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        createdBy: null == createdBy
            ? _value.createdBy
            : createdBy // ignore: cast_nullable_to_non_nullable
                  as String,
        isCompleted: null == isCompleted
            ? _value.isCompleted
            : isCompleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isTemplate: null == isTemplate
            ? _value.isTemplate
            : isTemplate // ignore: cast_nullable_to_non_nullable
                  as bool,
        sharedWith: null == sharedWith
            ? _value._sharedWith
            : sharedWith // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        items: null == items
            ? _value._items
            : items // ignore: cast_nullable_to_non_nullable
                  as List<ShoppingListItem>,
        selectedSellerId: freezed == selectedSellerId
            ? _value.selectedSellerId
            : selectedSellerId // ignore: cast_nullable_to_non_nullable
                  as String?,
        selectedSellerName: freezed == selectedSellerName
            ? _value.selectedSellerName
            : selectedSellerName // ignore: cast_nullable_to_non_nullable
                  as String?,
        hasPriceList: null == hasPriceList
            ? _value.hasPriceList
            : hasPriceList // ignore: cast_nullable_to_non_nullable
                  as bool,
        priceListViewed: null == priceListViewed
            ? _value.priceListViewed
            : priceListViewed // ignore: cast_nullable_to_non_nullable
                  as bool,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        priceType: null == priceType
            ? _value.priceType
            : priceType // ignore: cast_nullable_to_non_nullable
                  as String,
        overallTotalPrice: freezed == overallTotalPrice
            ? _value.overallTotalPrice
            : overallTotalPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ShoppingListModelImpl implements _ShoppingListModel {
  const _$ShoppingListModelImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.itemCount,
    required this.totalPrice,
    required this.isShared,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.isCompleted = false,
    this.isTemplate = false,
    final List<String> sharedWith = const [],
    final List<ShoppingListItem> items = const [],
    this.selectedSellerId,
    this.selectedSellerName,
    this.hasPriceList = false,
    this.priceListViewed = false,
    this.status = 'pending',
    this.priceType = 'itemized',
    this.overallTotalPrice,
  }) : _sharedWith = sharedWith,
       _items = items;

  factory _$ShoppingListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShoppingListModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final int itemCount;
  @override
  final double totalPrice;
  @override
  final bool isShared;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final String createdBy;
  @override
  @JsonKey()
  final bool isCompleted;
  @override
  @JsonKey()
  final bool isTemplate;
  // Whether this list is a template
  final List<String> _sharedWith;
  // Whether this list is a template
  @override
  @JsonKey()
  List<String> get sharedWith {
    if (_sharedWith is EqualUnmodifiableListView) return _sharedWith;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sharedWith);
  }

  final List<ShoppingListItem> _items;
  @override
  @JsonKey()
  List<ShoppingListItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  // New fields for seller selection and price visibility
  @override
  final String? selectedSellerId;
  @override
  final String? selectedSellerName;
  @override
  @JsonKey()
  final bool hasPriceList;
  @override
  @JsonKey()
  final bool priceListViewed;
  @override
  @JsonKey()
  final String status;
  // pending, requested, priced, completed
  @override
  @JsonKey()
  final String priceType;
  // 'itemized' or 'total_only'
  @override
  final double? overallTotalPrice;

  @override
  String toString() {
    return 'ShoppingListModel(id: $id, name: $name, description: $description, itemCount: $itemCount, totalPrice: $totalPrice, isShared: $isShared, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, isCompleted: $isCompleted, isTemplate: $isTemplate, sharedWith: $sharedWith, items: $items, selectedSellerId: $selectedSellerId, selectedSellerName: $selectedSellerName, hasPriceList: $hasPriceList, priceListViewed: $priceListViewed, status: $status, priceType: $priceType, overallTotalPrice: $overallTotalPrice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShoppingListModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.itemCount, itemCount) ||
                other.itemCount == itemCount) &&
            (identical(other.totalPrice, totalPrice) ||
                other.totalPrice == totalPrice) &&
            (identical(other.isShared, isShared) ||
                other.isShared == isShared) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.isTemplate, isTemplate) ||
                other.isTemplate == isTemplate) &&
            const DeepCollectionEquality().equals(
              other._sharedWith,
              _sharedWith,
            ) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.selectedSellerId, selectedSellerId) ||
                other.selectedSellerId == selectedSellerId) &&
            (identical(other.selectedSellerName, selectedSellerName) ||
                other.selectedSellerName == selectedSellerName) &&
            (identical(other.hasPriceList, hasPriceList) ||
                other.hasPriceList == hasPriceList) &&
            (identical(other.priceListViewed, priceListViewed) ||
                other.priceListViewed == priceListViewed) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.priceType, priceType) ||
                other.priceType == priceType) &&
            (identical(other.overallTotalPrice, overallTotalPrice) ||
                other.overallTotalPrice == overallTotalPrice));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    description,
    itemCount,
    totalPrice,
    isShared,
    createdAt,
    updatedAt,
    createdBy,
    isCompleted,
    isTemplate,
    const DeepCollectionEquality().hash(_sharedWith),
    const DeepCollectionEquality().hash(_items),
    selectedSellerId,
    selectedSellerName,
    hasPriceList,
    priceListViewed,
    status,
    priceType,
    overallTotalPrice,
  ]);

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShoppingListModelImplCopyWith<_$ShoppingListModelImpl> get copyWith =>
      __$$ShoppingListModelImplCopyWithImpl<_$ShoppingListModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ShoppingListModelImplToJson(this);
  }
}

abstract class _ShoppingListModel implements ShoppingListModel {
  const factory _ShoppingListModel({
    required final String id,
    required final String name,
    required final String description,
    required final int itemCount,
    required final double totalPrice,
    required final bool isShared,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    required final String createdBy,
    final bool isCompleted,
    final bool isTemplate,
    final List<String> sharedWith,
    final List<ShoppingListItem> items,
    final String? selectedSellerId,
    final String? selectedSellerName,
    final bool hasPriceList,
    final bool priceListViewed,
    final String status,
    final String priceType,
    final double? overallTotalPrice,
  }) = _$ShoppingListModelImpl;

  factory _ShoppingListModel.fromJson(Map<String, dynamic> json) =
      _$ShoppingListModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  int get itemCount;
  @override
  double get totalPrice;
  @override
  bool get isShared;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  String get createdBy;
  @override
  bool get isCompleted;
  @override
  bool get isTemplate; // Whether this list is a template
  @override
  List<String> get sharedWith;
  @override
  List<ShoppingListItem> get items; // New fields for seller selection and price visibility
  @override
  String? get selectedSellerId;
  @override
  String? get selectedSellerName;
  @override
  bool get hasPriceList;
  @override
  bool get priceListViewed;
  @override
  String get status; // pending, requested, priced, completed
  @override
  String get priceType; // 'itemized' or 'total_only'
  @override
  double? get overallTotalPrice;

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShoppingListModelImplCopyWith<_$ShoppingListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ShoppingListItem _$ShoppingListItemFromJson(Map<String, dynamic> json) {
  return _ShoppingListItem.fromJson(json);
}

/// @nodoc
mixin _$ShoppingListItem {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  bool get isChecked => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this ShoppingListItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ShoppingListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShoppingListItemCopyWith<ShoppingListItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShoppingListItemCopyWith<$Res> {
  factory $ShoppingListItemCopyWith(
    ShoppingListItem value,
    $Res Function(ShoppingListItem) then,
  ) = _$ShoppingListItemCopyWithImpl<$Res, ShoppingListItem>;
  @useResult
  $Res call({
    String id,
    String name,
    double price,
    int quantity,
    bool isChecked,
    String? notes,
  });
}

/// @nodoc
class _$ShoppingListItemCopyWithImpl<$Res, $Val extends ShoppingListItem>
    implements $ShoppingListItemCopyWith<$Res> {
  _$ShoppingListItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShoppingListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? price = null,
    Object? quantity = null,
    Object? isChecked = null,
    Object? notes = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            quantity: null == quantity
                ? _value.quantity
                : quantity // ignore: cast_nullable_to_non_nullable
                      as int,
            isChecked: null == isChecked
                ? _value.isChecked
                : isChecked // ignore: cast_nullable_to_non_nullable
                      as bool,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ShoppingListItemImplCopyWith<$Res>
    implements $ShoppingListItemCopyWith<$Res> {
  factory _$$ShoppingListItemImplCopyWith(
    _$ShoppingListItemImpl value,
    $Res Function(_$ShoppingListItemImpl) then,
  ) = __$$ShoppingListItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    double price,
    int quantity,
    bool isChecked,
    String? notes,
  });
}

/// @nodoc
class __$$ShoppingListItemImplCopyWithImpl<$Res>
    extends _$ShoppingListItemCopyWithImpl<$Res, _$ShoppingListItemImpl>
    implements _$$ShoppingListItemImplCopyWith<$Res> {
  __$$ShoppingListItemImplCopyWithImpl(
    _$ShoppingListItemImpl _value,
    $Res Function(_$ShoppingListItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ShoppingListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? price = null,
    Object? quantity = null,
    Object? isChecked = null,
    Object? notes = freezed,
  }) {
    return _then(
      _$ShoppingListItemImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        quantity: null == quantity
            ? _value.quantity
            : quantity // ignore: cast_nullable_to_non_nullable
                  as int,
        isChecked: null == isChecked
            ? _value.isChecked
            : isChecked // ignore: cast_nullable_to_non_nullable
                  as bool,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ShoppingListItemImpl implements _ShoppingListItem {
  const _$ShoppingListItemImpl({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    required this.isChecked,
    this.notes,
  });

  factory _$ShoppingListItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShoppingListItemImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final double price;
  @override
  final int quantity;
  @override
  final bool isChecked;
  @override
  final String? notes;

  @override
  String toString() {
    return 'ShoppingListItem(id: $id, name: $name, price: $price, quantity: $quantity, isChecked: $isChecked, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShoppingListItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.isChecked, isChecked) ||
                other.isChecked == isChecked) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, price, quantity, isChecked, notes);

  /// Create a copy of ShoppingListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShoppingListItemImplCopyWith<_$ShoppingListItemImpl> get copyWith =>
      __$$ShoppingListItemImplCopyWithImpl<_$ShoppingListItemImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ShoppingListItemImplToJson(this);
  }
}

abstract class _ShoppingListItem implements ShoppingListItem {
  const factory _ShoppingListItem({
    required final String id,
    required final String name,
    required final double price,
    required final int quantity,
    required final bool isChecked,
    final String? notes,
  }) = _$ShoppingListItemImpl;

  factory _ShoppingListItem.fromJson(Map<String, dynamic> json) =
      _$ShoppingListItemImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  double get price;
  @override
  int get quantity;
  @override
  bool get isChecked;
  @override
  String? get notes;

  /// Create a copy of ShoppingListItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShoppingListItemImplCopyWith<_$ShoppingListItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
