// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'security_question_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SecurityQuestionModelImpl _$$SecurityQuestionModelImplFromJson(
  Map<String, dynamic> json,
) => _$SecurityQuestionModelImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  question: json['question'] as String,
  answer: json['answer'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$SecurityQuestionModelImplToJson(
  _$SecurityQuestionModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'question': instance.question,
  'answer': instance.answer,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
