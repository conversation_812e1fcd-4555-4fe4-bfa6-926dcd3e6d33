[{"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 1, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:shivish/shared/core/di/firebase_module.dart", "name": "FirebaseModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "firestore"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "firestore", "orderPosition": 0}, {"type": {"import": "package:firebase_storage/firebase_storage.dart", "name": "FirebaseStorage", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:firebase_storage/firebase_storage.dart", "name": "FirebaseStorage", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 1, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:shivish/shared/core/di/firebase_module.dart", "name": "FirebaseModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "storage"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "storage", "orderPosition": 0}]