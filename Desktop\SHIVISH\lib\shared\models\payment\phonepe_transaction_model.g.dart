// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'phonepe_transaction_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PhonePeTransactionModelImpl _$$PhonePeTransactionModelImplFromJson(
  Map<String, dynamic> json,
) => _$PhonePeTransactionModelImpl(
  transactionId: json['transactionId'] as String,
  merchantTransactionId: json['merchantTransactionId'] as String,
  orderId: json['orderId'] as String?,
  amount: (json['amount'] as num).toInt(),
  status:
      $enumDecodeNullable(_$PhonePeTransactionStatusEnumMap, json['status']) ??
      PhonePeTransactionStatus.pending,
  responseCode: json['responseCode'] as String?,
  responseMessage: json['responseMessage'] as String?,
  paymentInstrumentType: json['paymentInstrumentType'] as String?,
  paymentInstrumentDetails:
      json['paymentInstrumentDetails'] as Map<String, dynamic>?,
  customerId: json['customerId'] as String?,
  customerPhone: json['customerPhone'] as String?,
  customerEmail: json['customerEmail'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  rawResponse: json['rawResponse'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$PhonePeTransactionModelImplToJson(
  _$PhonePeTransactionModelImpl instance,
) => <String, dynamic>{
  'transactionId': instance.transactionId,
  'merchantTransactionId': instance.merchantTransactionId,
  'orderId': instance.orderId,
  'amount': instance.amount,
  'status': _$PhonePeTransactionStatusEnumMap[instance.status]!,
  'responseCode': instance.responseCode,
  'responseMessage': instance.responseMessage,
  'paymentInstrumentType': instance.paymentInstrumentType,
  'paymentInstrumentDetails': instance.paymentInstrumentDetails,
  'customerId': instance.customerId,
  'customerPhone': instance.customerPhone,
  'customerEmail': instance.customerEmail,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'rawResponse': instance.rawResponse,
};

const _$PhonePeTransactionStatusEnumMap = {
  PhonePeTransactionStatus.pending: 'PENDING',
  PhonePeTransactionStatus.success: 'SUCCESS',
  PhonePeTransactionStatus.failed: 'FAILED',
  PhonePeTransactionStatus.cancelled: 'CANCELLED',
  PhonePeTransactionStatus.unknown: 'UNKNOWN',
};
