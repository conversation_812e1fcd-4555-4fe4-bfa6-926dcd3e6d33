// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_command_training.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoiceCommandTrainingImpl _$$VoiceCommandTrainingImplFromJson(
  Map<String, dynamic> json,
) => _$VoiceCommandTrainingImpl(
  id: json['id'] as String,
  command: json['command'] as String,
  description: json['description'] as String,
  action: json['action'] as String,
  parameters: json['parameters'] as Map<String, dynamic>,
  isEnabled: json['isEnabled'] as bool,
  createdAt: json['createdAt'] as String,
  updatedAt: json['updatedAt'] as String,
);

Map<String, dynamic> _$$VoiceCommandTrainingImplToJson(
  _$VoiceCommandTrainingImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'command': instance.command,
  'description': instance.description,
  'action': instance.action,
  'parameters': instance.parameters,
  'isEnabled': instance.isEnabled,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
