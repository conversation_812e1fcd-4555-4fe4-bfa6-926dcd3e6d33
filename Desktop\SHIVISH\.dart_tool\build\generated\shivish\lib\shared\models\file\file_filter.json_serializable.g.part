// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FileFilterImpl _$$FileFilterImplFromJson(Map<String, dynamic> json) =>
    _$FileFilterImpl(
      extensions:
          (json['extensions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      showFavorites: json['showFavorites'] as bool? ?? false,
      searchQuery: json['searchQuery'] as String?,
      extension: json['extension'] as String?,
      mimeType: json['mimeType'] as String?,
      minSize: (json['minSize'] as num?)?.toInt(),
      maxSize: (json['maxSize'] as num?)?.toInt(),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      sort:
          $enumDecodeNullable(_$FileSortEnumMap, json['sort']) ??
          FileSort.nameAsc,
    );

Map<String, dynamic> _$$FileFilterImplToJson(_$FileFilterImpl instance) =>
    <String, dynamic>{
      'extensions': instance.extensions,
      'tags': instance.tags,
      'showFavorites': instance.showFavorites,
      'searchQuery': instance.searchQuery,
      'extension': instance.extension,
      'mimeType': instance.mimeType,
      'minSize': instance.minSize,
      'maxSize': instance.maxSize,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'sort': _$FileSortEnumMap[instance.sort]!,
    };

const _$FileSortEnumMap = {
  FileSort.nameAsc: 'nameAsc',
  FileSort.nameDesc: 'nameDesc',
  FileSort.dateAsc: 'dateAsc',
  FileSort.dateDesc: 'dateDesc',
  FileSort.sizeAsc: 'sizeAsc',
  FileSort.sizeDesc: 'sizeDesc',
  FileSort.typeAsc: 'typeAsc',
  FileSort.typeDesc: 'typeDesc',
};
