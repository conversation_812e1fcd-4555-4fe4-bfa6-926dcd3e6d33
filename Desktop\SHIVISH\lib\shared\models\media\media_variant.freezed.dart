// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_variant.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

MediaVariant _$MediaVariantFromJson(Map<String, dynamic> json) {
  return _MediaVariant.fromJson(json);
}

/// @nodoc
mixin _$MediaVariant {
  String get url => throw _privateConstructorUsedError;
  String get quality => throw _privateConstructorUsedError;
  int get width => throw _privateConstructorUsedError;
  int get height => throw _privateConstructorUsedError;
  int get bitrate => throw _privateConstructorUsedError;
  String? get format => throw _privateConstructorUsedError;

  /// Serializes this MediaVariant to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MediaVariantCopyWith<MediaVariant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaVariantCopyWith<$Res> {
  factory $MediaVariantCopyWith(
    MediaVariant value,
    $Res Function(MediaVariant) then,
  ) = _$MediaVariantCopyWithImpl<$Res, MediaVariant>;
  @useResult
  $Res call({
    String url,
    String quality,
    int width,
    int height,
    int bitrate,
    String? format,
  });
}

/// @nodoc
class _$MediaVariantCopyWithImpl<$Res, $Val extends MediaVariant>
    implements $MediaVariantCopyWith<$Res> {
  _$MediaVariantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = null,
    Object? quality = null,
    Object? width = null,
    Object? height = null,
    Object? bitrate = null,
    Object? format = freezed,
  }) {
    return _then(
      _value.copyWith(
            url: null == url
                ? _value.url
                : url // ignore: cast_nullable_to_non_nullable
                      as String,
            quality: null == quality
                ? _value.quality
                : quality // ignore: cast_nullable_to_non_nullable
                      as String,
            width: null == width
                ? _value.width
                : width // ignore: cast_nullable_to_non_nullable
                      as int,
            height: null == height
                ? _value.height
                : height // ignore: cast_nullable_to_non_nullable
                      as int,
            bitrate: null == bitrate
                ? _value.bitrate
                : bitrate // ignore: cast_nullable_to_non_nullable
                      as int,
            format: freezed == format
                ? _value.format
                : format // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MediaVariantImplCopyWith<$Res>
    implements $MediaVariantCopyWith<$Res> {
  factory _$$MediaVariantImplCopyWith(
    _$MediaVariantImpl value,
    $Res Function(_$MediaVariantImpl) then,
  ) = __$$MediaVariantImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String url,
    String quality,
    int width,
    int height,
    int bitrate,
    String? format,
  });
}

/// @nodoc
class __$$MediaVariantImplCopyWithImpl<$Res>
    extends _$MediaVariantCopyWithImpl<$Res, _$MediaVariantImpl>
    implements _$$MediaVariantImplCopyWith<$Res> {
  __$$MediaVariantImplCopyWithImpl(
    _$MediaVariantImpl _value,
    $Res Function(_$MediaVariantImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = null,
    Object? quality = null,
    Object? width = null,
    Object? height = null,
    Object? bitrate = null,
    Object? format = freezed,
  }) {
    return _then(
      _$MediaVariantImpl(
        url: null == url
            ? _value.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        quality: null == quality
            ? _value.quality
            : quality // ignore: cast_nullable_to_non_nullable
                  as String,
        width: null == width
            ? _value.width
            : width // ignore: cast_nullable_to_non_nullable
                  as int,
        height: null == height
            ? _value.height
            : height // ignore: cast_nullable_to_non_nullable
                  as int,
        bitrate: null == bitrate
            ? _value.bitrate
            : bitrate // ignore: cast_nullable_to_non_nullable
                  as int,
        format: freezed == format
            ? _value.format
            : format // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaVariantImpl implements _MediaVariant {
  const _$MediaVariantImpl({
    required this.url,
    required this.quality,
    required this.width,
    required this.height,
    required this.bitrate,
    this.format,
  });

  factory _$MediaVariantImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaVariantImplFromJson(json);

  @override
  final String url;
  @override
  final String quality;
  @override
  final int width;
  @override
  final int height;
  @override
  final int bitrate;
  @override
  final String? format;

  @override
  String toString() {
    return 'MediaVariant(url: $url, quality: $quality, width: $width, height: $height, bitrate: $bitrate, format: $format)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaVariantImpl &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.quality, quality) || other.quality == quality) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.bitrate, bitrate) || other.bitrate == bitrate) &&
            (identical(other.format, format) || other.format == format));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, url, quality, width, height, bitrate, format);

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaVariantImplCopyWith<_$MediaVariantImpl> get copyWith =>
      __$$MediaVariantImplCopyWithImpl<_$MediaVariantImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaVariantImplToJson(this);
  }
}

abstract class _MediaVariant implements MediaVariant {
  const factory _MediaVariant({
    required final String url,
    required final String quality,
    required final int width,
    required final int height,
    required final int bitrate,
    final String? format,
  }) = _$MediaVariantImpl;

  factory _MediaVariant.fromJson(Map<String, dynamic> json) =
      _$MediaVariantImpl.fromJson;

  @override
  String get url;
  @override
  String get quality;
  @override
  int get width;
  @override
  int get height;
  @override
  int get bitrate;
  @override
  String? get format;

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaVariantImplCopyWith<_$MediaVariantImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
