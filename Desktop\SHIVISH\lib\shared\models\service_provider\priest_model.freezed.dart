// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'priest_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PriestModel _$PriestModelFromJson(Map<String, dynamic> json) {
  return _PriestModel.fromJson(json);
}

/// @nodoc
mixin _$PriestModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get specialization => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get totalReviews => throw _privateConstructorUsedError;
  List<String> get services => throw _privateConstructorUsedError;
  List<String> get languages => throw _privateConstructorUsedError;
  List<String> get certifications => throw _privateConstructorUsedError;
  List<String> get experience => throw _privateConstructorUsedError;
  String get profileImage => throw _privateConstructorUsedError;
  String get coverImage => throw _privateConstructorUsedError;
  Map<String, dynamic> get contactInfo => throw _privateConstructorUsedError;
  Map<String, dynamic> get availability => throw _privateConstructorUsedError;
  Map<String, dynamic> get pricing => throw _privateConstructorUsedError;
  Map<String, dynamic> get location => throw _privateConstructorUsedError;
  bool get isAvailable => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this PriestModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PriestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PriestModelCopyWith<PriestModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PriestModelCopyWith<$Res> {
  factory $PriestModelCopyWith(
    PriestModel value,
    $Res Function(PriestModel) then,
  ) = _$PriestModelCopyWithImpl<$Res, PriestModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String specialization,
    String description,
    double rating,
    int totalReviews,
    List<String> services,
    List<String> languages,
    List<String> certifications,
    List<String> experience,
    String profileImage,
    String coverImage,
    Map<String, dynamic> contactInfo,
    Map<String, dynamic> availability,
    Map<String, dynamic> pricing,
    Map<String, dynamic> location,
    bool isAvailable,
    bool isVerified,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$PriestModelCopyWithImpl<$Res, $Val extends PriestModel>
    implements $PriestModelCopyWith<$Res> {
  _$PriestModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PriestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? specialization = null,
    Object? description = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? services = null,
    Object? languages = null,
    Object? certifications = null,
    Object? experience = null,
    Object? profileImage = null,
    Object? coverImage = null,
    Object? contactInfo = null,
    Object? availability = null,
    Object? pricing = null,
    Object? location = null,
    Object? isAvailable = null,
    Object? isVerified = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            specialization: null == specialization
                ? _value.specialization
                : specialization // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            totalReviews: null == totalReviews
                ? _value.totalReviews
                : totalReviews // ignore: cast_nullable_to_non_nullable
                      as int,
            services: null == services
                ? _value.services
                : services // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            languages: null == languages
                ? _value.languages
                : languages // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            certifications: null == certifications
                ? _value.certifications
                : certifications // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            experience: null == experience
                ? _value.experience
                : experience // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            profileImage: null == profileImage
                ? _value.profileImage
                : profileImage // ignore: cast_nullable_to_non_nullable
                      as String,
            coverImage: null == coverImage
                ? _value.coverImage
                : coverImage // ignore: cast_nullable_to_non_nullable
                      as String,
            contactInfo: null == contactInfo
                ? _value.contactInfo
                : contactInfo // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            availability: null == availability
                ? _value.availability
                : availability // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            pricing: null == pricing
                ? _value.pricing
                : pricing // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            location: null == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            isAvailable: null == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PriestModelImplCopyWith<$Res>
    implements $PriestModelCopyWith<$Res> {
  factory _$$PriestModelImplCopyWith(
    _$PriestModelImpl value,
    $Res Function(_$PriestModelImpl) then,
  ) = __$$PriestModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String specialization,
    String description,
    double rating,
    int totalReviews,
    List<String> services,
    List<String> languages,
    List<String> certifications,
    List<String> experience,
    String profileImage,
    String coverImage,
    Map<String, dynamic> contactInfo,
    Map<String, dynamic> availability,
    Map<String, dynamic> pricing,
    Map<String, dynamic> location,
    bool isAvailable,
    bool isVerified,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$PriestModelImplCopyWithImpl<$Res>
    extends _$PriestModelCopyWithImpl<$Res, _$PriestModelImpl>
    implements _$$PriestModelImplCopyWith<$Res> {
  __$$PriestModelImplCopyWithImpl(
    _$PriestModelImpl _value,
    $Res Function(_$PriestModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PriestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? specialization = null,
    Object? description = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? services = null,
    Object? languages = null,
    Object? certifications = null,
    Object? experience = null,
    Object? profileImage = null,
    Object? coverImage = null,
    Object? contactInfo = null,
    Object? availability = null,
    Object? pricing = null,
    Object? location = null,
    Object? isAvailable = null,
    Object? isVerified = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$PriestModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        specialization: null == specialization
            ? _value.specialization
            : specialization // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        totalReviews: null == totalReviews
            ? _value.totalReviews
            : totalReviews // ignore: cast_nullable_to_non_nullable
                  as int,
        services: null == services
            ? _value._services
            : services // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        languages: null == languages
            ? _value._languages
            : languages // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        certifications: null == certifications
            ? _value._certifications
            : certifications // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        experience: null == experience
            ? _value._experience
            : experience // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        profileImage: null == profileImage
            ? _value.profileImage
            : profileImage // ignore: cast_nullable_to_non_nullable
                  as String,
        coverImage: null == coverImage
            ? _value.coverImage
            : coverImage // ignore: cast_nullable_to_non_nullable
                  as String,
        contactInfo: null == contactInfo
            ? _value._contactInfo
            : contactInfo // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        availability: null == availability
            ? _value._availability
            : availability // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        pricing: null == pricing
            ? _value._pricing
            : pricing // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        location: null == location
            ? _value._location
            : location // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        isAvailable: null == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PriestModelImpl implements _PriestModel {
  const _$PriestModelImpl({
    required this.id,
    required this.name,
    required this.specialization,
    required this.description,
    required this.rating,
    required this.totalReviews,
    required final List<String> services,
    required final List<String> languages,
    required final List<String> certifications,
    required final List<String> experience,
    required this.profileImage,
    required this.coverImage,
    required final Map<String, dynamic> contactInfo,
    required final Map<String, dynamic> availability,
    required final Map<String, dynamic> pricing,
    required final Map<String, dynamic> location,
    required this.isAvailable,
    required this.isVerified,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  }) : _services = services,
       _languages = languages,
       _certifications = certifications,
       _experience = experience,
       _contactInfo = contactInfo,
       _availability = availability,
       _pricing = pricing,
       _location = location;

  factory _$PriestModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PriestModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String specialization;
  @override
  final String description;
  @override
  final double rating;
  @override
  final int totalReviews;
  final List<String> _services;
  @override
  List<String> get services {
    if (_services is EqualUnmodifiableListView) return _services;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_services);
  }

  final List<String> _languages;
  @override
  List<String> get languages {
    if (_languages is EqualUnmodifiableListView) return _languages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_languages);
  }

  final List<String> _certifications;
  @override
  List<String> get certifications {
    if (_certifications is EqualUnmodifiableListView) return _certifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_certifications);
  }

  final List<String> _experience;
  @override
  List<String> get experience {
    if (_experience is EqualUnmodifiableListView) return _experience;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_experience);
  }

  @override
  final String profileImage;
  @override
  final String coverImage;
  final Map<String, dynamic> _contactInfo;
  @override
  Map<String, dynamic> get contactInfo {
    if (_contactInfo is EqualUnmodifiableMapView) return _contactInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_contactInfo);
  }

  final Map<String, dynamic> _availability;
  @override
  Map<String, dynamic> get availability {
    if (_availability is EqualUnmodifiableMapView) return _availability;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_availability);
  }

  final Map<String, dynamic> _pricing;
  @override
  Map<String, dynamic> get pricing {
    if (_pricing is EqualUnmodifiableMapView) return _pricing;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_pricing);
  }

  final Map<String, dynamic> _location;
  @override
  Map<String, dynamic> get location {
    if (_location is EqualUnmodifiableMapView) return _location;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_location);
  }

  @override
  final bool isAvailable;
  @override
  final bool isVerified;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'PriestModel(id: $id, name: $name, specialization: $specialization, description: $description, rating: $rating, totalReviews: $totalReviews, services: $services, languages: $languages, certifications: $certifications, experience: $experience, profileImage: $profileImage, coverImage: $coverImage, contactInfo: $contactInfo, availability: $availability, pricing: $pricing, location: $location, isAvailable: $isAvailable, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PriestModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.specialization, specialization) ||
                other.specialization == specialization) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalReviews, totalReviews) ||
                other.totalReviews == totalReviews) &&
            const DeepCollectionEquality().equals(other._services, _services) &&
            const DeepCollectionEquality().equals(
              other._languages,
              _languages,
            ) &&
            const DeepCollectionEquality().equals(
              other._certifications,
              _certifications,
            ) &&
            const DeepCollectionEquality().equals(
              other._experience,
              _experience,
            ) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.coverImage, coverImage) ||
                other.coverImage == coverImage) &&
            const DeepCollectionEquality().equals(
              other._contactInfo,
              _contactInfo,
            ) &&
            const DeepCollectionEquality().equals(
              other._availability,
              _availability,
            ) &&
            const DeepCollectionEquality().equals(other._pricing, _pricing) &&
            const DeepCollectionEquality().equals(other._location, _location) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    specialization,
    description,
    rating,
    totalReviews,
    const DeepCollectionEquality().hash(_services),
    const DeepCollectionEquality().hash(_languages),
    const DeepCollectionEquality().hash(_certifications),
    const DeepCollectionEquality().hash(_experience),
    profileImage,
    coverImage,
    const DeepCollectionEquality().hash(_contactInfo),
    const DeepCollectionEquality().hash(_availability),
    const DeepCollectionEquality().hash(_pricing),
    const DeepCollectionEquality().hash(_location),
    isAvailable,
    isVerified,
    createdAt,
    updatedAt,
    isDeleted,
  ]);

  /// Create a copy of PriestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PriestModelImplCopyWith<_$PriestModelImpl> get copyWith =>
      __$$PriestModelImplCopyWithImpl<_$PriestModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PriestModelImplToJson(this);
  }
}

abstract class _PriestModel implements PriestModel {
  const factory _PriestModel({
    required final String id,
    required final String name,
    required final String specialization,
    required final String description,
    required final double rating,
    required final int totalReviews,
    required final List<String> services,
    required final List<String> languages,
    required final List<String> certifications,
    required final List<String> experience,
    required final String profileImage,
    required final String coverImage,
    required final Map<String, dynamic> contactInfo,
    required final Map<String, dynamic> availability,
    required final Map<String, dynamic> pricing,
    required final Map<String, dynamic> location,
    required final bool isAvailable,
    required final bool isVerified,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$PriestModelImpl;

  factory _PriestModel.fromJson(Map<String, dynamic> json) =
      _$PriestModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get specialization;
  @override
  String get description;
  @override
  double get rating;
  @override
  int get totalReviews;
  @override
  List<String> get services;
  @override
  List<String> get languages;
  @override
  List<String> get certifications;
  @override
  List<String> get experience;
  @override
  String get profileImage;
  @override
  String get coverImage;
  @override
  Map<String, dynamic> get contactInfo;
  @override
  Map<String, dynamic> get availability;
  @override
  Map<String, dynamic> get pricing;
  @override
  Map<String, dynamic> get location;
  @override
  bool get isAvailable;
  @override
  bool get isVerified;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of PriestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PriestModelImplCopyWith<_$PriestModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
