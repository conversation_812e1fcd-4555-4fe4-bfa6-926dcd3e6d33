[{"type": {"import": "package:shivish/shared/services/language_service.dart", "name": "LanguageService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/shared/services/language_service.dart", "name": "LanguageService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 1, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shared_preferences/shared_preferences.dart", "name": "SharedPreferences", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:shared_preferences/src/shared_preferences_legacy.dart"]}, "instanceName": null, "paramName": "_prefs", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]