// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BannerPricingImpl _$$BannerPricingImplFromJson(Map<String, dynamic> json) =>
    _$BannerPricingImpl(
      id: json['id'] as String,
      basePrice: (json['basePrice'] as num).toDouble(),
      durationPrices: (json['durationPrices'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      priorityMultiplier: (json['priorityMultiplier'] as Map<String, dynamic>)
          .map((k, e) => MapEntry(k, (e as num).toDouble())),
      categoryMultipliers: (json['categoryMultipliers'] as Map<String, dynamic>)
          .map((k, e) => MapEntry(k, (e as num).toDouble())),
      seasonalMultipliers: (json['seasonalMultipliers'] as Map<String, dynamic>)
          .map((k, e) => MapEntry(k, (e as num).toDouble())),
      aiSuggestedPrice: (json['aiSuggestedPrice'] as num).toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$$BannerPricingImplToJson(_$BannerPricingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'basePrice': instance.basePrice,
      'durationPrices': instance.durationPrices,
      'priorityMultiplier': instance.priorityMultiplier,
      'categoryMultipliers': instance.categoryMultipliers,
      'seasonalMultipliers': instance.seasonalMultipliers,
      'aiSuggestedPrice': instance.aiSuggestedPrice,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'isActive': instance.isActive,
    };
