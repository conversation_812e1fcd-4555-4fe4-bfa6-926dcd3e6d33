// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentStateImpl _$$PaymentStateImplFromJson(Map<String, dynamic> json) =>
    _$PaymentStateImpl(
      isLoading: json['isLoading'] as bool? ?? false,
      hasData: json['hasData'] as bool? ?? false,
      hasError: json['hasError'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
      summary: json['summary'] == null
          ? null
          : PaymentSummary.fromJson(json['summary'] as Map<String, dynamic>),
      transactions:
          (json['transactions'] as List<dynamic>?)
              ?.map((e) => TransactionModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      settlements:
          (json['settlements'] as List<dynamic>?)
              ?.map((e) => SettlementModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      bankAccounts:
          (json['bankAccounts'] as List<dynamic>?)
              ?.map((e) => BankAccountModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$PaymentStateImplToJson(_$PaymentStateImpl instance) =>
    <String, dynamic>{
      'isLoading': instance.isLoading,
      'hasData': instance.hasData,
      'hasError': instance.hasError,
      'errorMessage': instance.errorMessage,
      'summary': instance.summary?.toJson(),
      'transactions': instance.transactions.map((e) => e.toJson()).toList(),
      'settlements': instance.settlements.map((e) => e.toJson()).toList(),
      'bankAccounts': instance.bankAccounts.map((e) => e.toJson()).toList(),
    };
