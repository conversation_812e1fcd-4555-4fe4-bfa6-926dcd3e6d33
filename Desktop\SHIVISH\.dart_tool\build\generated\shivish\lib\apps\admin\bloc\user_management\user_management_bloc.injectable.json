[{"type": {"import": "package:shivish/apps/admin/bloc/user_management/user_management_bloc.dart", "name": "UserManagementBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/user_management/user_management_bloc.dart", "name": "UserManagementBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": null, "name": "InvalidType", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_userService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]