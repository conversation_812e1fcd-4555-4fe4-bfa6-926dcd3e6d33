// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ChatbotConfigModelImpl _$$ChatbotConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$ChatbotConfigModelImpl(
  enabled: json['enabled'] as bool,
  modelName: json['modelName'] as String,
  language: json['language'] as String,
  temperature: (json['temperature'] as num).toDouble(),
  maxTokens: (json['maxTokens'] as num).toInt(),
  supportedLanguages: (json['supportedLanguages'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  responseTemplates: Map<String, String>.from(json['responseTemplates'] as Map),
  modelParameters: json['modelParameters'] as Map<String, dynamic>,
);

Map<String, dynamic> _$$ChatbotConfigModelImplToJson(
  _$ChatbotConfigModelImpl instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'modelName': instance.modelName,
  'language': instance.language,
  'temperature': instance.temperature,
  'maxTokens': instance.maxTokens,
  'supportedLanguages': instance.supportedLanguages,
  'responseTemplates': instance.responseTemplates,
  'modelParameters': instance.modelParameters,
};
