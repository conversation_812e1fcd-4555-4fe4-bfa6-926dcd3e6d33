// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ProductVariant _$ProductVariantFromJson(Map<String, dynamic> json) {
  return _ProductVariant.fromJson(json);
}

/// @nodoc
mixin _$ProductVariant {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get sku => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  double? get salePrice => throw _privateConstructorUsedError;
  double? get compareAtPrice => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  String? get barcode => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  Map<String, dynamic>? get options => throw _privateConstructorUsedError;
  bool get isDefault => throw _privateConstructorUsedError;
  bool get isAvailable => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this ProductVariant to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductVariant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductVariantCopyWith<ProductVariant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductVariantCopyWith<$Res> {
  factory $ProductVariantCopyWith(
    ProductVariant value,
    $Res Function(ProductVariant) then,
  ) = _$ProductVariantCopyWithImpl<$Res, ProductVariant>;
  @useResult
  $Res call({
    String id,
    String name,
    String sku,
    double price,
    double? salePrice,
    double? compareAtPrice,
    int quantity,
    String? barcode,
    String? imageUrl,
    Map<String, dynamic>? options,
    bool isDefault,
    bool isAvailable,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$ProductVariantCopyWithImpl<$Res, $Val extends ProductVariant>
    implements $ProductVariantCopyWith<$Res> {
  _$ProductVariantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductVariant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? sku = null,
    Object? price = null,
    Object? salePrice = freezed,
    Object? compareAtPrice = freezed,
    Object? quantity = null,
    Object? barcode = freezed,
    Object? imageUrl = freezed,
    Object? options = freezed,
    Object? isDefault = null,
    Object? isAvailable = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            sku: null == sku
                ? _value.sku
                : sku // ignore: cast_nullable_to_non_nullable
                      as String,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            salePrice: freezed == salePrice
                ? _value.salePrice
                : salePrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            compareAtPrice: freezed == compareAtPrice
                ? _value.compareAtPrice
                : compareAtPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            quantity: null == quantity
                ? _value.quantity
                : quantity // ignore: cast_nullable_to_non_nullable
                      as int,
            barcode: freezed == barcode
                ? _value.barcode
                : barcode // ignore: cast_nullable_to_non_nullable
                      as String?,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            options: freezed == options
                ? _value.options
                : options // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            isDefault: null == isDefault
                ? _value.isDefault
                : isDefault // ignore: cast_nullable_to_non_nullable
                      as bool,
            isAvailable: null == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProductVariantImplCopyWith<$Res>
    implements $ProductVariantCopyWith<$Res> {
  factory _$$ProductVariantImplCopyWith(
    _$ProductVariantImpl value,
    $Res Function(_$ProductVariantImpl) then,
  ) = __$$ProductVariantImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String sku,
    double price,
    double? salePrice,
    double? compareAtPrice,
    int quantity,
    String? barcode,
    String? imageUrl,
    Map<String, dynamic>? options,
    bool isDefault,
    bool isAvailable,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$ProductVariantImplCopyWithImpl<$Res>
    extends _$ProductVariantCopyWithImpl<$Res, _$ProductVariantImpl>
    implements _$$ProductVariantImplCopyWith<$Res> {
  __$$ProductVariantImplCopyWithImpl(
    _$ProductVariantImpl _value,
    $Res Function(_$ProductVariantImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProductVariant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? sku = null,
    Object? price = null,
    Object? salePrice = freezed,
    Object? compareAtPrice = freezed,
    Object? quantity = null,
    Object? barcode = freezed,
    Object? imageUrl = freezed,
    Object? options = freezed,
    Object? isDefault = null,
    Object? isAvailable = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$ProductVariantImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        sku: null == sku
            ? _value.sku
            : sku // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        salePrice: freezed == salePrice
            ? _value.salePrice
            : salePrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        compareAtPrice: freezed == compareAtPrice
            ? _value.compareAtPrice
            : compareAtPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        quantity: null == quantity
            ? _value.quantity
            : quantity // ignore: cast_nullable_to_non_nullable
                  as int,
        barcode: freezed == barcode
            ? _value.barcode
            : barcode // ignore: cast_nullable_to_non_nullable
                  as String?,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        options: freezed == options
            ? _value._options
            : options // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        isDefault: null == isDefault
            ? _value.isDefault
            : isDefault // ignore: cast_nullable_to_non_nullable
                  as bool,
        isAvailable: null == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductVariantImpl implements _ProductVariant {
  const _$ProductVariantImpl({
    required this.id,
    required this.name,
    required this.sku,
    required this.price,
    this.salePrice,
    this.compareAtPrice,
    required this.quantity,
    this.barcode,
    this.imageUrl,
    final Map<String, dynamic>? options,
    this.isDefault = false,
    this.isAvailable = true,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  }) : _options = options;

  factory _$ProductVariantImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductVariantImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String sku;
  @override
  final double price;
  @override
  final double? salePrice;
  @override
  final double? compareAtPrice;
  @override
  final int quantity;
  @override
  final String? barcode;
  @override
  final String? imageUrl;
  final Map<String, dynamic>? _options;
  @override
  Map<String, dynamic>? get options {
    final value = _options;
    if (value == null) return null;
    if (_options is EqualUnmodifiableMapView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  @JsonKey()
  final bool isDefault;
  @override
  @JsonKey()
  final bool isAvailable;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'ProductVariant(id: $id, name: $name, sku: $sku, price: $price, salePrice: $salePrice, compareAtPrice: $compareAtPrice, quantity: $quantity, barcode: $barcode, imageUrl: $imageUrl, options: $options, isDefault: $isDefault, isAvailable: $isAvailable, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductVariantImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.sku, sku) || other.sku == sku) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.salePrice, salePrice) ||
                other.salePrice == salePrice) &&
            (identical(other.compareAtPrice, compareAtPrice) ||
                other.compareAtPrice == compareAtPrice) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.barcode, barcode) || other.barcode == barcode) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(other._options, _options) &&
            (identical(other.isDefault, isDefault) ||
                other.isDefault == isDefault) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    sku,
    price,
    salePrice,
    compareAtPrice,
    quantity,
    barcode,
    imageUrl,
    const DeepCollectionEquality().hash(_options),
    isDefault,
    isAvailable,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of ProductVariant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductVariantImplCopyWith<_$ProductVariantImpl> get copyWith =>
      __$$ProductVariantImplCopyWithImpl<_$ProductVariantImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductVariantImplToJson(this);
  }
}

abstract class _ProductVariant implements ProductVariant {
  const factory _ProductVariant({
    required final String id,
    required final String name,
    required final String sku,
    required final double price,
    final double? salePrice,
    final double? compareAtPrice,
    required final int quantity,
    final String? barcode,
    final String? imageUrl,
    final Map<String, dynamic>? options,
    final bool isDefault,
    final bool isAvailable,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$ProductVariantImpl;

  factory _ProductVariant.fromJson(Map<String, dynamic> json) =
      _$ProductVariantImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get sku;
  @override
  double get price;
  @override
  double? get salePrice;
  @override
  double? get compareAtPrice;
  @override
  int get quantity;
  @override
  String? get barcode;
  @override
  String? get imageUrl;
  @override
  Map<String, dynamic>? get options;
  @override
  bool get isDefault;
  @override
  bool get isAvailable;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of ProductVariant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductVariantImplCopyWith<_$ProductVariantImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductCategory _$ProductCategoryFromJson(Map<String, dynamic> json) {
  return _ProductCategory.fromJson(json);
}

/// @nodoc
mixin _$ProductCategory {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get parentId => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this ProductCategory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductCategoryCopyWith<ProductCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductCategoryCopyWith<$Res> {
  factory $ProductCategoryCopyWith(
    ProductCategory value,
    $Res Function(ProductCategory) then,
  ) = _$ProductCategoryCopyWithImpl<$Res, ProductCategory>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    String? imageUrl,
    String? parentId,
    bool isActive,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$ProductCategoryCopyWithImpl<$Res, $Val extends ProductCategory>
    implements $ProductCategoryCopyWith<$Res> {
  _$ProductCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? imageUrl = freezed,
    Object? parentId = freezed,
    Object? isActive = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            parentId: freezed == parentId
                ? _value.parentId
                : parentId // ignore: cast_nullable_to_non_nullable
                      as String?,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProductCategoryImplCopyWith<$Res>
    implements $ProductCategoryCopyWith<$Res> {
  factory _$$ProductCategoryImplCopyWith(
    _$ProductCategoryImpl value,
    $Res Function(_$ProductCategoryImpl) then,
  ) = __$$ProductCategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    String? imageUrl,
    String? parentId,
    bool isActive,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$ProductCategoryImplCopyWithImpl<$Res>
    extends _$ProductCategoryCopyWithImpl<$Res, _$ProductCategoryImpl>
    implements _$$ProductCategoryImplCopyWith<$Res> {
  __$$ProductCategoryImplCopyWithImpl(
    _$ProductCategoryImpl _value,
    $Res Function(_$ProductCategoryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? imageUrl = freezed,
    Object? parentId = freezed,
    Object? isActive = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$ProductCategoryImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        parentId: freezed == parentId
            ? _value.parentId
            : parentId // ignore: cast_nullable_to_non_nullable
                  as String?,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductCategoryImpl implements _ProductCategory {
  const _$ProductCategoryImpl({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    this.parentId,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$ProductCategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductCategoryImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? imageUrl;
  @override
  final String? parentId;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'ProductCategory(id: $id, name: $name, description: $description, imageUrl: $imageUrl, parentId: $parentId, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductCategoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    imageUrl,
    parentId,
    isActive,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductCategoryImplCopyWith<_$ProductCategoryImpl> get copyWith =>
      __$$ProductCategoryImplCopyWithImpl<_$ProductCategoryImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductCategoryImplToJson(this);
  }
}

abstract class _ProductCategory implements ProductCategory {
  const factory _ProductCategory({
    required final String id,
    required final String name,
    final String? description,
    final String? imageUrl,
    final String? parentId,
    final bool isActive,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$ProductCategoryImpl;

  factory _ProductCategory.fromJson(Map<String, dynamic> json) =
      _$ProductCategoryImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  String? get imageUrl;
  @override
  String? get parentId;
  @override
  bool get isActive;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductCategoryImplCopyWith<_$ProductCategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) {
  return _ProductModel.fromJson(json);
}

/// @nodoc
mixin _$ProductModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  double? get originalPrice => throw _privateConstructorUsedError;
  double? get discountPercentage => throw _privateConstructorUsedError;
  double? get salePrice => throw _privateConstructorUsedError;
  double? get compareAtPrice => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  String get categoryId => throw _privateConstructorUsedError;
  String get sellerId => throw _privateConstructorUsedError;
  List<String> get images => throw _privateConstructorUsedError;
  bool get isFeatured => throw _privateConstructorUsedError;
  bool get isApproved => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isSuspended => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  String? get brand => throw _privateConstructorUsedError;
  String? get unit => throw _privateConstructorUsedError;
  double? get weight => throw _privateConstructorUsedError;
  double? get rating => throw _privateConstructorUsedError;
  int? get reviewCount => throw _privateConstructorUsedError;
  List<String> get highlights => throw _privateConstructorUsedError;
  Map<String, dynamic>? get specifications =>
      throw _privateConstructorUsedError;
  List<ProductVariant>? get variants => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  ProductType get productType => throw _privateConstructorUsedError;
  ProductStatus get productStatus => throw _privateConstructorUsedError;
  String? get verificationNotes =>
      throw _privateConstructorUsedError; // Seller specific fields
  String? get sellerName => throw _privateConstructorUsedError;
  String? get sellerEmail => throw _privateConstructorUsedError;
  String? get sellerPhone => throw _privateConstructorUsedError;
  String? get sellerAddress => throw _privateConstructorUsedError;
  double? get sellerRating => throw _privateConstructorUsedError;
  int? get sellerTotalSales => throw _privateConstructorUsedError;
  int? get sellerTotalProducts => throw _privateConstructorUsedError;
  bool? get sellerIsVerified => throw _privateConstructorUsedError;
  bool? get sellerIsActive => throw _privateConstructorUsedError;
  DateTime? get sellerCreatedAt => throw _privateConstructorUsedError;
  DateTime? get sellerUpdatedAt => throw _privateConstructorUsedError;

  /// Serializes this ProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductModelCopyWith<ProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductModelCopyWith<$Res> {
  factory $ProductModelCopyWith(
    ProductModel value,
    $Res Function(ProductModel) then,
  ) = _$ProductModelCopyWithImpl<$Res, ProductModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    double price,
    double? originalPrice,
    double? discountPercentage,
    double? salePrice,
    double? compareAtPrice,
    int quantity,
    String categoryId,
    String sellerId,
    List<String> images,
    bool isFeatured,
    bool isApproved,
    bool isDeleted,
    bool isActive,
    bool isSuspended,
    List<String> tags,
    String? brand,
    String? unit,
    double? weight,
    double? rating,
    int? reviewCount,
    List<String> highlights,
    Map<String, dynamic>? specifications,
    List<ProductVariant>? variants,
    DateTime? createdAt,
    DateTime? updatedAt,
    ProductType productType,
    ProductStatus productStatus,
    String? verificationNotes,
    String? sellerName,
    String? sellerEmail,
    String? sellerPhone,
    String? sellerAddress,
    double? sellerRating,
    int? sellerTotalSales,
    int? sellerTotalProducts,
    bool? sellerIsVerified,
    bool? sellerIsActive,
    DateTime? sellerCreatedAt,
    DateTime? sellerUpdatedAt,
  });
}

/// @nodoc
class _$ProductModelCopyWithImpl<$Res, $Val extends ProductModel>
    implements $ProductModelCopyWith<$Res> {
  _$ProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? price = null,
    Object? originalPrice = freezed,
    Object? discountPercentage = freezed,
    Object? salePrice = freezed,
    Object? compareAtPrice = freezed,
    Object? quantity = null,
    Object? categoryId = null,
    Object? sellerId = null,
    Object? images = null,
    Object? isFeatured = null,
    Object? isApproved = null,
    Object? isDeleted = null,
    Object? isActive = null,
    Object? isSuspended = null,
    Object? tags = null,
    Object? brand = freezed,
    Object? unit = freezed,
    Object? weight = freezed,
    Object? rating = freezed,
    Object? reviewCount = freezed,
    Object? highlights = null,
    Object? specifications = freezed,
    Object? variants = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? productType = null,
    Object? productStatus = null,
    Object? verificationNotes = freezed,
    Object? sellerName = freezed,
    Object? sellerEmail = freezed,
    Object? sellerPhone = freezed,
    Object? sellerAddress = freezed,
    Object? sellerRating = freezed,
    Object? sellerTotalSales = freezed,
    Object? sellerTotalProducts = freezed,
    Object? sellerIsVerified = freezed,
    Object? sellerIsActive = freezed,
    Object? sellerCreatedAt = freezed,
    Object? sellerUpdatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            originalPrice: freezed == originalPrice
                ? _value.originalPrice
                : originalPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            discountPercentage: freezed == discountPercentage
                ? _value.discountPercentage
                : discountPercentage // ignore: cast_nullable_to_non_nullable
                      as double?,
            salePrice: freezed == salePrice
                ? _value.salePrice
                : salePrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            compareAtPrice: freezed == compareAtPrice
                ? _value.compareAtPrice
                : compareAtPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            quantity: null == quantity
                ? _value.quantity
                : quantity // ignore: cast_nullable_to_non_nullable
                      as int,
            categoryId: null == categoryId
                ? _value.categoryId
                : categoryId // ignore: cast_nullable_to_non_nullable
                      as String,
            sellerId: null == sellerId
                ? _value.sellerId
                : sellerId // ignore: cast_nullable_to_non_nullable
                      as String,
            images: null == images
                ? _value.images
                : images // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isFeatured: null == isFeatured
                ? _value.isFeatured
                : isFeatured // ignore: cast_nullable_to_non_nullable
                      as bool,
            isApproved: null == isApproved
                ? _value.isApproved
                : isApproved // ignore: cast_nullable_to_non_nullable
                      as bool,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            isSuspended: null == isSuspended
                ? _value.isSuspended
                : isSuspended // ignore: cast_nullable_to_non_nullable
                      as bool,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            brand: freezed == brand
                ? _value.brand
                : brand // ignore: cast_nullable_to_non_nullable
                      as String?,
            unit: freezed == unit
                ? _value.unit
                : unit // ignore: cast_nullable_to_non_nullable
                      as String?,
            weight: freezed == weight
                ? _value.weight
                : weight // ignore: cast_nullable_to_non_nullable
                      as double?,
            rating: freezed == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double?,
            reviewCount: freezed == reviewCount
                ? _value.reviewCount
                : reviewCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            highlights: null == highlights
                ? _value.highlights
                : highlights // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            specifications: freezed == specifications
                ? _value.specifications
                : specifications // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            variants: freezed == variants
                ? _value.variants
                : variants // ignore: cast_nullable_to_non_nullable
                      as List<ProductVariant>?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            productType: null == productType
                ? _value.productType
                : productType // ignore: cast_nullable_to_non_nullable
                      as ProductType,
            productStatus: null == productStatus
                ? _value.productStatus
                : productStatus // ignore: cast_nullable_to_non_nullable
                      as ProductStatus,
            verificationNotes: freezed == verificationNotes
                ? _value.verificationNotes
                : verificationNotes // ignore: cast_nullable_to_non_nullable
                      as String?,
            sellerName: freezed == sellerName
                ? _value.sellerName
                : sellerName // ignore: cast_nullable_to_non_nullable
                      as String?,
            sellerEmail: freezed == sellerEmail
                ? _value.sellerEmail
                : sellerEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            sellerPhone: freezed == sellerPhone
                ? _value.sellerPhone
                : sellerPhone // ignore: cast_nullable_to_non_nullable
                      as String?,
            sellerAddress: freezed == sellerAddress
                ? _value.sellerAddress
                : sellerAddress // ignore: cast_nullable_to_non_nullable
                      as String?,
            sellerRating: freezed == sellerRating
                ? _value.sellerRating
                : sellerRating // ignore: cast_nullable_to_non_nullable
                      as double?,
            sellerTotalSales: freezed == sellerTotalSales
                ? _value.sellerTotalSales
                : sellerTotalSales // ignore: cast_nullable_to_non_nullable
                      as int?,
            sellerTotalProducts: freezed == sellerTotalProducts
                ? _value.sellerTotalProducts
                : sellerTotalProducts // ignore: cast_nullable_to_non_nullable
                      as int?,
            sellerIsVerified: freezed == sellerIsVerified
                ? _value.sellerIsVerified
                : sellerIsVerified // ignore: cast_nullable_to_non_nullable
                      as bool?,
            sellerIsActive: freezed == sellerIsActive
                ? _value.sellerIsActive
                : sellerIsActive // ignore: cast_nullable_to_non_nullable
                      as bool?,
            sellerCreatedAt: freezed == sellerCreatedAt
                ? _value.sellerCreatedAt
                : sellerCreatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            sellerUpdatedAt: freezed == sellerUpdatedAt
                ? _value.sellerUpdatedAt
                : sellerUpdatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProductModelImplCopyWith<$Res>
    implements $ProductModelCopyWith<$Res> {
  factory _$$ProductModelImplCopyWith(
    _$ProductModelImpl value,
    $Res Function(_$ProductModelImpl) then,
  ) = __$$ProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    double price,
    double? originalPrice,
    double? discountPercentage,
    double? salePrice,
    double? compareAtPrice,
    int quantity,
    String categoryId,
    String sellerId,
    List<String> images,
    bool isFeatured,
    bool isApproved,
    bool isDeleted,
    bool isActive,
    bool isSuspended,
    List<String> tags,
    String? brand,
    String? unit,
    double? weight,
    double? rating,
    int? reviewCount,
    List<String> highlights,
    Map<String, dynamic>? specifications,
    List<ProductVariant>? variants,
    DateTime? createdAt,
    DateTime? updatedAt,
    ProductType productType,
    ProductStatus productStatus,
    String? verificationNotes,
    String? sellerName,
    String? sellerEmail,
    String? sellerPhone,
    String? sellerAddress,
    double? sellerRating,
    int? sellerTotalSales,
    int? sellerTotalProducts,
    bool? sellerIsVerified,
    bool? sellerIsActive,
    DateTime? sellerCreatedAt,
    DateTime? sellerUpdatedAt,
  });
}

/// @nodoc
class __$$ProductModelImplCopyWithImpl<$Res>
    extends _$ProductModelCopyWithImpl<$Res, _$ProductModelImpl>
    implements _$$ProductModelImplCopyWith<$Res> {
  __$$ProductModelImplCopyWithImpl(
    _$ProductModelImpl _value,
    $Res Function(_$ProductModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? price = null,
    Object? originalPrice = freezed,
    Object? discountPercentage = freezed,
    Object? salePrice = freezed,
    Object? compareAtPrice = freezed,
    Object? quantity = null,
    Object? categoryId = null,
    Object? sellerId = null,
    Object? images = null,
    Object? isFeatured = null,
    Object? isApproved = null,
    Object? isDeleted = null,
    Object? isActive = null,
    Object? isSuspended = null,
    Object? tags = null,
    Object? brand = freezed,
    Object? unit = freezed,
    Object? weight = freezed,
    Object? rating = freezed,
    Object? reviewCount = freezed,
    Object? highlights = null,
    Object? specifications = freezed,
    Object? variants = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? productType = null,
    Object? productStatus = null,
    Object? verificationNotes = freezed,
    Object? sellerName = freezed,
    Object? sellerEmail = freezed,
    Object? sellerPhone = freezed,
    Object? sellerAddress = freezed,
    Object? sellerRating = freezed,
    Object? sellerTotalSales = freezed,
    Object? sellerTotalProducts = freezed,
    Object? sellerIsVerified = freezed,
    Object? sellerIsActive = freezed,
    Object? sellerCreatedAt = freezed,
    Object? sellerUpdatedAt = freezed,
  }) {
    return _then(
      _$ProductModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        originalPrice: freezed == originalPrice
            ? _value.originalPrice
            : originalPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        discountPercentage: freezed == discountPercentage
            ? _value.discountPercentage
            : discountPercentage // ignore: cast_nullable_to_non_nullable
                  as double?,
        salePrice: freezed == salePrice
            ? _value.salePrice
            : salePrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        compareAtPrice: freezed == compareAtPrice
            ? _value.compareAtPrice
            : compareAtPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        quantity: null == quantity
            ? _value.quantity
            : quantity // ignore: cast_nullable_to_non_nullable
                  as int,
        categoryId: null == categoryId
            ? _value.categoryId
            : categoryId // ignore: cast_nullable_to_non_nullable
                  as String,
        sellerId: null == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String,
        images: null == images
            ? _value._images
            : images // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isFeatured: null == isFeatured
            ? _value.isFeatured
            : isFeatured // ignore: cast_nullable_to_non_nullable
                  as bool,
        isApproved: null == isApproved
            ? _value.isApproved
            : isApproved // ignore: cast_nullable_to_non_nullable
                  as bool,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        isSuspended: null == isSuspended
            ? _value.isSuspended
            : isSuspended // ignore: cast_nullable_to_non_nullable
                  as bool,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        brand: freezed == brand
            ? _value.brand
            : brand // ignore: cast_nullable_to_non_nullable
                  as String?,
        unit: freezed == unit
            ? _value.unit
            : unit // ignore: cast_nullable_to_non_nullable
                  as String?,
        weight: freezed == weight
            ? _value.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as double?,
        rating: freezed == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double?,
        reviewCount: freezed == reviewCount
            ? _value.reviewCount
            : reviewCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        highlights: null == highlights
            ? _value._highlights
            : highlights // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        specifications: freezed == specifications
            ? _value._specifications
            : specifications // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        variants: freezed == variants
            ? _value._variants
            : variants // ignore: cast_nullable_to_non_nullable
                  as List<ProductVariant>?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        productType: null == productType
            ? _value.productType
            : productType // ignore: cast_nullable_to_non_nullable
                  as ProductType,
        productStatus: null == productStatus
            ? _value.productStatus
            : productStatus // ignore: cast_nullable_to_non_nullable
                  as ProductStatus,
        verificationNotes: freezed == verificationNotes
            ? _value.verificationNotes
            : verificationNotes // ignore: cast_nullable_to_non_nullable
                  as String?,
        sellerName: freezed == sellerName
            ? _value.sellerName
            : sellerName // ignore: cast_nullable_to_non_nullable
                  as String?,
        sellerEmail: freezed == sellerEmail
            ? _value.sellerEmail
            : sellerEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        sellerPhone: freezed == sellerPhone
            ? _value.sellerPhone
            : sellerPhone // ignore: cast_nullable_to_non_nullable
                  as String?,
        sellerAddress: freezed == sellerAddress
            ? _value.sellerAddress
            : sellerAddress // ignore: cast_nullable_to_non_nullable
                  as String?,
        sellerRating: freezed == sellerRating
            ? _value.sellerRating
            : sellerRating // ignore: cast_nullable_to_non_nullable
                  as double?,
        sellerTotalSales: freezed == sellerTotalSales
            ? _value.sellerTotalSales
            : sellerTotalSales // ignore: cast_nullable_to_non_nullable
                  as int?,
        sellerTotalProducts: freezed == sellerTotalProducts
            ? _value.sellerTotalProducts
            : sellerTotalProducts // ignore: cast_nullable_to_non_nullable
                  as int?,
        sellerIsVerified: freezed == sellerIsVerified
            ? _value.sellerIsVerified
            : sellerIsVerified // ignore: cast_nullable_to_non_nullable
                  as bool?,
        sellerIsActive: freezed == sellerIsActive
            ? _value.sellerIsActive
            : sellerIsActive // ignore: cast_nullable_to_non_nullable
                  as bool?,
        sellerCreatedAt: freezed == sellerCreatedAt
            ? _value.sellerCreatedAt
            : sellerCreatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        sellerUpdatedAt: freezed == sellerUpdatedAt
            ? _value.sellerUpdatedAt
            : sellerUpdatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductModelImpl implements _ProductModel {
  const _$ProductModelImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    this.discountPercentage,
    this.salePrice,
    this.compareAtPrice,
    required this.quantity,
    required this.categoryId,
    required this.sellerId,
    required final List<String> images,
    this.isFeatured = false,
    this.isApproved = false,
    this.isDeleted = false,
    this.isActive = true,
    this.isSuspended = false,
    final List<String> tags = const [],
    this.brand,
    this.unit,
    this.weight,
    this.rating,
    this.reviewCount,
    final List<String> highlights = const [],
    final Map<String, dynamic>? specifications,
    final List<ProductVariant>? variants,
    this.createdAt,
    this.updatedAt,
    required this.productType,
    required this.productStatus,
    this.verificationNotes,
    this.sellerName,
    this.sellerEmail,
    this.sellerPhone,
    this.sellerAddress,
    this.sellerRating,
    this.sellerTotalSales,
    this.sellerTotalProducts,
    this.sellerIsVerified,
    this.sellerIsActive,
    this.sellerCreatedAt,
    this.sellerUpdatedAt,
  }) : _images = images,
       _tags = tags,
       _highlights = highlights,
       _specifications = specifications,
       _variants = variants;

  factory _$ProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final double price;
  @override
  final double? originalPrice;
  @override
  final double? discountPercentage;
  @override
  final double? salePrice;
  @override
  final double? compareAtPrice;
  @override
  final int quantity;
  @override
  final String categoryId;
  @override
  final String sellerId;
  final List<String> _images;
  @override
  List<String> get images {
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_images);
  }

  @override
  @JsonKey()
  final bool isFeatured;
  @override
  @JsonKey()
  final bool isApproved;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final bool isSuspended;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final String? brand;
  @override
  final String? unit;
  @override
  final double? weight;
  @override
  final double? rating;
  @override
  final int? reviewCount;
  final List<String> _highlights;
  @override
  @JsonKey()
  List<String> get highlights {
    if (_highlights is EqualUnmodifiableListView) return _highlights;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_highlights);
  }

  final Map<String, dynamic>? _specifications;
  @override
  Map<String, dynamic>? get specifications {
    final value = _specifications;
    if (value == null) return null;
    if (_specifications is EqualUnmodifiableMapView) return _specifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<ProductVariant>? _variants;
  @override
  List<ProductVariant>? get variants {
    final value = _variants;
    if (value == null) return null;
    if (_variants is EqualUnmodifiableListView) return _variants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final ProductType productType;
  @override
  final ProductStatus productStatus;
  @override
  final String? verificationNotes;
  // Seller specific fields
  @override
  final String? sellerName;
  @override
  final String? sellerEmail;
  @override
  final String? sellerPhone;
  @override
  final String? sellerAddress;
  @override
  final double? sellerRating;
  @override
  final int? sellerTotalSales;
  @override
  final int? sellerTotalProducts;
  @override
  final bool? sellerIsVerified;
  @override
  final bool? sellerIsActive;
  @override
  final DateTime? sellerCreatedAt;
  @override
  final DateTime? sellerUpdatedAt;

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, description: $description, price: $price, originalPrice: $originalPrice, discountPercentage: $discountPercentage, salePrice: $salePrice, compareAtPrice: $compareAtPrice, quantity: $quantity, categoryId: $categoryId, sellerId: $sellerId, images: $images, isFeatured: $isFeatured, isApproved: $isApproved, isDeleted: $isDeleted, isActive: $isActive, isSuspended: $isSuspended, tags: $tags, brand: $brand, unit: $unit, weight: $weight, rating: $rating, reviewCount: $reviewCount, highlights: $highlights, specifications: $specifications, variants: $variants, createdAt: $createdAt, updatedAt: $updatedAt, productType: $productType, productStatus: $productStatus, verificationNotes: $verificationNotes, sellerName: $sellerName, sellerEmail: $sellerEmail, sellerPhone: $sellerPhone, sellerAddress: $sellerAddress, sellerRating: $sellerRating, sellerTotalSales: $sellerTotalSales, sellerTotalProducts: $sellerTotalProducts, sellerIsVerified: $sellerIsVerified, sellerIsActive: $sellerIsActive, sellerCreatedAt: $sellerCreatedAt, sellerUpdatedAt: $sellerUpdatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.originalPrice, originalPrice) ||
                other.originalPrice == originalPrice) &&
            (identical(other.discountPercentage, discountPercentage) ||
                other.discountPercentage == discountPercentage) &&
            (identical(other.salePrice, salePrice) ||
                other.salePrice == salePrice) &&
            (identical(other.compareAtPrice, compareAtPrice) ||
                other.compareAtPrice == compareAtPrice) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            const DeepCollectionEquality().equals(other._images, _images) &&
            (identical(other.isFeatured, isFeatured) ||
                other.isFeatured == isFeatured) &&
            (identical(other.isApproved, isApproved) ||
                other.isApproved == isApproved) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isSuspended, isSuspended) ||
                other.isSuspended == isSuspended) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.brand, brand) || other.brand == brand) &&
            (identical(other.unit, unit) || other.unit == unit) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.reviewCount, reviewCount) ||
                other.reviewCount == reviewCount) &&
            const DeepCollectionEquality().equals(
              other._highlights,
              _highlights,
            ) &&
            const DeepCollectionEquality().equals(
              other._specifications,
              _specifications,
            ) &&
            const DeepCollectionEquality().equals(other._variants, _variants) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.productType, productType) ||
                other.productType == productType) &&
            (identical(other.productStatus, productStatus) ||
                other.productStatus == productStatus) &&
            (identical(other.verificationNotes, verificationNotes) ||
                other.verificationNotes == verificationNotes) &&
            (identical(other.sellerName, sellerName) ||
                other.sellerName == sellerName) &&
            (identical(other.sellerEmail, sellerEmail) ||
                other.sellerEmail == sellerEmail) &&
            (identical(other.sellerPhone, sellerPhone) ||
                other.sellerPhone == sellerPhone) &&
            (identical(other.sellerAddress, sellerAddress) ||
                other.sellerAddress == sellerAddress) &&
            (identical(other.sellerRating, sellerRating) ||
                other.sellerRating == sellerRating) &&
            (identical(other.sellerTotalSales, sellerTotalSales) ||
                other.sellerTotalSales == sellerTotalSales) &&
            (identical(other.sellerTotalProducts, sellerTotalProducts) ||
                other.sellerTotalProducts == sellerTotalProducts) &&
            (identical(other.sellerIsVerified, sellerIsVerified) ||
                other.sellerIsVerified == sellerIsVerified) &&
            (identical(other.sellerIsActive, sellerIsActive) ||
                other.sellerIsActive == sellerIsActive) &&
            (identical(other.sellerCreatedAt, sellerCreatedAt) ||
                other.sellerCreatedAt == sellerCreatedAt) &&
            (identical(other.sellerUpdatedAt, sellerUpdatedAt) ||
                other.sellerUpdatedAt == sellerUpdatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    description,
    price,
    originalPrice,
    discountPercentage,
    salePrice,
    compareAtPrice,
    quantity,
    categoryId,
    sellerId,
    const DeepCollectionEquality().hash(_images),
    isFeatured,
    isApproved,
    isDeleted,
    isActive,
    isSuspended,
    const DeepCollectionEquality().hash(_tags),
    brand,
    unit,
    weight,
    rating,
    reviewCount,
    const DeepCollectionEquality().hash(_highlights),
    const DeepCollectionEquality().hash(_specifications),
    const DeepCollectionEquality().hash(_variants),
    createdAt,
    updatedAt,
    productType,
    productStatus,
    verificationNotes,
    sellerName,
    sellerEmail,
    sellerPhone,
    sellerAddress,
    sellerRating,
    sellerTotalSales,
    sellerTotalProducts,
    sellerIsVerified,
    sellerIsActive,
    sellerCreatedAt,
    sellerUpdatedAt,
  ]);

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductModelImplCopyWith<_$ProductModelImpl> get copyWith =>
      __$$ProductModelImplCopyWithImpl<_$ProductModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductModelImplToJson(this);
  }
}

abstract class _ProductModel implements ProductModel {
  const factory _ProductModel({
    required final String id,
    required final String name,
    required final String description,
    required final double price,
    final double? originalPrice,
    final double? discountPercentage,
    final double? salePrice,
    final double? compareAtPrice,
    required final int quantity,
    required final String categoryId,
    required final String sellerId,
    required final List<String> images,
    final bool isFeatured,
    final bool isApproved,
    final bool isDeleted,
    final bool isActive,
    final bool isSuspended,
    final List<String> tags,
    final String? brand,
    final String? unit,
    final double? weight,
    final double? rating,
    final int? reviewCount,
    final List<String> highlights,
    final Map<String, dynamic>? specifications,
    final List<ProductVariant>? variants,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    required final ProductType productType,
    required final ProductStatus productStatus,
    final String? verificationNotes,
    final String? sellerName,
    final String? sellerEmail,
    final String? sellerPhone,
    final String? sellerAddress,
    final double? sellerRating,
    final int? sellerTotalSales,
    final int? sellerTotalProducts,
    final bool? sellerIsVerified,
    final bool? sellerIsActive,
    final DateTime? sellerCreatedAt,
    final DateTime? sellerUpdatedAt,
  }) = _$ProductModelImpl;

  factory _ProductModel.fromJson(Map<String, dynamic> json) =
      _$ProductModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  double get price;
  @override
  double? get originalPrice;
  @override
  double? get discountPercentage;
  @override
  double? get salePrice;
  @override
  double? get compareAtPrice;
  @override
  int get quantity;
  @override
  String get categoryId;
  @override
  String get sellerId;
  @override
  List<String> get images;
  @override
  bool get isFeatured;
  @override
  bool get isApproved;
  @override
  bool get isDeleted;
  @override
  bool get isActive;
  @override
  bool get isSuspended;
  @override
  List<String> get tags;
  @override
  String? get brand;
  @override
  String? get unit;
  @override
  double? get weight;
  @override
  double? get rating;
  @override
  int? get reviewCount;
  @override
  List<String> get highlights;
  @override
  Map<String, dynamic>? get specifications;
  @override
  List<ProductVariant>? get variants;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  ProductType get productType;
  @override
  ProductStatus get productStatus;
  @override
  String? get verificationNotes; // Seller specific fields
  @override
  String? get sellerName;
  @override
  String? get sellerEmail;
  @override
  String? get sellerPhone;
  @override
  String? get sellerAddress;
  @override
  double? get sellerRating;
  @override
  int? get sellerTotalSales;
  @override
  int? get sellerTotalProducts;
  @override
  bool? get sellerIsVerified;
  @override
  bool? get sellerIsActive;
  @override
  DateTime? get sellerCreatedAt;
  @override
  DateTime? get sellerUpdatedAt;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductModelImplCopyWith<_$ProductModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
