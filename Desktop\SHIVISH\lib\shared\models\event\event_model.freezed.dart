// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'event_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

EventProduct _$EventProductFromJson(Map<String, dynamic> json) {
  return _EventProduct.fromJson(json);
}

/// @nodoc
mixin _$EventProduct {
  String get productId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;

  /// Serializes this EventProduct to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EventProduct
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EventProductCopyWith<EventProduct> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EventProductCopyWith<$Res> {
  factory $EventProductCopyWith(
    EventProduct value,
    $Res Function(EventProduct) then,
  ) = _$EventProductCopyWithImpl<$Res, EventProduct>;
  @useResult
  $Res call({
    String productId,
    String name,
    double price,
    int quantity,
    String? imageUrl,
    String? description,
  });
}

/// @nodoc
class _$EventProductCopyWithImpl<$Res, $Val extends EventProduct>
    implements $EventProductCopyWith<$Res> {
  _$EventProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EventProduct
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? name = null,
    Object? price = null,
    Object? quantity = null,
    Object? imageUrl = freezed,
    Object? description = freezed,
  }) {
    return _then(
      _value.copyWith(
            productId: null == productId
                ? _value.productId
                : productId // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            quantity: null == quantity
                ? _value.quantity
                : quantity // ignore: cast_nullable_to_non_nullable
                      as int,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EventProductImplCopyWith<$Res>
    implements $EventProductCopyWith<$Res> {
  factory _$$EventProductImplCopyWith(
    _$EventProductImpl value,
    $Res Function(_$EventProductImpl) then,
  ) = __$$EventProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String productId,
    String name,
    double price,
    int quantity,
    String? imageUrl,
    String? description,
  });
}

/// @nodoc
class __$$EventProductImplCopyWithImpl<$Res>
    extends _$EventProductCopyWithImpl<$Res, _$EventProductImpl>
    implements _$$EventProductImplCopyWith<$Res> {
  __$$EventProductImplCopyWithImpl(
    _$EventProductImpl _value,
    $Res Function(_$EventProductImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventProduct
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? name = null,
    Object? price = null,
    Object? quantity = null,
    Object? imageUrl = freezed,
    Object? description = freezed,
  }) {
    return _then(
      _$EventProductImpl(
        productId: null == productId
            ? _value.productId
            : productId // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        quantity: null == quantity
            ? _value.quantity
            : quantity // ignore: cast_nullable_to_non_nullable
                  as int,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EventProductImpl implements _EventProduct {
  const _$EventProductImpl({
    required this.productId,
    required this.name,
    required this.price,
    required this.quantity,
    this.imageUrl,
    this.description,
  });

  factory _$EventProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$EventProductImplFromJson(json);

  @override
  final String productId;
  @override
  final String name;
  @override
  final double price;
  @override
  final int quantity;
  @override
  final String? imageUrl;
  @override
  final String? description;

  @override
  String toString() {
    return 'EventProduct(productId: $productId, name: $name, price: $price, quantity: $quantity, imageUrl: $imageUrl, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EventProductImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    productId,
    name,
    price,
    quantity,
    imageUrl,
    description,
  );

  /// Create a copy of EventProduct
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EventProductImplCopyWith<_$EventProductImpl> get copyWith =>
      __$$EventProductImplCopyWithImpl<_$EventProductImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EventProductImplToJson(this);
  }
}

abstract class _EventProduct implements EventProduct {
  const factory _EventProduct({
    required final String productId,
    required final String name,
    required final double price,
    required final int quantity,
    final String? imageUrl,
    final String? description,
  }) = _$EventProductImpl;

  factory _EventProduct.fromJson(Map<String, dynamic> json) =
      _$EventProductImpl.fromJson;

  @override
  String get productId;
  @override
  String get name;
  @override
  double get price;
  @override
  int get quantity;
  @override
  String? get imageUrl;
  @override
  String? get description;

  /// Create a copy of EventProduct
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EventProductImplCopyWith<_$EventProductImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EventModel _$EventModelFromJson(Map<String, dynamic> json) {
  return _EventModel.fromJson(json);
}

/// @nodoc
mixin _$EventModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  EventType get type => throw _privateConstructorUsedError;
  EventStatus get status => throw _privateConstructorUsedError;
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime get endDate => throw _privateConstructorUsedError;
  String get createdBy => throw _privateConstructorUsedError;
  String? get approvedBy => throw _privateConstructorUsedError;
  String? get rejectedBy => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  bool get isPublic => throw _privateConstructorUsedError;
  bool get isRecurring => throw _privateConstructorUsedError;
  String? get recurrencePattern => throw _privateConstructorUsedError;
  Duration? get reminderBefore => throw _privateConstructorUsedError;
  String? get whatsappMessage => throw _privateConstructorUsedError;
  List<String>? get whatsappContacts => throw _privateConstructorUsedError;
  List<String>? get mediaUrls => throw _privateConstructorUsedError;
  List<EventProduct>? get products => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this EventModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EventModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EventModelCopyWith<EventModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EventModelCopyWith<$Res> {
  factory $EventModelCopyWith(
    EventModel value,
    $Res Function(EventModel) then,
  ) = _$EventModelCopyWithImpl<$Res, EventModel>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    EventType type,
    EventStatus status,
    DateTime startDate,
    DateTime endDate,
    String createdBy,
    String? approvedBy,
    String? rejectedBy,
    String? rejectionReason,
    bool isPublic,
    bool isRecurring,
    String? recurrencePattern,
    Duration? reminderBefore,
    String? whatsappMessage,
    List<String>? whatsappContacts,
    List<String>? mediaUrls,
    List<EventProduct>? products,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$EventModelCopyWithImpl<$Res, $Val extends EventModel>
    implements $EventModelCopyWith<$Res> {
  _$EventModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EventModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? type = null,
    Object? status = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? createdBy = null,
    Object? approvedBy = freezed,
    Object? rejectedBy = freezed,
    Object? rejectionReason = freezed,
    Object? isPublic = null,
    Object? isRecurring = null,
    Object? recurrencePattern = freezed,
    Object? reminderBefore = freezed,
    Object? whatsappMessage = freezed,
    Object? whatsappContacts = freezed,
    Object? mediaUrls = freezed,
    Object? products = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as EventType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as EventStatus,
            startDate: null == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            endDate: null == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            createdBy: null == createdBy
                ? _value.createdBy
                : createdBy // ignore: cast_nullable_to_non_nullable
                      as String,
            approvedBy: freezed == approvedBy
                ? _value.approvedBy
                : approvedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectedBy: freezed == rejectedBy
                ? _value.rejectedBy
                : rejectedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            isPublic: null == isPublic
                ? _value.isPublic
                : isPublic // ignore: cast_nullable_to_non_nullable
                      as bool,
            isRecurring: null == isRecurring
                ? _value.isRecurring
                : isRecurring // ignore: cast_nullable_to_non_nullable
                      as bool,
            recurrencePattern: freezed == recurrencePattern
                ? _value.recurrencePattern
                : recurrencePattern // ignore: cast_nullable_to_non_nullable
                      as String?,
            reminderBefore: freezed == reminderBefore
                ? _value.reminderBefore
                : reminderBefore // ignore: cast_nullable_to_non_nullable
                      as Duration?,
            whatsappMessage: freezed == whatsappMessage
                ? _value.whatsappMessage
                : whatsappMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            whatsappContacts: freezed == whatsappContacts
                ? _value.whatsappContacts
                : whatsappContacts // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            mediaUrls: freezed == mediaUrls
                ? _value.mediaUrls
                : mediaUrls // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            products: freezed == products
                ? _value.products
                : products // ignore: cast_nullable_to_non_nullable
                      as List<EventProduct>?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EventModelImplCopyWith<$Res>
    implements $EventModelCopyWith<$Res> {
  factory _$$EventModelImplCopyWith(
    _$EventModelImpl value,
    $Res Function(_$EventModelImpl) then,
  ) = __$$EventModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    EventType type,
    EventStatus status,
    DateTime startDate,
    DateTime endDate,
    String createdBy,
    String? approvedBy,
    String? rejectedBy,
    String? rejectionReason,
    bool isPublic,
    bool isRecurring,
    String? recurrencePattern,
    Duration? reminderBefore,
    String? whatsappMessage,
    List<String>? whatsappContacts,
    List<String>? mediaUrls,
    List<EventProduct>? products,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$EventModelImplCopyWithImpl<$Res>
    extends _$EventModelCopyWithImpl<$Res, _$EventModelImpl>
    implements _$$EventModelImplCopyWith<$Res> {
  __$$EventModelImplCopyWithImpl(
    _$EventModelImpl _value,
    $Res Function(_$EventModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? type = null,
    Object? status = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? createdBy = null,
    Object? approvedBy = freezed,
    Object? rejectedBy = freezed,
    Object? rejectionReason = freezed,
    Object? isPublic = null,
    Object? isRecurring = null,
    Object? recurrencePattern = freezed,
    Object? reminderBefore = freezed,
    Object? whatsappMessage = freezed,
    Object? whatsappContacts = freezed,
    Object? mediaUrls = freezed,
    Object? products = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$EventModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as EventType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as EventStatus,
        startDate: null == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        endDate: null == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        createdBy: null == createdBy
            ? _value.createdBy
            : createdBy // ignore: cast_nullable_to_non_nullable
                  as String,
        approvedBy: freezed == approvedBy
            ? _value.approvedBy
            : approvedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectedBy: freezed == rejectedBy
            ? _value.rejectedBy
            : rejectedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        isPublic: null == isPublic
            ? _value.isPublic
            : isPublic // ignore: cast_nullable_to_non_nullable
                  as bool,
        isRecurring: null == isRecurring
            ? _value.isRecurring
            : isRecurring // ignore: cast_nullable_to_non_nullable
                  as bool,
        recurrencePattern: freezed == recurrencePattern
            ? _value.recurrencePattern
            : recurrencePattern // ignore: cast_nullable_to_non_nullable
                  as String?,
        reminderBefore: freezed == reminderBefore
            ? _value.reminderBefore
            : reminderBefore // ignore: cast_nullable_to_non_nullable
                  as Duration?,
        whatsappMessage: freezed == whatsappMessage
            ? _value.whatsappMessage
            : whatsappMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        whatsappContacts: freezed == whatsappContacts
            ? _value._whatsappContacts
            : whatsappContacts // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        mediaUrls: freezed == mediaUrls
            ? _value._mediaUrls
            : mediaUrls // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        products: freezed == products
            ? _value._products
            : products // ignore: cast_nullable_to_non_nullable
                  as List<EventProduct>?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EventModelImpl implements _EventModel {
  const _$EventModelImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.createdBy,
    this.approvedBy,
    this.rejectedBy,
    this.rejectionReason,
    this.isPublic = false,
    this.isRecurring = false,
    this.recurrencePattern,
    this.reminderBefore,
    this.whatsappMessage,
    final List<String>? whatsappContacts,
    final List<String>? mediaUrls,
    final List<EventProduct>? products,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  }) : _whatsappContacts = whatsappContacts,
       _mediaUrls = mediaUrls,
       _products = products;

  factory _$EventModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$EventModelImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final EventType type;
  @override
  final EventStatus status;
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  @override
  final String createdBy;
  @override
  final String? approvedBy;
  @override
  final String? rejectedBy;
  @override
  final String? rejectionReason;
  @override
  @JsonKey()
  final bool isPublic;
  @override
  @JsonKey()
  final bool isRecurring;
  @override
  final String? recurrencePattern;
  @override
  final Duration? reminderBefore;
  @override
  final String? whatsappMessage;
  final List<String>? _whatsappContacts;
  @override
  List<String>? get whatsappContacts {
    final value = _whatsappContacts;
    if (value == null) return null;
    if (_whatsappContacts is EqualUnmodifiableListView)
      return _whatsappContacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _mediaUrls;
  @override
  List<String>? get mediaUrls {
    final value = _mediaUrls;
    if (value == null) return null;
    if (_mediaUrls is EqualUnmodifiableListView) return _mediaUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<EventProduct>? _products;
  @override
  List<EventProduct>? get products {
    final value = _products;
    if (value == null) return null;
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'EventModel(id: $id, title: $title, description: $description, type: $type, status: $status, startDate: $startDate, endDate: $endDate, createdBy: $createdBy, approvedBy: $approvedBy, rejectedBy: $rejectedBy, rejectionReason: $rejectionReason, isPublic: $isPublic, isRecurring: $isRecurring, recurrencePattern: $recurrencePattern, reminderBefore: $reminderBefore, whatsappMessage: $whatsappMessage, whatsappContacts: $whatsappContacts, mediaUrls: $mediaUrls, products: $products, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EventModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.approvedBy, approvedBy) ||
                other.approvedBy == approvedBy) &&
            (identical(other.rejectedBy, rejectedBy) ||
                other.rejectedBy == rejectedBy) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.isPublic, isPublic) ||
                other.isPublic == isPublic) &&
            (identical(other.isRecurring, isRecurring) ||
                other.isRecurring == isRecurring) &&
            (identical(other.recurrencePattern, recurrencePattern) ||
                other.recurrencePattern == recurrencePattern) &&
            (identical(other.reminderBefore, reminderBefore) ||
                other.reminderBefore == reminderBefore) &&
            (identical(other.whatsappMessage, whatsappMessage) ||
                other.whatsappMessage == whatsappMessage) &&
            const DeepCollectionEquality().equals(
              other._whatsappContacts,
              _whatsappContacts,
            ) &&
            const DeepCollectionEquality().equals(
              other._mediaUrls,
              _mediaUrls,
            ) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    title,
    description,
    type,
    status,
    startDate,
    endDate,
    createdBy,
    approvedBy,
    rejectedBy,
    rejectionReason,
    isPublic,
    isRecurring,
    recurrencePattern,
    reminderBefore,
    whatsappMessage,
    const DeepCollectionEquality().hash(_whatsappContacts),
    const DeepCollectionEquality().hash(_mediaUrls),
    const DeepCollectionEquality().hash(_products),
    createdAt,
    updatedAt,
    isDeleted,
  ]);

  /// Create a copy of EventModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EventModelImplCopyWith<_$EventModelImpl> get copyWith =>
      __$$EventModelImplCopyWithImpl<_$EventModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EventModelImplToJson(this);
  }
}

abstract class _EventModel implements EventModel {
  const factory _EventModel({
    required final String id,
    required final String title,
    required final String description,
    required final EventType type,
    required final EventStatus status,
    required final DateTime startDate,
    required final DateTime endDate,
    required final String createdBy,
    final String? approvedBy,
    final String? rejectedBy,
    final String? rejectionReason,
    final bool isPublic,
    final bool isRecurring,
    final String? recurrencePattern,
    final Duration? reminderBefore,
    final String? whatsappMessage,
    final List<String>? whatsappContacts,
    final List<String>? mediaUrls,
    final List<EventProduct>? products,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$EventModelImpl;

  factory _EventModel.fromJson(Map<String, dynamic> json) =
      _$EventModelImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  EventType get type;
  @override
  EventStatus get status;
  @override
  DateTime get startDate;
  @override
  DateTime get endDate;
  @override
  String get createdBy;
  @override
  String? get approvedBy;
  @override
  String? get rejectedBy;
  @override
  String? get rejectionReason;
  @override
  bool get isPublic;
  @override
  bool get isRecurring;
  @override
  String? get recurrencePattern;
  @override
  Duration? get reminderBefore;
  @override
  String? get whatsappMessage;
  @override
  List<String>? get whatsappContacts;
  @override
  List<String>? get mediaUrls;
  @override
  List<EventProduct>? get products;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of EventModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EventModelImplCopyWith<_$EventModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
