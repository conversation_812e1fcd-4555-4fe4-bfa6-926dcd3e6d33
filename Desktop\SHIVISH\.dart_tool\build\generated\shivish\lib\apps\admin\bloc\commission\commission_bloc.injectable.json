[{"type": {"import": "package:shivish/apps/admin/bloc/commission/commission_bloc.dart", "name": "CommissionBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/commission/commission_bloc.dart", "name": "CommissionBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/commission/commission_service.dart", "name": "CommissionService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_commissionService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]