[{"type": {"import": "package:shivish/apps/seller/presentation/cubits/price_management/price_management_cubit.dart", "name": "PriceManagementCubit", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/seller/presentation/cubits/price_management/price_management_cubit.dart", "name": "PriceManagementCubit", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/apps/seller/data/repositories/price_management_repository.dart", "name": "PriceManagementRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_repository", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]