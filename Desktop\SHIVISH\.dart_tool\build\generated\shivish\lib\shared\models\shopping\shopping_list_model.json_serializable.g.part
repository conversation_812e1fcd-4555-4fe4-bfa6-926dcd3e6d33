// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ShoppingListModelImpl _$$ShoppingListModelImplFromJson(
  Map<String, dynamic> json,
) => _$ShoppingListModelImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  itemCount: (json['itemCount'] as num).toInt(),
  totalPrice: (json['totalPrice'] as num).toDouble(),
  isShared: json['isShared'] as bool,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  createdBy: json['createdBy'] as String,
  isCompleted: json['isCompleted'] as bool? ?? false,
  isTemplate: json['isTemplate'] as bool? ?? false,
  sharedWith:
      (json['sharedWith'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => ShoppingListItem.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  selectedSellerId: json['selectedSellerId'] as String?,
  selectedSellerName: json['selectedSellerName'] as String?,
  hasPriceList: json['hasPriceList'] as bool? ?? false,
  priceListViewed: json['priceListViewed'] as bool? ?? false,
  status: json['status'] as String? ?? 'pending',
  priceType: json['priceType'] as String? ?? 'itemized',
  overallTotalPrice: (json['overallTotalPrice'] as num?)?.toDouble(),
);

Map<String, dynamic> _$$ShoppingListModelImplToJson(
  _$ShoppingListModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'itemCount': instance.itemCount,
  'totalPrice': instance.totalPrice,
  'isShared': instance.isShared,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'createdBy': instance.createdBy,
  'isCompleted': instance.isCompleted,
  'isTemplate': instance.isTemplate,
  'sharedWith': instance.sharedWith,
  'items': instance.items.map((e) => e.toJson()).toList(),
  'selectedSellerId': instance.selectedSellerId,
  'selectedSellerName': instance.selectedSellerName,
  'hasPriceList': instance.hasPriceList,
  'priceListViewed': instance.priceListViewed,
  'status': instance.status,
  'priceType': instance.priceType,
  'overallTotalPrice': instance.overallTotalPrice,
};

_$ShoppingListItemImpl _$$ShoppingListItemImplFromJson(
  Map<String, dynamic> json,
) => _$ShoppingListItemImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  price: (json['price'] as num).toDouble(),
  quantity: (json['quantity'] as num).toInt(),
  isChecked: json['isChecked'] as bool,
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$$ShoppingListItemImplToJson(
  _$ShoppingListItemImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'price': instance.price,
  'quantity': instance.quantity,
  'isChecked': instance.isChecked,
  'notes': instance.notes,
};
