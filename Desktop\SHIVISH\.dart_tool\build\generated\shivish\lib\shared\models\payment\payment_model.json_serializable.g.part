// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentModelImpl _$$PaymentModelImplFromJson(Map<String, dynamic> json) =>
    _$PaymentModelImpl(
      id: json['id'] as String,
      paymentNumber: json['paymentNumber'] as String,
      orderId: json['orderId'] as String,
      customerId: json['customerId'] as String,
      merchantId: json['merchantId'] as String,
      type: $enumDecode(_$PaymentTypeEnumMap, json['type']),
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      method: $enumDecode(_$PaymentMethodEnumMap, json['method']),
      gateway: $enumDecode(_$PaymentGatewayEnumMap, json['gateway']),
      amount: (json['amount'] as num).toDouble(),
      taxAmount: (json['taxAmount'] as num).toDouble(),
      feeAmount: (json['feeAmount'] as num).toDouble(),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      card: json['card'] == null
          ? null
          : CardModel.fromJson(json['card'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$PaymentModelImplToJson(_$PaymentModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'paymentNumber': instance.paymentNumber,
      'orderId': instance.orderId,
      'customerId': instance.customerId,
      'merchantId': instance.merchantId,
      'type': _$PaymentTypeEnumMap[instance.type]!,
      'status': instance.status.toJson(),
      'method': _$PaymentMethodEnumMap[instance.method]!,
      'gateway': _$PaymentGatewayEnumMap[instance.gateway]!,
      'amount': instance.amount,
      'taxAmount': instance.taxAmount,
      'feeAmount': instance.feeAmount,
      'totalAmount': instance.totalAmount,
      'card': instance.card?.toJson(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

const _$PaymentTypeEnumMap = {
  PaymentType.purchase: 'purchase',
  PaymentType.refund: 'refund',
  PaymentType.subscription: 'subscription',
  PaymentType.donation: 'donation',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.processing: 'processing',
  PaymentStatus.completed: 'completed',
  PaymentStatus.failed: 'failed',
  PaymentStatus.refunded: 'refunded',
  PaymentStatus.cancelled: 'cancelled',
};

const _$PaymentMethodEnumMap = {
  PaymentMethod.card: 'card',
  PaymentMethod.cash: 'cash',
  PaymentMethod.upi: 'upi',
  PaymentMethod.wallet: 'wallet',
};

const _$PaymentGatewayEnumMap = {
  PaymentGateway.phonepe: 'phonepe',
  PaymentGateway.upi: 'upi',
  PaymentGateway.cash: 'cash',
};
