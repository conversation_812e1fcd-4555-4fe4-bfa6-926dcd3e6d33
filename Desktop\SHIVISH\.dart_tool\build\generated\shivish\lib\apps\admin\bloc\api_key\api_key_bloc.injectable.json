[{"type": {"import": "package:shivish/apps/admin/bloc/api_key/api_key_bloc.dart", "name": "ApiKeyBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/api_key/api_key_bloc.dart", "name": "ApiKeyBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/api_key/api_key_service.dart", "name": "ApiKeyService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_apiKeyService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]