// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BookingImpl _$$BookingImplFromJson(Map<String, dynamic> json) =>
    _$BookingImpl(
      id: json['id'] as String,
      priestId: json['priestId'] as String,
      customerName: json['customerName'] as String,
      serviceType: json['serviceType'] as String,
      date: json['date'] as String,
      time: json['time'] as String,
      status: json['status'] as String,
      amount: (json['amount'] as num).toDouble(),
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$BookingImplToJson(_$BookingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'priestId': instance.priestId,
      'customerName': instance.customerName,
      'serviceType': instance.serviceType,
      'date': instance.date,
      'time': instance.time,
      'status': instance.status,
      'amount': instance.amount,
      'notes': instance.notes,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
