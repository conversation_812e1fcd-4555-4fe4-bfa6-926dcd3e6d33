// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListSubmissionOrderImpl _$$ListSubmissionOrderImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionOrderImpl(
  id: json['id'] as String,
  submissionId: json['submissionId'] as String,
  buyerId: json['buyerId'] as String,
  sellerId: json['sellerId'] as String,
  totalAmount: (json['totalAmount'] as num).toDouble(),
  items: (json['items'] as List<dynamic>)
      .map((e) => ListSubmissionOrderItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  createdAt: DateTime.parse(json['createdAt'] as String),
  isPaid: json['isPaid'] as bool? ?? false,
  paymentId: json['paymentId'] as String?,
  deliveryId: json['deliveryId'] as String?,
  status: json['status'] as String? ?? 'pending',
);

Map<String, dynamic> _$$ListSubmissionOrderImplToJson(
  _$ListSubmissionOrderImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'submissionId': instance.submissionId,
  'buyerId': instance.buyerId,
  'sellerId': instance.sellerId,
  'totalAmount': instance.totalAmount,
  'items': instance.items.map((e) => e.toJson()).toList(),
  'createdAt': instance.createdAt.toIso8601String(),
  'isPaid': instance.isPaid,
  'paymentId': instance.paymentId,
  'deliveryId': instance.deliveryId,
  'status': instance.status,
};

_$ListSubmissionOrderItemImpl _$$ListSubmissionOrderItemImplFromJson(
  Map<String, dynamic> json,
) => _$ListSubmissionOrderItemImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  quantity: (json['quantity'] as num).toInt(),
  price: (json['price'] as num).toDouble(),
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$$ListSubmissionOrderItemImplToJson(
  _$ListSubmissionOrderItemImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'quantity': instance.quantity,
  'price': instance.price,
  'notes': instance.notes,
};
