[{"type": {"import": "package:shivish/apps/seller/data/repositories/price_management_repository.dart", "name": "PriceManagementRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/seller/data/repositories/price_management_repository.dart", "name": "PriceManagementRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:cloud_firestore/cloud_firestore.dart", "name": "FirebaseFirestore", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_firestore", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]