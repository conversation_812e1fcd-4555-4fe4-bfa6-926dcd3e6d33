// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lab_test_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

LabTestModel _$LabTestModelFromJson(Map<String, dynamic> json) {
  return _LabTestModel.fromJson(json);
}

/// @nodoc
mixin _$LabTestModel {
  String get id => throw _privateConstructorUsedError;
  String get testNumber => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get hospitalId => throw _privateConstructorUsedError;
  String get hospitalName => throw _privateConstructorUsedError;
  String get patientId => throw _privateConstructorUsedError;
  String get patientName => throw _privateConstructorUsedError;
  String get patientPhone => throw _privateConstructorUsedError;
  String? get patientEmail => throw _privateConstructorUsedError;
  String? get doctorId => throw _privateConstructorUsedError;
  String? get doctorName => throw _privateConstructorUsedError;
  DateTime get testDate => throw _privateConstructorUsedError;
  String? get timeSlot => throw _privateConstructorUsedError;
  LabTestStatus get status => throw _privateConstructorUsedError;
  LabTestCategory get category => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  bool get isPaid => throw _privateConstructorUsedError;
  String? get paymentId => throw _privateConstructorUsedError;
  String? get reportUrl => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get sampleType => throw _privateConstructorUsedError;
  int? get turnaroundTime => throw _privateConstructorUsedError;
  String? get instructions => throw _privateConstructorUsedError;
  bool? get isAvailable => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  DateTime? get deletedAt => throw _privateConstructorUsedError;

  /// Serializes this LabTestModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LabTestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LabTestModelCopyWith<LabTestModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LabTestModelCopyWith<$Res> {
  factory $LabTestModelCopyWith(
    LabTestModel value,
    $Res Function(LabTestModel) then,
  ) = _$LabTestModelCopyWithImpl<$Res, LabTestModel>;
  @useResult
  $Res call({
    String id,
    String testNumber,
    String name,
    String description,
    String hospitalId,
    String hospitalName,
    String patientId,
    String patientName,
    String patientPhone,
    String? patientEmail,
    String? doctorId,
    String? doctorName,
    DateTime testDate,
    String? timeSlot,
    LabTestStatus status,
    LabTestCategory category,
    double price,
    bool isPaid,
    String? paymentId,
    String? reportUrl,
    String? notes,
    String? sampleType,
    int? turnaroundTime,
    String? instructions,
    bool? isAvailable,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });
}

/// @nodoc
class _$LabTestModelCopyWithImpl<$Res, $Val extends LabTestModel>
    implements $LabTestModelCopyWith<$Res> {
  _$LabTestModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LabTestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? testNumber = null,
    Object? name = null,
    Object? description = null,
    Object? hospitalId = null,
    Object? hospitalName = null,
    Object? patientId = null,
    Object? patientName = null,
    Object? patientPhone = null,
    Object? patientEmail = freezed,
    Object? doctorId = freezed,
    Object? doctorName = freezed,
    Object? testDate = null,
    Object? timeSlot = freezed,
    Object? status = null,
    Object? category = null,
    Object? price = null,
    Object? isPaid = null,
    Object? paymentId = freezed,
    Object? reportUrl = freezed,
    Object? notes = freezed,
    Object? sampleType = freezed,
    Object? turnaroundTime = freezed,
    Object? instructions = freezed,
    Object? isAvailable = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            testNumber: null == testNumber
                ? _value.testNumber
                : testNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            hospitalId: null == hospitalId
                ? _value.hospitalId
                : hospitalId // ignore: cast_nullable_to_non_nullable
                      as String,
            hospitalName: null == hospitalName
                ? _value.hospitalName
                : hospitalName // ignore: cast_nullable_to_non_nullable
                      as String,
            patientId: null == patientId
                ? _value.patientId
                : patientId // ignore: cast_nullable_to_non_nullable
                      as String,
            patientName: null == patientName
                ? _value.patientName
                : patientName // ignore: cast_nullable_to_non_nullable
                      as String,
            patientPhone: null == patientPhone
                ? _value.patientPhone
                : patientPhone // ignore: cast_nullable_to_non_nullable
                      as String,
            patientEmail: freezed == patientEmail
                ? _value.patientEmail
                : patientEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            doctorId: freezed == doctorId
                ? _value.doctorId
                : doctorId // ignore: cast_nullable_to_non_nullable
                      as String?,
            doctorName: freezed == doctorName
                ? _value.doctorName
                : doctorName // ignore: cast_nullable_to_non_nullable
                      as String?,
            testDate: null == testDate
                ? _value.testDate
                : testDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            timeSlot: freezed == timeSlot
                ? _value.timeSlot
                : timeSlot // ignore: cast_nullable_to_non_nullable
                      as String?,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as LabTestStatus,
            category: null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                      as LabTestCategory,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            isPaid: null == isPaid
                ? _value.isPaid
                : isPaid // ignore: cast_nullable_to_non_nullable
                      as bool,
            paymentId: freezed == paymentId
                ? _value.paymentId
                : paymentId // ignore: cast_nullable_to_non_nullable
                      as String?,
            reportUrl: freezed == reportUrl
                ? _value.reportUrl
                : reportUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            sampleType: freezed == sampleType
                ? _value.sampleType
                : sampleType // ignore: cast_nullable_to_non_nullable
                      as String?,
            turnaroundTime: freezed == turnaroundTime
                ? _value.turnaroundTime
                : turnaroundTime // ignore: cast_nullable_to_non_nullable
                      as int?,
            instructions: freezed == instructions
                ? _value.instructions
                : instructions // ignore: cast_nullable_to_non_nullable
                      as String?,
            isAvailable: freezed == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            deletedAt: freezed == deletedAt
                ? _value.deletedAt
                : deletedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LabTestModelImplCopyWith<$Res>
    implements $LabTestModelCopyWith<$Res> {
  factory _$$LabTestModelImplCopyWith(
    _$LabTestModelImpl value,
    $Res Function(_$LabTestModelImpl) then,
  ) = __$$LabTestModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String testNumber,
    String name,
    String description,
    String hospitalId,
    String hospitalName,
    String patientId,
    String patientName,
    String patientPhone,
    String? patientEmail,
    String? doctorId,
    String? doctorName,
    DateTime testDate,
    String? timeSlot,
    LabTestStatus status,
    LabTestCategory category,
    double price,
    bool isPaid,
    String? paymentId,
    String? reportUrl,
    String? notes,
    String? sampleType,
    int? turnaroundTime,
    String? instructions,
    bool? isAvailable,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    DateTime? deletedAt,
  });
}

/// @nodoc
class __$$LabTestModelImplCopyWithImpl<$Res>
    extends _$LabTestModelCopyWithImpl<$Res, _$LabTestModelImpl>
    implements _$$LabTestModelImplCopyWith<$Res> {
  __$$LabTestModelImplCopyWithImpl(
    _$LabTestModelImpl _value,
    $Res Function(_$LabTestModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LabTestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? testNumber = null,
    Object? name = null,
    Object? description = null,
    Object? hospitalId = null,
    Object? hospitalName = null,
    Object? patientId = null,
    Object? patientName = null,
    Object? patientPhone = null,
    Object? patientEmail = freezed,
    Object? doctorId = freezed,
    Object? doctorName = freezed,
    Object? testDate = null,
    Object? timeSlot = freezed,
    Object? status = null,
    Object? category = null,
    Object? price = null,
    Object? isPaid = null,
    Object? paymentId = freezed,
    Object? reportUrl = freezed,
    Object? notes = freezed,
    Object? sampleType = freezed,
    Object? turnaroundTime = freezed,
    Object? instructions = freezed,
    Object? isAvailable = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
  }) {
    return _then(
      _$LabTestModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        testNumber: null == testNumber
            ? _value.testNumber
            : testNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        hospitalId: null == hospitalId
            ? _value.hospitalId
            : hospitalId // ignore: cast_nullable_to_non_nullable
                  as String,
        hospitalName: null == hospitalName
            ? _value.hospitalName
            : hospitalName // ignore: cast_nullable_to_non_nullable
                  as String,
        patientId: null == patientId
            ? _value.patientId
            : patientId // ignore: cast_nullable_to_non_nullable
                  as String,
        patientName: null == patientName
            ? _value.patientName
            : patientName // ignore: cast_nullable_to_non_nullable
                  as String,
        patientPhone: null == patientPhone
            ? _value.patientPhone
            : patientPhone // ignore: cast_nullable_to_non_nullable
                  as String,
        patientEmail: freezed == patientEmail
            ? _value.patientEmail
            : patientEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        doctorId: freezed == doctorId
            ? _value.doctorId
            : doctorId // ignore: cast_nullable_to_non_nullable
                  as String?,
        doctorName: freezed == doctorName
            ? _value.doctorName
            : doctorName // ignore: cast_nullable_to_non_nullable
                  as String?,
        testDate: null == testDate
            ? _value.testDate
            : testDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        timeSlot: freezed == timeSlot
            ? _value.timeSlot
            : timeSlot // ignore: cast_nullable_to_non_nullable
                  as String?,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as LabTestStatus,
        category: null == category
            ? _value.category
            : category // ignore: cast_nullable_to_non_nullable
                  as LabTestCategory,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        isPaid: null == isPaid
            ? _value.isPaid
            : isPaid // ignore: cast_nullable_to_non_nullable
                  as bool,
        paymentId: freezed == paymentId
            ? _value.paymentId
            : paymentId // ignore: cast_nullable_to_non_nullable
                  as String?,
        reportUrl: freezed == reportUrl
            ? _value.reportUrl
            : reportUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        sampleType: freezed == sampleType
            ? _value.sampleType
            : sampleType // ignore: cast_nullable_to_non_nullable
                  as String?,
        turnaroundTime: freezed == turnaroundTime
            ? _value.turnaroundTime
            : turnaroundTime // ignore: cast_nullable_to_non_nullable
                  as int?,
        instructions: freezed == instructions
            ? _value.instructions
            : instructions // ignore: cast_nullable_to_non_nullable
                  as String?,
        isAvailable: freezed == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        deletedAt: freezed == deletedAt
            ? _value.deletedAt
            : deletedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LabTestModelImpl implements _LabTestModel {
  const _$LabTestModelImpl({
    required this.id,
    required this.testNumber,
    required this.name,
    required this.description,
    required this.hospitalId,
    required this.hospitalName,
    required this.patientId,
    required this.patientName,
    required this.patientPhone,
    this.patientEmail,
    this.doctorId,
    this.doctorName,
    required this.testDate,
    this.timeSlot,
    required this.status,
    required this.category,
    required this.price,
    required this.isPaid,
    this.paymentId,
    this.reportUrl,
    this.notes,
    this.sampleType,
    this.turnaroundTime,
    this.instructions,
    this.isAvailable,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    this.deletedAt,
  });

  factory _$LabTestModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LabTestModelImplFromJson(json);

  @override
  final String id;
  @override
  final String testNumber;
  @override
  final String name;
  @override
  final String description;
  @override
  final String hospitalId;
  @override
  final String hospitalName;
  @override
  final String patientId;
  @override
  final String patientName;
  @override
  final String patientPhone;
  @override
  final String? patientEmail;
  @override
  final String? doctorId;
  @override
  final String? doctorName;
  @override
  final DateTime testDate;
  @override
  final String? timeSlot;
  @override
  final LabTestStatus status;
  @override
  final LabTestCategory category;
  @override
  final double price;
  @override
  final bool isPaid;
  @override
  final String? paymentId;
  @override
  final String? reportUrl;
  @override
  final String? notes;
  @override
  final String? sampleType;
  @override
  final int? turnaroundTime;
  @override
  final String? instructions;
  @override
  final bool? isAvailable;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  final DateTime? deletedAt;

  @override
  String toString() {
    return 'LabTestModel(id: $id, testNumber: $testNumber, name: $name, description: $description, hospitalId: $hospitalId, hospitalName: $hospitalName, patientId: $patientId, patientName: $patientName, patientPhone: $patientPhone, patientEmail: $patientEmail, doctorId: $doctorId, doctorName: $doctorName, testDate: $testDate, timeSlot: $timeSlot, status: $status, category: $category, price: $price, isPaid: $isPaid, paymentId: $paymentId, reportUrl: $reportUrl, notes: $notes, sampleType: $sampleType, turnaroundTime: $turnaroundTime, instructions: $instructions, isAvailable: $isAvailable, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LabTestModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.testNumber, testNumber) ||
                other.testNumber == testNumber) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.hospitalId, hospitalId) ||
                other.hospitalId == hospitalId) &&
            (identical(other.hospitalName, hospitalName) ||
                other.hospitalName == hospitalName) &&
            (identical(other.patientId, patientId) ||
                other.patientId == patientId) &&
            (identical(other.patientName, patientName) ||
                other.patientName == patientName) &&
            (identical(other.patientPhone, patientPhone) ||
                other.patientPhone == patientPhone) &&
            (identical(other.patientEmail, patientEmail) ||
                other.patientEmail == patientEmail) &&
            (identical(other.doctorId, doctorId) ||
                other.doctorId == doctorId) &&
            (identical(other.doctorName, doctorName) ||
                other.doctorName == doctorName) &&
            (identical(other.testDate, testDate) ||
                other.testDate == testDate) &&
            (identical(other.timeSlot, timeSlot) ||
                other.timeSlot == timeSlot) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.isPaid, isPaid) || other.isPaid == isPaid) &&
            (identical(other.paymentId, paymentId) ||
                other.paymentId == paymentId) &&
            (identical(other.reportUrl, reportUrl) ||
                other.reportUrl == reportUrl) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.sampleType, sampleType) ||
                other.sampleType == sampleType) &&
            (identical(other.turnaroundTime, turnaroundTime) ||
                other.turnaroundTime == turnaroundTime) &&
            (identical(other.instructions, instructions) ||
                other.instructions == instructions) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    testNumber,
    name,
    description,
    hospitalId,
    hospitalName,
    patientId,
    patientName,
    patientPhone,
    patientEmail,
    doctorId,
    doctorName,
    testDate,
    timeSlot,
    status,
    category,
    price,
    isPaid,
    paymentId,
    reportUrl,
    notes,
    sampleType,
    turnaroundTime,
    instructions,
    isAvailable,
    createdAt,
    updatedAt,
    isDeleted,
    deletedAt,
  ]);

  /// Create a copy of LabTestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LabTestModelImplCopyWith<_$LabTestModelImpl> get copyWith =>
      __$$LabTestModelImplCopyWithImpl<_$LabTestModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LabTestModelImplToJson(this);
  }
}

abstract class _LabTestModel implements LabTestModel {
  const factory _LabTestModel({
    required final String id,
    required final String testNumber,
    required final String name,
    required final String description,
    required final String hospitalId,
    required final String hospitalName,
    required final String patientId,
    required final String patientName,
    required final String patientPhone,
    final String? patientEmail,
    final String? doctorId,
    final String? doctorName,
    required final DateTime testDate,
    final String? timeSlot,
    required final LabTestStatus status,
    required final LabTestCategory category,
    required final double price,
    required final bool isPaid,
    final String? paymentId,
    final String? reportUrl,
    final String? notes,
    final String? sampleType,
    final int? turnaroundTime,
    final String? instructions,
    final bool? isAvailable,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    final DateTime? deletedAt,
  }) = _$LabTestModelImpl;

  factory _LabTestModel.fromJson(Map<String, dynamic> json) =
      _$LabTestModelImpl.fromJson;

  @override
  String get id;
  @override
  String get testNumber;
  @override
  String get name;
  @override
  String get description;
  @override
  String get hospitalId;
  @override
  String get hospitalName;
  @override
  String get patientId;
  @override
  String get patientName;
  @override
  String get patientPhone;
  @override
  String? get patientEmail;
  @override
  String? get doctorId;
  @override
  String? get doctorName;
  @override
  DateTime get testDate;
  @override
  String? get timeSlot;
  @override
  LabTestStatus get status;
  @override
  LabTestCategory get category;
  @override
  double get price;
  @override
  bool get isPaid;
  @override
  String? get paymentId;
  @override
  String? get reportUrl;
  @override
  String? get notes;
  @override
  String? get sampleType;
  @override
  int? get turnaroundTime;
  @override
  String? get instructions;
  @override
  bool? get isAvailable;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  DateTime? get deletedAt;

  /// Create a copy of LabTestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LabTestModelImplCopyWith<_$LabTestModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
