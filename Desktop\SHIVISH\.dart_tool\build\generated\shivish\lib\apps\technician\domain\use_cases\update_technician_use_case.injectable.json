[{"type": {"import": "package:shivish/apps/technician/domain/use_cases/update_technician_use_case.dart", "name": "UpdateTechnicianUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/technician/domain/use_cases/update_technician_use_case.dart", "name": "UpdateTechnicianUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/apps/technician/domain/repositories/technician_repository.dart", "name": "TechnicianRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_repository", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]