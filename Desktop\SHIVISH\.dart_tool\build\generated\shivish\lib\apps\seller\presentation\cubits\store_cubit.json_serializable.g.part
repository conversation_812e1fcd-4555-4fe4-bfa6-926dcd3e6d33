// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StoreStateImpl _$$StoreStateImplFromJson(Map<String, dynamic> json) =>
    _$StoreStateImpl(
      store: json['store'] == null
          ? null
          : StoreModel.fromJson(json['store'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool? ?? false,
      isEditing: json['isEditing'] as bool? ?? false,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$$StoreStateImplToJson(_$StoreStateImpl instance) =>
    <String, dynamic>{
      'store': instance.store?.toJson(),
      'isLoading': instance.isLoading,
      'isEditing': instance.isEditing,
      'error': instance.error,
    };
