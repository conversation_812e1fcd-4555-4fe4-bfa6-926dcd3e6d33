// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

MediaMetadata _$MediaMetadataFromJson(Map<String, dynamic> json) {
  return _MediaMetadata.fromJson(json);
}

/// @nodoc
mixin _$MediaMetadata {
  String get id => throw _privateConstructorUsedError;
  String get fileName => throw _privateConstructorUsedError;
  String get fileExtension => throw _privateConstructorUsedError;
  String get mimeType => throw _privateConstructorUsedError;
  int get fileSize => throw _privateConstructorUsedError;
  int? get width => throw _privateConstructorUsedError;
  int? get height => throw _privateConstructorUsedError;
  double? get duration => throw _privateConstructorUsedError;
  String? get codec => throw _privateConstructorUsedError;
  String? get bitrate => throw _privateConstructorUsedError;
  String? get resolution => throw _privateConstructorUsedError;
  String? get orientation => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this MediaMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MediaMetadataCopyWith<MediaMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaMetadataCopyWith<$Res> {
  factory $MediaMetadataCopyWith(
    MediaMetadata value,
    $Res Function(MediaMetadata) then,
  ) = _$MediaMetadataCopyWithImpl<$Res, MediaMetadata>;
  @useResult
  $Res call({
    String id,
    String fileName,
    String fileExtension,
    String mimeType,
    int fileSize,
    int? width,
    int? height,
    double? duration,
    String? codec,
    String? bitrate,
    String? resolution,
    String? orientation,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$MediaMetadataCopyWithImpl<$Res, $Val extends MediaMetadata>
    implements $MediaMetadataCopyWith<$Res> {
  _$MediaMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fileName = null,
    Object? fileExtension = null,
    Object? mimeType = null,
    Object? fileSize = null,
    Object? width = freezed,
    Object? height = freezed,
    Object? duration = freezed,
    Object? codec = freezed,
    Object? bitrate = freezed,
    Object? resolution = freezed,
    Object? orientation = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            fileName: null == fileName
                ? _value.fileName
                : fileName // ignore: cast_nullable_to_non_nullable
                      as String,
            fileExtension: null == fileExtension
                ? _value.fileExtension
                : fileExtension // ignore: cast_nullable_to_non_nullable
                      as String,
            mimeType: null == mimeType
                ? _value.mimeType
                : mimeType // ignore: cast_nullable_to_non_nullable
                      as String,
            fileSize: null == fileSize
                ? _value.fileSize
                : fileSize // ignore: cast_nullable_to_non_nullable
                      as int,
            width: freezed == width
                ? _value.width
                : width // ignore: cast_nullable_to_non_nullable
                      as int?,
            height: freezed == height
                ? _value.height
                : height // ignore: cast_nullable_to_non_nullable
                      as int?,
            duration: freezed == duration
                ? _value.duration
                : duration // ignore: cast_nullable_to_non_nullable
                      as double?,
            codec: freezed == codec
                ? _value.codec
                : codec // ignore: cast_nullable_to_non_nullable
                      as String?,
            bitrate: freezed == bitrate
                ? _value.bitrate
                : bitrate // ignore: cast_nullable_to_non_nullable
                      as String?,
            resolution: freezed == resolution
                ? _value.resolution
                : resolution // ignore: cast_nullable_to_non_nullable
                      as String?,
            orientation: freezed == orientation
                ? _value.orientation
                : orientation // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MediaMetadataImplCopyWith<$Res>
    implements $MediaMetadataCopyWith<$Res> {
  factory _$$MediaMetadataImplCopyWith(
    _$MediaMetadataImpl value,
    $Res Function(_$MediaMetadataImpl) then,
  ) = __$$MediaMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String fileName,
    String fileExtension,
    String mimeType,
    int fileSize,
    int? width,
    int? height,
    double? duration,
    String? codec,
    String? bitrate,
    String? resolution,
    String? orientation,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$MediaMetadataImplCopyWithImpl<$Res>
    extends _$MediaMetadataCopyWithImpl<$Res, _$MediaMetadataImpl>
    implements _$$MediaMetadataImplCopyWith<$Res> {
  __$$MediaMetadataImplCopyWithImpl(
    _$MediaMetadataImpl _value,
    $Res Function(_$MediaMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fileName = null,
    Object? fileExtension = null,
    Object? mimeType = null,
    Object? fileSize = null,
    Object? width = freezed,
    Object? height = freezed,
    Object? duration = freezed,
    Object? codec = freezed,
    Object? bitrate = freezed,
    Object? resolution = freezed,
    Object? orientation = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$MediaMetadataImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        fileName: null == fileName
            ? _value.fileName
            : fileName // ignore: cast_nullable_to_non_nullable
                  as String,
        fileExtension: null == fileExtension
            ? _value.fileExtension
            : fileExtension // ignore: cast_nullable_to_non_nullable
                  as String,
        mimeType: null == mimeType
            ? _value.mimeType
            : mimeType // ignore: cast_nullable_to_non_nullable
                  as String,
        fileSize: null == fileSize
            ? _value.fileSize
            : fileSize // ignore: cast_nullable_to_non_nullable
                  as int,
        width: freezed == width
            ? _value.width
            : width // ignore: cast_nullable_to_non_nullable
                  as int?,
        height: freezed == height
            ? _value.height
            : height // ignore: cast_nullable_to_non_nullable
                  as int?,
        duration: freezed == duration
            ? _value.duration
            : duration // ignore: cast_nullable_to_non_nullable
                  as double?,
        codec: freezed == codec
            ? _value.codec
            : codec // ignore: cast_nullable_to_non_nullable
                  as String?,
        bitrate: freezed == bitrate
            ? _value.bitrate
            : bitrate // ignore: cast_nullable_to_non_nullable
                  as String?,
        resolution: freezed == resolution
            ? _value.resolution
            : resolution // ignore: cast_nullable_to_non_nullable
                  as String?,
        orientation: freezed == orientation
            ? _value.orientation
            : orientation // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaMetadataImpl implements _MediaMetadata {
  const _$MediaMetadataImpl({
    required this.id,
    required this.fileName,
    required this.fileExtension,
    required this.mimeType,
    required this.fileSize,
    this.width,
    this.height,
    this.duration,
    this.codec,
    this.bitrate,
    this.resolution,
    this.orientation,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$MediaMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaMetadataImplFromJson(json);

  @override
  final String id;
  @override
  final String fileName;
  @override
  final String fileExtension;
  @override
  final String mimeType;
  @override
  final int fileSize;
  @override
  final int? width;
  @override
  final int? height;
  @override
  final double? duration;
  @override
  final String? codec;
  @override
  final String? bitrate;
  @override
  final String? resolution;
  @override
  final String? orientation;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'MediaMetadata(id: $id, fileName: $fileName, fileExtension: $fileExtension, mimeType: $mimeType, fileSize: $fileSize, width: $width, height: $height, duration: $duration, codec: $codec, bitrate: $bitrate, resolution: $resolution, orientation: $orientation, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaMetadataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.fileExtension, fileExtension) ||
                other.fileExtension == fileExtension) &&
            (identical(other.mimeType, mimeType) ||
                other.mimeType == mimeType) &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.codec, codec) || other.codec == codec) &&
            (identical(other.bitrate, bitrate) || other.bitrate == bitrate) &&
            (identical(other.resolution, resolution) ||
                other.resolution == resolution) &&
            (identical(other.orientation, orientation) ||
                other.orientation == orientation) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    fileName,
    fileExtension,
    mimeType,
    fileSize,
    width,
    height,
    duration,
    codec,
    bitrate,
    resolution,
    orientation,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaMetadataImplCopyWith<_$MediaMetadataImpl> get copyWith =>
      __$$MediaMetadataImplCopyWithImpl<_$MediaMetadataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaMetadataImplToJson(this);
  }
}

abstract class _MediaMetadata implements MediaMetadata {
  const factory _MediaMetadata({
    required final String id,
    required final String fileName,
    required final String fileExtension,
    required final String mimeType,
    required final int fileSize,
    final int? width,
    final int? height,
    final double? duration,
    final String? codec,
    final String? bitrate,
    final String? resolution,
    final String? orientation,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$MediaMetadataImpl;

  factory _MediaMetadata.fromJson(Map<String, dynamic> json) =
      _$MediaMetadataImpl.fromJson;

  @override
  String get id;
  @override
  String get fileName;
  @override
  String get fileExtension;
  @override
  String get mimeType;
  @override
  int get fileSize;
  @override
  int? get width;
  @override
  int? get height;
  @override
  double? get duration;
  @override
  String? get codec;
  @override
  String? get bitrate;
  @override
  String? get resolution;
  @override
  String? get orientation;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaMetadataImplCopyWith<_$MediaMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MediaVariant _$MediaVariantFromJson(Map<String, dynamic> json) {
  return _MediaVariant.fromJson(json);
}

/// @nodoc
mixin _$MediaVariant {
  String get id => throw _privateConstructorUsedError;
  MediaQuality get quality => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;
  String get path => throw _privateConstructorUsedError;
  int get fileSize => throw _privateConstructorUsedError;
  int? get width => throw _privateConstructorUsedError;
  int? get height => throw _privateConstructorUsedError;
  double? get duration => throw _privateConstructorUsedError;
  String? get codec => throw _privateConstructorUsedError;
  String? get bitrate => throw _privateConstructorUsedError;
  String? get resolution => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this MediaVariant to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MediaVariantCopyWith<MediaVariant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaVariantCopyWith<$Res> {
  factory $MediaVariantCopyWith(
    MediaVariant value,
    $Res Function(MediaVariant) then,
  ) = _$MediaVariantCopyWithImpl<$Res, MediaVariant>;
  @useResult
  $Res call({
    String id,
    MediaQuality quality,
    String url,
    String path,
    int fileSize,
    int? width,
    int? height,
    double? duration,
    String? codec,
    String? bitrate,
    String? resolution,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$MediaVariantCopyWithImpl<$Res, $Val extends MediaVariant>
    implements $MediaVariantCopyWith<$Res> {
  _$MediaVariantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? quality = null,
    Object? url = null,
    Object? path = null,
    Object? fileSize = null,
    Object? width = freezed,
    Object? height = freezed,
    Object? duration = freezed,
    Object? codec = freezed,
    Object? bitrate = freezed,
    Object? resolution = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            quality: null == quality
                ? _value.quality
                : quality // ignore: cast_nullable_to_non_nullable
                      as MediaQuality,
            url: null == url
                ? _value.url
                : url // ignore: cast_nullable_to_non_nullable
                      as String,
            path: null == path
                ? _value.path
                : path // ignore: cast_nullable_to_non_nullable
                      as String,
            fileSize: null == fileSize
                ? _value.fileSize
                : fileSize // ignore: cast_nullable_to_non_nullable
                      as int,
            width: freezed == width
                ? _value.width
                : width // ignore: cast_nullable_to_non_nullable
                      as int?,
            height: freezed == height
                ? _value.height
                : height // ignore: cast_nullable_to_non_nullable
                      as int?,
            duration: freezed == duration
                ? _value.duration
                : duration // ignore: cast_nullable_to_non_nullable
                      as double?,
            codec: freezed == codec
                ? _value.codec
                : codec // ignore: cast_nullable_to_non_nullable
                      as String?,
            bitrate: freezed == bitrate
                ? _value.bitrate
                : bitrate // ignore: cast_nullable_to_non_nullable
                      as String?,
            resolution: freezed == resolution
                ? _value.resolution
                : resolution // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MediaVariantImplCopyWith<$Res>
    implements $MediaVariantCopyWith<$Res> {
  factory _$$MediaVariantImplCopyWith(
    _$MediaVariantImpl value,
    $Res Function(_$MediaVariantImpl) then,
  ) = __$$MediaVariantImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    MediaQuality quality,
    String url,
    String path,
    int fileSize,
    int? width,
    int? height,
    double? duration,
    String? codec,
    String? bitrate,
    String? resolution,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$MediaVariantImplCopyWithImpl<$Res>
    extends _$MediaVariantCopyWithImpl<$Res, _$MediaVariantImpl>
    implements _$$MediaVariantImplCopyWith<$Res> {
  __$$MediaVariantImplCopyWithImpl(
    _$MediaVariantImpl _value,
    $Res Function(_$MediaVariantImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? quality = null,
    Object? url = null,
    Object? path = null,
    Object? fileSize = null,
    Object? width = freezed,
    Object? height = freezed,
    Object? duration = freezed,
    Object? codec = freezed,
    Object? bitrate = freezed,
    Object? resolution = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$MediaVariantImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        quality: null == quality
            ? _value.quality
            : quality // ignore: cast_nullable_to_non_nullable
                  as MediaQuality,
        url: null == url
            ? _value.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        path: null == path
            ? _value.path
            : path // ignore: cast_nullable_to_non_nullable
                  as String,
        fileSize: null == fileSize
            ? _value.fileSize
            : fileSize // ignore: cast_nullable_to_non_nullable
                  as int,
        width: freezed == width
            ? _value.width
            : width // ignore: cast_nullable_to_non_nullable
                  as int?,
        height: freezed == height
            ? _value.height
            : height // ignore: cast_nullable_to_non_nullable
                  as int?,
        duration: freezed == duration
            ? _value.duration
            : duration // ignore: cast_nullable_to_non_nullable
                  as double?,
        codec: freezed == codec
            ? _value.codec
            : codec // ignore: cast_nullable_to_non_nullable
                  as String?,
        bitrate: freezed == bitrate
            ? _value.bitrate
            : bitrate // ignore: cast_nullable_to_non_nullable
                  as String?,
        resolution: freezed == resolution
            ? _value.resolution
            : resolution // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaVariantImpl implements _MediaVariant {
  const _$MediaVariantImpl({
    required this.id,
    required this.quality,
    required this.url,
    required this.path,
    required this.fileSize,
    this.width,
    this.height,
    this.duration,
    this.codec,
    this.bitrate,
    this.resolution,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$MediaVariantImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaVariantImplFromJson(json);

  @override
  final String id;
  @override
  final MediaQuality quality;
  @override
  final String url;
  @override
  final String path;
  @override
  final int fileSize;
  @override
  final int? width;
  @override
  final int? height;
  @override
  final double? duration;
  @override
  final String? codec;
  @override
  final String? bitrate;
  @override
  final String? resolution;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'MediaVariant(id: $id, quality: $quality, url: $url, path: $path, fileSize: $fileSize, width: $width, height: $height, duration: $duration, codec: $codec, bitrate: $bitrate, resolution: $resolution, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaVariantImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.quality, quality) || other.quality == quality) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.path, path) || other.path == path) &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.codec, codec) || other.codec == codec) &&
            (identical(other.bitrate, bitrate) || other.bitrate == bitrate) &&
            (identical(other.resolution, resolution) ||
                other.resolution == resolution) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    quality,
    url,
    path,
    fileSize,
    width,
    height,
    duration,
    codec,
    bitrate,
    resolution,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaVariantImplCopyWith<_$MediaVariantImpl> get copyWith =>
      __$$MediaVariantImplCopyWithImpl<_$MediaVariantImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaVariantImplToJson(this);
  }
}

abstract class _MediaVariant implements MediaVariant {
  const factory _MediaVariant({
    required final String id,
    required final MediaQuality quality,
    required final String url,
    required final String path,
    required final int fileSize,
    final int? width,
    final int? height,
    final double? duration,
    final String? codec,
    final String? bitrate,
    final String? resolution,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$MediaVariantImpl;

  factory _MediaVariant.fromJson(Map<String, dynamic> json) =
      _$MediaVariantImpl.fromJson;

  @override
  String get id;
  @override
  MediaQuality get quality;
  @override
  String get url;
  @override
  String get path;
  @override
  int get fileSize;
  @override
  int? get width;
  @override
  int? get height;
  @override
  double? get duration;
  @override
  String? get codec;
  @override
  String? get bitrate;
  @override
  String? get resolution;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of MediaVariant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaVariantImplCopyWith<_$MediaVariantImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MediaModel _$MediaModelFromJson(Map<String, dynamic> json) {
  return _MediaModel.fromJson(json);
}

/// @nodoc
mixin _$MediaModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;
  MediaType get type => throw _privateConstructorUsedError;
  String get uploadedBy => throw _privateConstructorUsedError;
  DateTime get uploadedAt => throw _privateConstructorUsedError;
  MediaStatus get status => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;

  /// Serializes this MediaModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MediaModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MediaModelCopyWith<MediaModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaModelCopyWith<$Res> {
  factory $MediaModelCopyWith(
    MediaModel value,
    $Res Function(MediaModel) then,
  ) = _$MediaModelCopyWithImpl<$Res, MediaModel>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String url,
    MediaType type,
    String uploadedBy,
    DateTime uploadedAt,
    MediaStatus status,
    String? rejectionReason,
  });
}

/// @nodoc
class _$MediaModelCopyWithImpl<$Res, $Val extends MediaModel>
    implements $MediaModelCopyWith<$Res> {
  _$MediaModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MediaModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? url = null,
    Object? type = null,
    Object? uploadedBy = null,
    Object? uploadedAt = null,
    Object? status = null,
    Object? rejectionReason = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            url: null == url
                ? _value.url
                : url // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as MediaType,
            uploadedBy: null == uploadedBy
                ? _value.uploadedBy
                : uploadedBy // ignore: cast_nullable_to_non_nullable
                      as String,
            uploadedAt: null == uploadedAt
                ? _value.uploadedAt
                : uploadedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as MediaStatus,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MediaModelImplCopyWith<$Res>
    implements $MediaModelCopyWith<$Res> {
  factory _$$MediaModelImplCopyWith(
    _$MediaModelImpl value,
    $Res Function(_$MediaModelImpl) then,
  ) = __$$MediaModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String url,
    MediaType type,
    String uploadedBy,
    DateTime uploadedAt,
    MediaStatus status,
    String? rejectionReason,
  });
}

/// @nodoc
class __$$MediaModelImplCopyWithImpl<$Res>
    extends _$MediaModelCopyWithImpl<$Res, _$MediaModelImpl>
    implements _$$MediaModelImplCopyWith<$Res> {
  __$$MediaModelImplCopyWithImpl(
    _$MediaModelImpl _value,
    $Res Function(_$MediaModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MediaModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? url = null,
    Object? type = null,
    Object? uploadedBy = null,
    Object? uploadedAt = null,
    Object? status = null,
    Object? rejectionReason = freezed,
  }) {
    return _then(
      _$MediaModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _value.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as MediaType,
        uploadedBy: null == uploadedBy
            ? _value.uploadedBy
            : uploadedBy // ignore: cast_nullable_to_non_nullable
                  as String,
        uploadedAt: null == uploadedAt
            ? _value.uploadedAt
            : uploadedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as MediaStatus,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaModelImpl implements _MediaModel {
  const _$MediaModelImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.url,
    required this.type,
    required this.uploadedBy,
    required this.uploadedAt,
    required this.status,
    this.rejectionReason,
  });

  factory _$MediaModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaModelImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String url;
  @override
  final MediaType type;
  @override
  final String uploadedBy;
  @override
  final DateTime uploadedAt;
  @override
  final MediaStatus status;
  @override
  final String? rejectionReason;

  @override
  String toString() {
    return 'MediaModel(id: $id, title: $title, description: $description, url: $url, type: $type, uploadedBy: $uploadedBy, uploadedAt: $uploadedAt, status: $status, rejectionReason: $rejectionReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.uploadedBy, uploadedBy) ||
                other.uploadedBy == uploadedBy) &&
            (identical(other.uploadedAt, uploadedAt) ||
                other.uploadedAt == uploadedAt) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    url,
    type,
    uploadedBy,
    uploadedAt,
    status,
    rejectionReason,
  );

  /// Create a copy of MediaModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaModelImplCopyWith<_$MediaModelImpl> get copyWith =>
      __$$MediaModelImplCopyWithImpl<_$MediaModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaModelImplToJson(this);
  }
}

abstract class _MediaModel implements MediaModel {
  const factory _MediaModel({
    required final String id,
    required final String title,
    required final String description,
    required final String url,
    required final MediaType type,
    required final String uploadedBy,
    required final DateTime uploadedAt,
    required final MediaStatus status,
    final String? rejectionReason,
  }) = _$MediaModelImpl;

  factory _MediaModel.fromJson(Map<String, dynamic> json) =
      _$MediaModelImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get url;
  @override
  MediaType get type;
  @override
  String get uploadedBy;
  @override
  DateTime get uploadedAt;
  @override
  MediaStatus get status;
  @override
  String? get rejectionReason;

  /// Create a copy of MediaModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaModelImplCopyWith<_$MediaModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
