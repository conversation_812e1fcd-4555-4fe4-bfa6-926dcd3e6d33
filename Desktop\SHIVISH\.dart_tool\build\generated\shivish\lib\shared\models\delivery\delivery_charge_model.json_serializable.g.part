// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeliveryChargeModelImpl _$$DeliveryChargeModelImplFromJson(
  Map<String, dynamic> json,
) => _$DeliveryChargeModelImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  providerType: $enumDecode(
    _$DeliveryProviderTypeEnumMap,
    json['providerType'],
  ),
  baseCharge: (json['baseCharge'] as num).toDouble(),
  perKmCharge: (json['perKmCharge'] as num).toDouble(),
  areaType: $enumDecode(_$AreaTypeEnumMap, json['areaType']),
  maxDistance: (json['maxDistance'] as num).toDouble(),
  isActive: json['isActive'] as bool,
  description: json['description'] as String?,
  additionalCharges:
      json['additionalCharges'] as Map<String, dynamic>? ?? const {},
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$DeliveryChargeModelImplToJson(
  _$DeliveryChargeModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'providerType': _$DeliveryProviderTypeEnumMap[instance.providerType]!,
  'baseCharge': instance.baseCharge,
  'perKmCharge': instance.perKmCharge,
  'areaType': _$AreaTypeEnumMap[instance.areaType]!,
  'maxDistance': instance.maxDistance,
  'isActive': instance.isActive,
  'description': instance.description,
  'additionalCharges': instance.additionalCharges,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$DeliveryProviderTypeEnumMap = {
  DeliveryProviderType.ecomExpress: 'ecom_express',
  DeliveryProviderType.localDelivery: 'local_delivery',
};

const _$AreaTypeEnumMap = {
  AreaType.metro: 'metro',
  AreaType.nonMetro: 'non_metro',
};
