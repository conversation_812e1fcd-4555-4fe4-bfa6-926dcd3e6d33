// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'connected_device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConnectedDeviceModelImpl _$$ConnectedDeviceModelImplFromJson(
  Map<String, dynamic> json,
) => _$ConnectedDeviceModelImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  type: json['type'] as String,
  lastActive: DateTime.parse(json['lastActive'] as String),
  deviceInfo: json['deviceInfo'] as String,
  isCurrentDevice: json['isCurrentDevice'] as bool,
  isAuthorized: json['isAuthorized'] as bool? ?? false,
);

Map<String, dynamic> _$$ConnectedDeviceModelImplToJson(
  _$ConnectedDeviceModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'type': instance.type,
  'lastActive': instance.lastActive.toIso8601String(),
  'deviceInfo': instance.deviceInfo,
  'isCurrentDevice': instance.isCurrentDevice,
  'isAuthorized': instance.isAuthorized,
};
