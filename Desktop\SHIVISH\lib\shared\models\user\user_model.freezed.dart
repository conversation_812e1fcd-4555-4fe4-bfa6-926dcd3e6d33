// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

UserAddress _$UserAddressFromJson(Map<String, dynamic> json) {
  return _UserAddress.fromJson(json);
}

/// @nodoc
mixin _$UserAddress {
  String get id => throw _privateConstructorUsedError;
  String get street => throw _privateConstructorUsedError;
  String get city => throw _privateConstructorUsedError;
  String get state => throw _privateConstructorUsedError;
  String get country => throw _privateConstructorUsedError;
  String get postalCode => throw _privateConstructorUsedError;
  String? get landmark => throw _privateConstructorUsedError;
  String get contactName => throw _privateConstructorUsedError;
  String get contactPhone => throw _privateConstructorUsedError;
  String? get contactEmail => throw _privateConstructorUsedError;
  bool get isDefault => throw _privateConstructorUsedError;
  bool get isBilling => throw _privateConstructorUsedError;
  bool get isShipping => throw _privateConstructorUsedError;
  double? get latitude => throw _privateConstructorUsedError;
  double? get longitude => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this UserAddress to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserAddress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserAddressCopyWith<UserAddress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserAddressCopyWith<$Res> {
  factory $UserAddressCopyWith(
    UserAddress value,
    $Res Function(UserAddress) then,
  ) = _$UserAddressCopyWithImpl<$Res, UserAddress>;
  @useResult
  $Res call({
    String id,
    String street,
    String city,
    String state,
    String country,
    String postalCode,
    String? landmark,
    String contactName,
    String contactPhone,
    String? contactEmail,
    bool isDefault,
    bool isBilling,
    bool isShipping,
    double? latitude,
    double? longitude,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$UserAddressCopyWithImpl<$Res, $Val extends UserAddress>
    implements $UserAddressCopyWith<$Res> {
  _$UserAddressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserAddress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? street = null,
    Object? city = null,
    Object? state = null,
    Object? country = null,
    Object? postalCode = null,
    Object? landmark = freezed,
    Object? contactName = null,
    Object? contactPhone = null,
    Object? contactEmail = freezed,
    Object? isDefault = null,
    Object? isBilling = null,
    Object? isShipping = null,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            street: null == street
                ? _value.street
                : street // ignore: cast_nullable_to_non_nullable
                      as String,
            city: null == city
                ? _value.city
                : city // ignore: cast_nullable_to_non_nullable
                      as String,
            state: null == state
                ? _value.state
                : state // ignore: cast_nullable_to_non_nullable
                      as String,
            country: null == country
                ? _value.country
                : country // ignore: cast_nullable_to_non_nullable
                      as String,
            postalCode: null == postalCode
                ? _value.postalCode
                : postalCode // ignore: cast_nullable_to_non_nullable
                      as String,
            landmark: freezed == landmark
                ? _value.landmark
                : landmark // ignore: cast_nullable_to_non_nullable
                      as String?,
            contactName: null == contactName
                ? _value.contactName
                : contactName // ignore: cast_nullable_to_non_nullable
                      as String,
            contactPhone: null == contactPhone
                ? _value.contactPhone
                : contactPhone // ignore: cast_nullable_to_non_nullable
                      as String,
            contactEmail: freezed == contactEmail
                ? _value.contactEmail
                : contactEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            isDefault: null == isDefault
                ? _value.isDefault
                : isDefault // ignore: cast_nullable_to_non_nullable
                      as bool,
            isBilling: null == isBilling
                ? _value.isBilling
                : isBilling // ignore: cast_nullable_to_non_nullable
                      as bool,
            isShipping: null == isShipping
                ? _value.isShipping
                : isShipping // ignore: cast_nullable_to_non_nullable
                      as bool,
            latitude: freezed == latitude
                ? _value.latitude
                : latitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            longitude: freezed == longitude
                ? _value.longitude
                : longitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserAddressImplCopyWith<$Res>
    implements $UserAddressCopyWith<$Res> {
  factory _$$UserAddressImplCopyWith(
    _$UserAddressImpl value,
    $Res Function(_$UserAddressImpl) then,
  ) = __$$UserAddressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String street,
    String city,
    String state,
    String country,
    String postalCode,
    String? landmark,
    String contactName,
    String contactPhone,
    String? contactEmail,
    bool isDefault,
    bool isBilling,
    bool isShipping,
    double? latitude,
    double? longitude,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$UserAddressImplCopyWithImpl<$Res>
    extends _$UserAddressCopyWithImpl<$Res, _$UserAddressImpl>
    implements _$$UserAddressImplCopyWith<$Res> {
  __$$UserAddressImplCopyWithImpl(
    _$UserAddressImpl _value,
    $Res Function(_$UserAddressImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserAddress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? street = null,
    Object? city = null,
    Object? state = null,
    Object? country = null,
    Object? postalCode = null,
    Object? landmark = freezed,
    Object? contactName = null,
    Object? contactPhone = null,
    Object? contactEmail = freezed,
    Object? isDefault = null,
    Object? isBilling = null,
    Object? isShipping = null,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$UserAddressImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        street: null == street
            ? _value.street
            : street // ignore: cast_nullable_to_non_nullable
                  as String,
        city: null == city
            ? _value.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String,
        state: null == state
            ? _value.state
            : state // ignore: cast_nullable_to_non_nullable
                  as String,
        country: null == country
            ? _value.country
            : country // ignore: cast_nullable_to_non_nullable
                  as String,
        postalCode: null == postalCode
            ? _value.postalCode
            : postalCode // ignore: cast_nullable_to_non_nullable
                  as String,
        landmark: freezed == landmark
            ? _value.landmark
            : landmark // ignore: cast_nullable_to_non_nullable
                  as String?,
        contactName: null == contactName
            ? _value.contactName
            : contactName // ignore: cast_nullable_to_non_nullable
                  as String,
        contactPhone: null == contactPhone
            ? _value.contactPhone
            : contactPhone // ignore: cast_nullable_to_non_nullable
                  as String,
        contactEmail: freezed == contactEmail
            ? _value.contactEmail
            : contactEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        isDefault: null == isDefault
            ? _value.isDefault
            : isDefault // ignore: cast_nullable_to_non_nullable
                  as bool,
        isBilling: null == isBilling
            ? _value.isBilling
            : isBilling // ignore: cast_nullable_to_non_nullable
                  as bool,
        isShipping: null == isShipping
            ? _value.isShipping
            : isShipping // ignore: cast_nullable_to_non_nullable
                  as bool,
        latitude: freezed == latitude
            ? _value.latitude
            : latitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        longitude: freezed == longitude
            ? _value.longitude
            : longitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserAddressImpl implements _UserAddress {
  const _$UserAddressImpl({
    required this.id,
    required this.street,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    this.landmark,
    required this.contactName,
    required this.contactPhone,
    this.contactEmail,
    this.isDefault = false,
    this.isBilling = false,
    this.isShipping = false,
    this.latitude,
    this.longitude,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$UserAddressImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserAddressImplFromJson(json);

  @override
  final String id;
  @override
  final String street;
  @override
  final String city;
  @override
  final String state;
  @override
  final String country;
  @override
  final String postalCode;
  @override
  final String? landmark;
  @override
  final String contactName;
  @override
  final String contactPhone;
  @override
  final String? contactEmail;
  @override
  @JsonKey()
  final bool isDefault;
  @override
  @JsonKey()
  final bool isBilling;
  @override
  @JsonKey()
  final bool isShipping;
  @override
  final double? latitude;
  @override
  final double? longitude;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'UserAddress(id: $id, street: $street, city: $city, state: $state, country: $country, postalCode: $postalCode, landmark: $landmark, contactName: $contactName, contactPhone: $contactPhone, contactEmail: $contactEmail, isDefault: $isDefault, isBilling: $isBilling, isShipping: $isShipping, latitude: $latitude, longitude: $longitude, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserAddressImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.landmark, landmark) ||
                other.landmark == landmark) &&
            (identical(other.contactName, contactName) ||
                other.contactName == contactName) &&
            (identical(other.contactPhone, contactPhone) ||
                other.contactPhone == contactPhone) &&
            (identical(other.contactEmail, contactEmail) ||
                other.contactEmail == contactEmail) &&
            (identical(other.isDefault, isDefault) ||
                other.isDefault == isDefault) &&
            (identical(other.isBilling, isBilling) ||
                other.isBilling == isBilling) &&
            (identical(other.isShipping, isShipping) ||
                other.isShipping == isShipping) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    street,
    city,
    state,
    country,
    postalCode,
    landmark,
    contactName,
    contactPhone,
    contactEmail,
    isDefault,
    isBilling,
    isShipping,
    latitude,
    longitude,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of UserAddress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserAddressImplCopyWith<_$UserAddressImpl> get copyWith =>
      __$$UserAddressImplCopyWithImpl<_$UserAddressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserAddressImplToJson(this);
  }
}

abstract class _UserAddress implements UserAddress {
  const factory _UserAddress({
    required final String id,
    required final String street,
    required final String city,
    required final String state,
    required final String country,
    required final String postalCode,
    final String? landmark,
    required final String contactName,
    required final String contactPhone,
    final String? contactEmail,
    final bool isDefault,
    final bool isBilling,
    final bool isShipping,
    final double? latitude,
    final double? longitude,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$UserAddressImpl;

  factory _UserAddress.fromJson(Map<String, dynamic> json) =
      _$UserAddressImpl.fromJson;

  @override
  String get id;
  @override
  String get street;
  @override
  String get city;
  @override
  String get state;
  @override
  String get country;
  @override
  String get postalCode;
  @override
  String? get landmark;
  @override
  String get contactName;
  @override
  String get contactPhone;
  @override
  String? get contactEmail;
  @override
  bool get isDefault;
  @override
  bool get isBilling;
  @override
  bool get isShipping;
  @override
  double? get latitude;
  @override
  double? get longitude;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of UserAddress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserAddressImplCopyWith<_$UserAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserDocument _$UserDocumentFromJson(Map<String, dynamic> json) {
  return _UserDocument.fromJson(json);
}

/// @nodoc
mixin _$UserDocument {
  String get id => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get number => throw _privateConstructorUsedError;
  String get fileUrl => throw _privateConstructorUsedError;
  DateTime? get expiryDate => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  String? get verifiedBy => throw _privateConstructorUsedError;
  DateTime? get verifiedAt => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this UserDocument to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserDocument
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserDocumentCopyWith<UserDocument> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserDocumentCopyWith<$Res> {
  factory $UserDocumentCopyWith(
    UserDocument value,
    $Res Function(UserDocument) then,
  ) = _$UserDocumentCopyWithImpl<$Res, UserDocument>;
  @useResult
  $Res call({
    String id,
    String type,
    String number,
    String fileUrl,
    DateTime? expiryDate,
    bool isVerified,
    String? verifiedBy,
    DateTime? verifiedAt,
    String? rejectionReason,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$UserDocumentCopyWithImpl<$Res, $Val extends UserDocument>
    implements $UserDocumentCopyWith<$Res> {
  _$UserDocumentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserDocument
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? number = null,
    Object? fileUrl = null,
    Object? expiryDate = freezed,
    Object? isVerified = null,
    Object? verifiedBy = freezed,
    Object? verifiedAt = freezed,
    Object? rejectionReason = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as String,
            number: null == number
                ? _value.number
                : number // ignore: cast_nullable_to_non_nullable
                      as String,
            fileUrl: null == fileUrl
                ? _value.fileUrl
                : fileUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            expiryDate: freezed == expiryDate
                ? _value.expiryDate
                : expiryDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            verifiedBy: freezed == verifiedBy
                ? _value.verifiedBy
                : verifiedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            verifiedAt: freezed == verifiedAt
                ? _value.verifiedAt
                : verifiedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserDocumentImplCopyWith<$Res>
    implements $UserDocumentCopyWith<$Res> {
  factory _$$UserDocumentImplCopyWith(
    _$UserDocumentImpl value,
    $Res Function(_$UserDocumentImpl) then,
  ) = __$$UserDocumentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String type,
    String number,
    String fileUrl,
    DateTime? expiryDate,
    bool isVerified,
    String? verifiedBy,
    DateTime? verifiedAt,
    String? rejectionReason,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$UserDocumentImplCopyWithImpl<$Res>
    extends _$UserDocumentCopyWithImpl<$Res, _$UserDocumentImpl>
    implements _$$UserDocumentImplCopyWith<$Res> {
  __$$UserDocumentImplCopyWithImpl(
    _$UserDocumentImpl _value,
    $Res Function(_$UserDocumentImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserDocument
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? number = null,
    Object? fileUrl = null,
    Object? expiryDate = freezed,
    Object? isVerified = null,
    Object? verifiedBy = freezed,
    Object? verifiedAt = freezed,
    Object? rejectionReason = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$UserDocumentImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as String,
        number: null == number
            ? _value.number
            : number // ignore: cast_nullable_to_non_nullable
                  as String,
        fileUrl: null == fileUrl
            ? _value.fileUrl
            : fileUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        expiryDate: freezed == expiryDate
            ? _value.expiryDate
            : expiryDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        verifiedBy: freezed == verifiedBy
            ? _value.verifiedBy
            : verifiedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        verifiedAt: freezed == verifiedAt
            ? _value.verifiedAt
            : verifiedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserDocumentImpl implements _UserDocument {
  const _$UserDocumentImpl({
    required this.id,
    required this.type,
    required this.number,
    required this.fileUrl,
    this.expiryDate,
    this.isVerified = false,
    this.verifiedBy,
    this.verifiedAt,
    this.rejectionReason,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$UserDocumentImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserDocumentImplFromJson(json);

  @override
  final String id;
  @override
  final String type;
  @override
  final String number;
  @override
  final String fileUrl;
  @override
  final DateTime? expiryDate;
  @override
  @JsonKey()
  final bool isVerified;
  @override
  final String? verifiedBy;
  @override
  final DateTime? verifiedAt;
  @override
  final String? rejectionReason;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'UserDocument(id: $id, type: $type, number: $number, fileUrl: $fileUrl, expiryDate: $expiryDate, isVerified: $isVerified, verifiedBy: $verifiedBy, verifiedAt: $verifiedAt, rejectionReason: $rejectionReason, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserDocumentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.number, number) || other.number == number) &&
            (identical(other.fileUrl, fileUrl) || other.fileUrl == fileUrl) &&
            (identical(other.expiryDate, expiryDate) ||
                other.expiryDate == expiryDate) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.verifiedBy, verifiedBy) ||
                other.verifiedBy == verifiedBy) &&
            (identical(other.verifiedAt, verifiedAt) ||
                other.verifiedAt == verifiedAt) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    type,
    number,
    fileUrl,
    expiryDate,
    isVerified,
    verifiedBy,
    verifiedAt,
    rejectionReason,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of UserDocument
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserDocumentImplCopyWith<_$UserDocumentImpl> get copyWith =>
      __$$UserDocumentImplCopyWithImpl<_$UserDocumentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserDocumentImplToJson(this);
  }
}

abstract class _UserDocument implements UserDocument {
  const factory _UserDocument({
    required final String id,
    required final String type,
    required final String number,
    required final String fileUrl,
    final DateTime? expiryDate,
    final bool isVerified,
    final String? verifiedBy,
    final DateTime? verifiedAt,
    final String? rejectionReason,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$UserDocumentImpl;

  factory _UserDocument.fromJson(Map<String, dynamic> json) =
      _$UserDocumentImpl.fromJson;

  @override
  String get id;
  @override
  String get type;
  @override
  String get number;
  @override
  String get fileUrl;
  @override
  DateTime? get expiryDate;
  @override
  bool get isVerified;
  @override
  String? get verifiedBy;
  @override
  DateTime? get verifiedAt;
  @override
  String? get rejectionReason;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of UserDocument
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserDocumentImplCopyWith<_$UserDocumentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$UserModel {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get displayName => throw _privateConstructorUsedError;
  String? get photoUrl => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  bool get emailVerified => throw _privateConstructorUsedError;
  UserRole get role => throw _privateConstructorUsedError;
  UserStatus get status => throw _privateConstructorUsedError;
  VerificationStatus get verificationStatus =>
      throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  List<UserAddress> get addresses => throw _privateConstructorUsedError;
  List<UserDocument> get documents => throw _privateConstructorUsedError;
  List<String> get fcmTokens => throw _privateConstructorUsedError;
  Map<String, bool> get notificationPreferences =>
      throw _privateConstructorUsedError;
  int get orderCount => throw _privateConstructorUsedError;
  double get ratingAverage => throw _privateConstructorUsedError;
  int get ratingCount => throw _privateConstructorUsedError;
  double get totalEarnings => throw _privateConstructorUsedError;
  String? get businessName => throw _privateConstructorUsedError;
  String? get businessType => throw _privateConstructorUsedError;
  String? get bio => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  String? get language => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  String? get taxNumber => throw _privateConstructorUsedError;
  String? get bankName => throw _privateConstructorUsedError;
  String? get bankAccountName => throw _privateConstructorUsedError;
  String? get bankAccountNumber => throw _privateConstructorUsedError;
  String? get bankRoutingNumber => throw _privateConstructorUsedError;
  List<String>? get serviceAreas => throw _privateConstructorUsedError;
  List<String>? get serviceCategories => throw _privateConstructorUsedError;
  Map<String, String>? get socialLinks => throw _privateConstructorUsedError;
  Map<String, List<String>>? get workingHours =>
      throw _privateConstructorUsedError;
  String? get approvedBy => throw _privateConstructorUsedError;
  DateTime? get approvedAt => throw _privateConstructorUsedError;
  String? get rejectedBy => throw _privateConstructorUsedError;
  DateTime? get rejectedAt => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  String? get suspendedBy => throw _privateConstructorUsedError;
  DateTime? get suspendedAt => throw _privateConstructorUsedError;
  String? get suspensionReason => throw _privateConstructorUsedError;
  DateTime? get dateOfBirth => throw _privateConstructorUsedError;
  DateTime? get lastLoginAt => throw _privateConstructorUsedError;
  DateTime? get lastActiveAt => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserModelCopyWith<UserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelCopyWith<$Res> {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) then) =
      _$UserModelCopyWithImpl<$Res, UserModel>;
  @useResult
  $Res call({
    String id,
    String email,
    String displayName,
    String? photoUrl,
    String? phoneNumber,
    bool emailVerified,
    UserRole role,
    UserStatus status,
    VerificationStatus verificationStatus,
    String firstName,
    String lastName,
    List<UserAddress> addresses,
    List<UserDocument> documents,
    List<String> fcmTokens,
    Map<String, bool> notificationPreferences,
    int orderCount,
    double ratingAverage,
    int ratingCount,
    double totalEarnings,
    String? businessName,
    String? businessType,
    String? bio,
    String? gender,
    String? language,
    String? currency,
    String? taxNumber,
    String? bankName,
    String? bankAccountName,
    String? bankAccountNumber,
    String? bankRoutingNumber,
    List<String>? serviceAreas,
    List<String>? serviceCategories,
    Map<String, String>? socialLinks,
    Map<String, List<String>>? workingHours,
    String? approvedBy,
    DateTime? approvedAt,
    String? rejectedBy,
    DateTime? rejectedAt,
    String? rejectionReason,
    String? suspendedBy,
    DateTime? suspendedAt,
    String? suspensionReason,
    DateTime? dateOfBirth,
    DateTime? lastLoginAt,
    DateTime? lastActiveAt,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$UserModelCopyWithImpl<$Res, $Val extends UserModel>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? displayName = null,
    Object? photoUrl = freezed,
    Object? phoneNumber = freezed,
    Object? emailVerified = null,
    Object? role = null,
    Object? status = null,
    Object? verificationStatus = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? addresses = null,
    Object? documents = null,
    Object? fcmTokens = null,
    Object? notificationPreferences = null,
    Object? orderCount = null,
    Object? ratingAverage = null,
    Object? ratingCount = null,
    Object? totalEarnings = null,
    Object? businessName = freezed,
    Object? businessType = freezed,
    Object? bio = freezed,
    Object? gender = freezed,
    Object? language = freezed,
    Object? currency = freezed,
    Object? taxNumber = freezed,
    Object? bankName = freezed,
    Object? bankAccountName = freezed,
    Object? bankAccountNumber = freezed,
    Object? bankRoutingNumber = freezed,
    Object? serviceAreas = freezed,
    Object? serviceCategories = freezed,
    Object? socialLinks = freezed,
    Object? workingHours = freezed,
    Object? approvedBy = freezed,
    Object? approvedAt = freezed,
    Object? rejectedBy = freezed,
    Object? rejectedAt = freezed,
    Object? rejectionReason = freezed,
    Object? suspendedBy = freezed,
    Object? suspendedAt = freezed,
    Object? suspensionReason = freezed,
    Object? dateOfBirth = freezed,
    Object? lastLoginAt = freezed,
    Object? lastActiveAt = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            displayName: null == displayName
                ? _value.displayName
                : displayName // ignore: cast_nullable_to_non_nullable
                      as String,
            photoUrl: freezed == photoUrl
                ? _value.photoUrl
                : photoUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            phoneNumber: freezed == phoneNumber
                ? _value.phoneNumber
                : phoneNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            emailVerified: null == emailVerified
                ? _value.emailVerified
                : emailVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            role: null == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as UserRole,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as UserStatus,
            verificationStatus: null == verificationStatus
                ? _value.verificationStatus
                : verificationStatus // ignore: cast_nullable_to_non_nullable
                      as VerificationStatus,
            firstName: null == firstName
                ? _value.firstName
                : firstName // ignore: cast_nullable_to_non_nullable
                      as String,
            lastName: null == lastName
                ? _value.lastName
                : lastName // ignore: cast_nullable_to_non_nullable
                      as String,
            addresses: null == addresses
                ? _value.addresses
                : addresses // ignore: cast_nullable_to_non_nullable
                      as List<UserAddress>,
            documents: null == documents
                ? _value.documents
                : documents // ignore: cast_nullable_to_non_nullable
                      as List<UserDocument>,
            fcmTokens: null == fcmTokens
                ? _value.fcmTokens
                : fcmTokens // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            notificationPreferences: null == notificationPreferences
                ? _value.notificationPreferences
                : notificationPreferences // ignore: cast_nullable_to_non_nullable
                      as Map<String, bool>,
            orderCount: null == orderCount
                ? _value.orderCount
                : orderCount // ignore: cast_nullable_to_non_nullable
                      as int,
            ratingAverage: null == ratingAverage
                ? _value.ratingAverage
                : ratingAverage // ignore: cast_nullable_to_non_nullable
                      as double,
            ratingCount: null == ratingCount
                ? _value.ratingCount
                : ratingCount // ignore: cast_nullable_to_non_nullable
                      as int,
            totalEarnings: null == totalEarnings
                ? _value.totalEarnings
                : totalEarnings // ignore: cast_nullable_to_non_nullable
                      as double,
            businessName: freezed == businessName
                ? _value.businessName
                : businessName // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessType: freezed == businessType
                ? _value.businessType
                : businessType // ignore: cast_nullable_to_non_nullable
                      as String?,
            bio: freezed == bio
                ? _value.bio
                : bio // ignore: cast_nullable_to_non_nullable
                      as String?,
            gender: freezed == gender
                ? _value.gender
                : gender // ignore: cast_nullable_to_non_nullable
                      as String?,
            language: freezed == language
                ? _value.language
                : language // ignore: cast_nullable_to_non_nullable
                      as String?,
            currency: freezed == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String?,
            taxNumber: freezed == taxNumber
                ? _value.taxNumber
                : taxNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            bankName: freezed == bankName
                ? _value.bankName
                : bankName // ignore: cast_nullable_to_non_nullable
                      as String?,
            bankAccountName: freezed == bankAccountName
                ? _value.bankAccountName
                : bankAccountName // ignore: cast_nullable_to_non_nullable
                      as String?,
            bankAccountNumber: freezed == bankAccountNumber
                ? _value.bankAccountNumber
                : bankAccountNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            bankRoutingNumber: freezed == bankRoutingNumber
                ? _value.bankRoutingNumber
                : bankRoutingNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            serviceAreas: freezed == serviceAreas
                ? _value.serviceAreas
                : serviceAreas // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            serviceCategories: freezed == serviceCategories
                ? _value.serviceCategories
                : serviceCategories // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            socialLinks: freezed == socialLinks
                ? _value.socialLinks
                : socialLinks // ignore: cast_nullable_to_non_nullable
                      as Map<String, String>?,
            workingHours: freezed == workingHours
                ? _value.workingHours
                : workingHours // ignore: cast_nullable_to_non_nullable
                      as Map<String, List<String>>?,
            approvedBy: freezed == approvedBy
                ? _value.approvedBy
                : approvedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            approvedAt: freezed == approvedAt
                ? _value.approvedAt
                : approvedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            rejectedBy: freezed == rejectedBy
                ? _value.rejectedBy
                : rejectedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectedAt: freezed == rejectedAt
                ? _value.rejectedAt
                : rejectedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            suspendedBy: freezed == suspendedBy
                ? _value.suspendedBy
                : suspendedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            suspendedAt: freezed == suspendedAt
                ? _value.suspendedAt
                : suspendedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            suspensionReason: freezed == suspensionReason
                ? _value.suspensionReason
                : suspensionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            dateOfBirth: freezed == dateOfBirth
                ? _value.dateOfBirth
                : dateOfBirth // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            lastLoginAt: freezed == lastLoginAt
                ? _value.lastLoginAt
                : lastLoginAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            lastActiveAt: freezed == lastActiveAt
                ? _value.lastActiveAt
                : lastActiveAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserModelImplCopyWith<$Res>
    implements $UserModelCopyWith<$Res> {
  factory _$$UserModelImplCopyWith(
    _$UserModelImpl value,
    $Res Function(_$UserModelImpl) then,
  ) = __$$UserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String email,
    String displayName,
    String? photoUrl,
    String? phoneNumber,
    bool emailVerified,
    UserRole role,
    UserStatus status,
    VerificationStatus verificationStatus,
    String firstName,
    String lastName,
    List<UserAddress> addresses,
    List<UserDocument> documents,
    List<String> fcmTokens,
    Map<String, bool> notificationPreferences,
    int orderCount,
    double ratingAverage,
    int ratingCount,
    double totalEarnings,
    String? businessName,
    String? businessType,
    String? bio,
    String? gender,
    String? language,
    String? currency,
    String? taxNumber,
    String? bankName,
    String? bankAccountName,
    String? bankAccountNumber,
    String? bankRoutingNumber,
    List<String>? serviceAreas,
    List<String>? serviceCategories,
    Map<String, String>? socialLinks,
    Map<String, List<String>>? workingHours,
    String? approvedBy,
    DateTime? approvedAt,
    String? rejectedBy,
    DateTime? rejectedAt,
    String? rejectionReason,
    String? suspendedBy,
    DateTime? suspendedAt,
    String? suspensionReason,
    DateTime? dateOfBirth,
    DateTime? lastLoginAt,
    DateTime? lastActiveAt,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$UserModelImplCopyWithImpl<$Res>
    extends _$UserModelCopyWithImpl<$Res, _$UserModelImpl>
    implements _$$UserModelImplCopyWith<$Res> {
  __$$UserModelImplCopyWithImpl(
    _$UserModelImpl _value,
    $Res Function(_$UserModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? displayName = null,
    Object? photoUrl = freezed,
    Object? phoneNumber = freezed,
    Object? emailVerified = null,
    Object? role = null,
    Object? status = null,
    Object? verificationStatus = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? addresses = null,
    Object? documents = null,
    Object? fcmTokens = null,
    Object? notificationPreferences = null,
    Object? orderCount = null,
    Object? ratingAverage = null,
    Object? ratingCount = null,
    Object? totalEarnings = null,
    Object? businessName = freezed,
    Object? businessType = freezed,
    Object? bio = freezed,
    Object? gender = freezed,
    Object? language = freezed,
    Object? currency = freezed,
    Object? taxNumber = freezed,
    Object? bankName = freezed,
    Object? bankAccountName = freezed,
    Object? bankAccountNumber = freezed,
    Object? bankRoutingNumber = freezed,
    Object? serviceAreas = freezed,
    Object? serviceCategories = freezed,
    Object? socialLinks = freezed,
    Object? workingHours = freezed,
    Object? approvedBy = freezed,
    Object? approvedAt = freezed,
    Object? rejectedBy = freezed,
    Object? rejectedAt = freezed,
    Object? rejectionReason = freezed,
    Object? suspendedBy = freezed,
    Object? suspendedAt = freezed,
    Object? suspensionReason = freezed,
    Object? dateOfBirth = freezed,
    Object? lastLoginAt = freezed,
    Object? lastActiveAt = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$UserModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _value.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        photoUrl: freezed == photoUrl
            ? _value.photoUrl
            : photoUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        phoneNumber: freezed == phoneNumber
            ? _value.phoneNumber
            : phoneNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        emailVerified: null == emailVerified
            ? _value.emailVerified
            : emailVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        role: null == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as UserRole,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as UserStatus,
        verificationStatus: null == verificationStatus
            ? _value.verificationStatus
            : verificationStatus // ignore: cast_nullable_to_non_nullable
                  as VerificationStatus,
        firstName: null == firstName
            ? _value.firstName
            : firstName // ignore: cast_nullable_to_non_nullable
                  as String,
        lastName: null == lastName
            ? _value.lastName
            : lastName // ignore: cast_nullable_to_non_nullable
                  as String,
        addresses: null == addresses
            ? _value._addresses
            : addresses // ignore: cast_nullable_to_non_nullable
                  as List<UserAddress>,
        documents: null == documents
            ? _value._documents
            : documents // ignore: cast_nullable_to_non_nullable
                  as List<UserDocument>,
        fcmTokens: null == fcmTokens
            ? _value._fcmTokens
            : fcmTokens // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        notificationPreferences: null == notificationPreferences
            ? _value._notificationPreferences
            : notificationPreferences // ignore: cast_nullable_to_non_nullable
                  as Map<String, bool>,
        orderCount: null == orderCount
            ? _value.orderCount
            : orderCount // ignore: cast_nullable_to_non_nullable
                  as int,
        ratingAverage: null == ratingAverage
            ? _value.ratingAverage
            : ratingAverage // ignore: cast_nullable_to_non_nullable
                  as double,
        ratingCount: null == ratingCount
            ? _value.ratingCount
            : ratingCount // ignore: cast_nullable_to_non_nullable
                  as int,
        totalEarnings: null == totalEarnings
            ? _value.totalEarnings
            : totalEarnings // ignore: cast_nullable_to_non_nullable
                  as double,
        businessName: freezed == businessName
            ? _value.businessName
            : businessName // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessType: freezed == businessType
            ? _value.businessType
            : businessType // ignore: cast_nullable_to_non_nullable
                  as String?,
        bio: freezed == bio
            ? _value.bio
            : bio // ignore: cast_nullable_to_non_nullable
                  as String?,
        gender: freezed == gender
            ? _value.gender
            : gender // ignore: cast_nullable_to_non_nullable
                  as String?,
        language: freezed == language
            ? _value.language
            : language // ignore: cast_nullable_to_non_nullable
                  as String?,
        currency: freezed == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String?,
        taxNumber: freezed == taxNumber
            ? _value.taxNumber
            : taxNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        bankName: freezed == bankName
            ? _value.bankName
            : bankName // ignore: cast_nullable_to_non_nullable
                  as String?,
        bankAccountName: freezed == bankAccountName
            ? _value.bankAccountName
            : bankAccountName // ignore: cast_nullable_to_non_nullable
                  as String?,
        bankAccountNumber: freezed == bankAccountNumber
            ? _value.bankAccountNumber
            : bankAccountNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        bankRoutingNumber: freezed == bankRoutingNumber
            ? _value.bankRoutingNumber
            : bankRoutingNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        serviceAreas: freezed == serviceAreas
            ? _value._serviceAreas
            : serviceAreas // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        serviceCategories: freezed == serviceCategories
            ? _value._serviceCategories
            : serviceCategories // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        socialLinks: freezed == socialLinks
            ? _value._socialLinks
            : socialLinks // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>?,
        workingHours: freezed == workingHours
            ? _value._workingHours
            : workingHours // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>?,
        approvedBy: freezed == approvedBy
            ? _value.approvedBy
            : approvedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        approvedAt: freezed == approvedAt
            ? _value.approvedAt
            : approvedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        rejectedBy: freezed == rejectedBy
            ? _value.rejectedBy
            : rejectedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectedAt: freezed == rejectedAt
            ? _value.rejectedAt
            : rejectedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        suspendedBy: freezed == suspendedBy
            ? _value.suspendedBy
            : suspendedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        suspendedAt: freezed == suspendedAt
            ? _value.suspendedAt
            : suspendedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        suspensionReason: freezed == suspensionReason
            ? _value.suspensionReason
            : suspensionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        dateOfBirth: freezed == dateOfBirth
            ? _value.dateOfBirth
            : dateOfBirth // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        lastLoginAt: freezed == lastLoginAt
            ? _value.lastLoginAt
            : lastLoginAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        lastActiveAt: freezed == lastActiveAt
            ? _value.lastActiveAt
            : lastActiveAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc

class _$UserModelImpl extends _UserModel {
  const _$UserModelImpl({
    required this.id,
    required this.email,
    required this.displayName,
    this.photoUrl,
    this.phoneNumber,
    this.emailVerified = false,
    this.role = UserRole.buyer,
    this.status = UserStatus.active,
    this.verificationStatus = VerificationStatus.unverified,
    this.firstName = '',
    this.lastName = '',
    final List<UserAddress> addresses = const [],
    final List<UserDocument> documents = const [],
    final List<String> fcmTokens = const [],
    final Map<String, bool> notificationPreferences = const {},
    this.orderCount = 0,
    this.ratingAverage = 0.0,
    this.ratingCount = 0,
    this.totalEarnings = 0.0,
    this.businessName,
    this.businessType,
    this.bio,
    this.gender,
    this.language,
    this.currency,
    this.taxNumber,
    this.bankName,
    this.bankAccountName,
    this.bankAccountNumber,
    this.bankRoutingNumber,
    final List<String>? serviceAreas,
    final List<String>? serviceCategories,
    final Map<String, String>? socialLinks,
    final Map<String, List<String>>? workingHours,
    this.approvedBy,
    this.approvedAt,
    this.rejectedBy,
    this.rejectedAt,
    this.rejectionReason,
    this.suspendedBy,
    this.suspendedAt,
    this.suspensionReason,
    this.dateOfBirth,
    this.lastLoginAt,
    this.lastActiveAt,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  }) : _addresses = addresses,
       _documents = documents,
       _fcmTokens = fcmTokens,
       _notificationPreferences = notificationPreferences,
       _serviceAreas = serviceAreas,
       _serviceCategories = serviceCategories,
       _socialLinks = socialLinks,
       _workingHours = workingHours,
       super._();

  @override
  final String id;
  @override
  final String email;
  @override
  final String displayName;
  @override
  final String? photoUrl;
  @override
  final String? phoneNumber;
  @override
  @JsonKey()
  final bool emailVerified;
  @override
  @JsonKey()
  final UserRole role;
  @override
  @JsonKey()
  final UserStatus status;
  @override
  @JsonKey()
  final VerificationStatus verificationStatus;
  @override
  @JsonKey()
  final String firstName;
  @override
  @JsonKey()
  final String lastName;
  final List<UserAddress> _addresses;
  @override
  @JsonKey()
  List<UserAddress> get addresses {
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_addresses);
  }

  final List<UserDocument> _documents;
  @override
  @JsonKey()
  List<UserDocument> get documents {
    if (_documents is EqualUnmodifiableListView) return _documents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_documents);
  }

  final List<String> _fcmTokens;
  @override
  @JsonKey()
  List<String> get fcmTokens {
    if (_fcmTokens is EqualUnmodifiableListView) return _fcmTokens;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fcmTokens);
  }

  final Map<String, bool> _notificationPreferences;
  @override
  @JsonKey()
  Map<String, bool> get notificationPreferences {
    if (_notificationPreferences is EqualUnmodifiableMapView)
      return _notificationPreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_notificationPreferences);
  }

  @override
  @JsonKey()
  final int orderCount;
  @override
  @JsonKey()
  final double ratingAverage;
  @override
  @JsonKey()
  final int ratingCount;
  @override
  @JsonKey()
  final double totalEarnings;
  @override
  final String? businessName;
  @override
  final String? businessType;
  @override
  final String? bio;
  @override
  final String? gender;
  @override
  final String? language;
  @override
  final String? currency;
  @override
  final String? taxNumber;
  @override
  final String? bankName;
  @override
  final String? bankAccountName;
  @override
  final String? bankAccountNumber;
  @override
  final String? bankRoutingNumber;
  final List<String>? _serviceAreas;
  @override
  List<String>? get serviceAreas {
    final value = _serviceAreas;
    if (value == null) return null;
    if (_serviceAreas is EqualUnmodifiableListView) return _serviceAreas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _serviceCategories;
  @override
  List<String>? get serviceCategories {
    final value = _serviceCategories;
    if (value == null) return null;
    if (_serviceCategories is EqualUnmodifiableListView)
      return _serviceCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final Map<String, String>? _socialLinks;
  @override
  Map<String, String>? get socialLinks {
    final value = _socialLinks;
    if (value == null) return null;
    if (_socialLinks is EqualUnmodifiableMapView) return _socialLinks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, List<String>>? _workingHours;
  @override
  Map<String, List<String>>? get workingHours {
    final value = _workingHours;
    if (value == null) return null;
    if (_workingHours is EqualUnmodifiableMapView) return _workingHours;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? approvedBy;
  @override
  final DateTime? approvedAt;
  @override
  final String? rejectedBy;
  @override
  final DateTime? rejectedAt;
  @override
  final String? rejectionReason;
  @override
  final String? suspendedBy;
  @override
  final DateTime? suspendedAt;
  @override
  final String? suspensionReason;
  @override
  final DateTime? dateOfBirth;
  @override
  final DateTime? lastLoginAt;
  @override
  final DateTime? lastActiveAt;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, displayName: $displayName, photoUrl: $photoUrl, phoneNumber: $phoneNumber, emailVerified: $emailVerified, role: $role, status: $status, verificationStatus: $verificationStatus, firstName: $firstName, lastName: $lastName, addresses: $addresses, documents: $documents, fcmTokens: $fcmTokens, notificationPreferences: $notificationPreferences, orderCount: $orderCount, ratingAverage: $ratingAverage, ratingCount: $ratingCount, totalEarnings: $totalEarnings, businessName: $businessName, businessType: $businessType, bio: $bio, gender: $gender, language: $language, currency: $currency, taxNumber: $taxNumber, bankName: $bankName, bankAccountName: $bankAccountName, bankAccountNumber: $bankAccountNumber, bankRoutingNumber: $bankRoutingNumber, serviceAreas: $serviceAreas, serviceCategories: $serviceCategories, socialLinks: $socialLinks, workingHours: $workingHours, approvedBy: $approvedBy, approvedAt: $approvedAt, rejectedBy: $rejectedBy, rejectedAt: $rejectedAt, rejectionReason: $rejectionReason, suspendedBy: $suspendedBy, suspendedAt: $suspendedAt, suspensionReason: $suspensionReason, dateOfBirth: $dateOfBirth, lastLoginAt: $lastLoginAt, lastActiveAt: $lastActiveAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.emailVerified, emailVerified) ||
                other.emailVerified == emailVerified) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.verificationStatus, verificationStatus) ||
                other.verificationStatus == verificationStatus) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            const DeepCollectionEquality().equals(
              other._addresses,
              _addresses,
            ) &&
            const DeepCollectionEquality().equals(
              other._documents,
              _documents,
            ) &&
            const DeepCollectionEquality().equals(
              other._fcmTokens,
              _fcmTokens,
            ) &&
            const DeepCollectionEquality().equals(
              other._notificationPreferences,
              _notificationPreferences,
            ) &&
            (identical(other.orderCount, orderCount) ||
                other.orderCount == orderCount) &&
            (identical(other.ratingAverage, ratingAverage) ||
                other.ratingAverage == ratingAverage) &&
            (identical(other.ratingCount, ratingCount) ||
                other.ratingCount == ratingCount) &&
            (identical(other.totalEarnings, totalEarnings) ||
                other.totalEarnings == totalEarnings) &&
            (identical(other.businessName, businessName) ||
                other.businessName == businessName) &&
            (identical(other.businessType, businessType) ||
                other.businessType == businessType) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.taxNumber, taxNumber) ||
                other.taxNumber == taxNumber) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.bankAccountName, bankAccountName) ||
                other.bankAccountName == bankAccountName) &&
            (identical(other.bankAccountNumber, bankAccountNumber) ||
                other.bankAccountNumber == bankAccountNumber) &&
            (identical(other.bankRoutingNumber, bankRoutingNumber) ||
                other.bankRoutingNumber == bankRoutingNumber) &&
            const DeepCollectionEquality().equals(
              other._serviceAreas,
              _serviceAreas,
            ) &&
            const DeepCollectionEquality().equals(
              other._serviceCategories,
              _serviceCategories,
            ) &&
            const DeepCollectionEquality().equals(
              other._socialLinks,
              _socialLinks,
            ) &&
            const DeepCollectionEquality().equals(
              other._workingHours,
              _workingHours,
            ) &&
            (identical(other.approvedBy, approvedBy) ||
                other.approvedBy == approvedBy) &&
            (identical(other.approvedAt, approvedAt) ||
                other.approvedAt == approvedAt) &&
            (identical(other.rejectedBy, rejectedBy) ||
                other.rejectedBy == rejectedBy) &&
            (identical(other.rejectedAt, rejectedAt) ||
                other.rejectedAt == rejectedAt) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.suspendedBy, suspendedBy) ||
                other.suspendedBy == suspendedBy) &&
            (identical(other.suspendedAt, suspendedAt) ||
                other.suspendedAt == suspendedAt) &&
            (identical(other.suspensionReason, suspensionReason) ||
                other.suspensionReason == suspensionReason) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.lastLoginAt, lastLoginAt) ||
                other.lastLoginAt == lastLoginAt) &&
            (identical(other.lastActiveAt, lastActiveAt) ||
                other.lastActiveAt == lastActiveAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    email,
    displayName,
    photoUrl,
    phoneNumber,
    emailVerified,
    role,
    status,
    verificationStatus,
    firstName,
    lastName,
    const DeepCollectionEquality().hash(_addresses),
    const DeepCollectionEquality().hash(_documents),
    const DeepCollectionEquality().hash(_fcmTokens),
    const DeepCollectionEquality().hash(_notificationPreferences),
    orderCount,
    ratingAverage,
    ratingCount,
    totalEarnings,
    businessName,
    businessType,
    bio,
    gender,
    language,
    currency,
    taxNumber,
    bankName,
    bankAccountName,
    bankAccountNumber,
    bankRoutingNumber,
    const DeepCollectionEquality().hash(_serviceAreas),
    const DeepCollectionEquality().hash(_serviceCategories),
    const DeepCollectionEquality().hash(_socialLinks),
    const DeepCollectionEquality().hash(_workingHours),
    approvedBy,
    approvedAt,
    rejectedBy,
    rejectedAt,
    rejectionReason,
    suspendedBy,
    suspendedAt,
    suspensionReason,
    dateOfBirth,
    lastLoginAt,
    lastActiveAt,
    createdAt,
    updatedAt,
    isDeleted,
  ]);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      __$$UserModelImplCopyWithImpl<_$UserModelImpl>(this, _$identity);
}

abstract class _UserModel extends UserModel {
  const factory _UserModel({
    required final String id,
    required final String email,
    required final String displayName,
    final String? photoUrl,
    final String? phoneNumber,
    final bool emailVerified,
    final UserRole role,
    final UserStatus status,
    final VerificationStatus verificationStatus,
    final String firstName,
    final String lastName,
    final List<UserAddress> addresses,
    final List<UserDocument> documents,
    final List<String> fcmTokens,
    final Map<String, bool> notificationPreferences,
    final int orderCount,
    final double ratingAverage,
    final int ratingCount,
    final double totalEarnings,
    final String? businessName,
    final String? businessType,
    final String? bio,
    final String? gender,
    final String? language,
    final String? currency,
    final String? taxNumber,
    final String? bankName,
    final String? bankAccountName,
    final String? bankAccountNumber,
    final String? bankRoutingNumber,
    final List<String>? serviceAreas,
    final List<String>? serviceCategories,
    final Map<String, String>? socialLinks,
    final Map<String, List<String>>? workingHours,
    final String? approvedBy,
    final DateTime? approvedAt,
    final String? rejectedBy,
    final DateTime? rejectedAt,
    final String? rejectionReason,
    final String? suspendedBy,
    final DateTime? suspendedAt,
    final String? suspensionReason,
    final DateTime? dateOfBirth,
    final DateTime? lastLoginAt,
    final DateTime? lastActiveAt,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$UserModelImpl;
  const _UserModel._() : super._();

  @override
  String get id;
  @override
  String get email;
  @override
  String get displayName;
  @override
  String? get photoUrl;
  @override
  String? get phoneNumber;
  @override
  bool get emailVerified;
  @override
  UserRole get role;
  @override
  UserStatus get status;
  @override
  VerificationStatus get verificationStatus;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  List<UserAddress> get addresses;
  @override
  List<UserDocument> get documents;
  @override
  List<String> get fcmTokens;
  @override
  Map<String, bool> get notificationPreferences;
  @override
  int get orderCount;
  @override
  double get ratingAverage;
  @override
  int get ratingCount;
  @override
  double get totalEarnings;
  @override
  String? get businessName;
  @override
  String? get businessType;
  @override
  String? get bio;
  @override
  String? get gender;
  @override
  String? get language;
  @override
  String? get currency;
  @override
  String? get taxNumber;
  @override
  String? get bankName;
  @override
  String? get bankAccountName;
  @override
  String? get bankAccountNumber;
  @override
  String? get bankRoutingNumber;
  @override
  List<String>? get serviceAreas;
  @override
  List<String>? get serviceCategories;
  @override
  Map<String, String>? get socialLinks;
  @override
  Map<String, List<String>>? get workingHours;
  @override
  String? get approvedBy;
  @override
  DateTime? get approvedAt;
  @override
  String? get rejectedBy;
  @override
  DateTime? get rejectedAt;
  @override
  String? get rejectionReason;
  @override
  String? get suspendedBy;
  @override
  DateTime? get suspendedAt;
  @override
  String? get suspensionReason;
  @override
  DateTime? get dateOfBirth;
  @override
  DateTime? get lastLoginAt;
  @override
  DateTime? get lastActiveAt;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
