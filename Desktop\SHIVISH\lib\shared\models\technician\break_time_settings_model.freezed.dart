// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'break_time_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

BreakTimeSettingsModel _$BreakTimeSettingsModelFromJson(
  Map<String, dynamic> json,
) {
  return _BreakTimeSettingsModel.fromJson(json);
}

/// @nodoc
mixin _$BreakTimeSettingsModel {
  String get lunchStartTime => throw _privateConstructorUsedError;
  String get lunchEndTime => throw _privateConstructorUsedError;
  String get teaStartTime => throw _privateConstructorUsedError;
  String get teaEndTime => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this BreakTimeSettingsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BreakTimeSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BreakTimeSettingsModelCopyWith<BreakTimeSettingsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BreakTimeSettingsModelCopyWith<$Res> {
  factory $BreakTimeSettingsModelCopyWith(
    BreakTimeSettingsModel value,
    $Res Function(BreakTimeSettingsModel) then,
  ) = _$BreakTimeSettingsModelCopyWithImpl<$Res, BreakTimeSettingsModel>;
  @useResult
  $Res call({
    String lunchStartTime,
    String lunchEndTime,
    String teaStartTime,
    String teaEndTime,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$BreakTimeSettingsModelCopyWithImpl<
  $Res,
  $Val extends BreakTimeSettingsModel
>
    implements $BreakTimeSettingsModelCopyWith<$Res> {
  _$BreakTimeSettingsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BreakTimeSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lunchStartTime = null,
    Object? lunchEndTime = null,
    Object? teaStartTime = null,
    Object? teaEndTime = null,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            lunchStartTime: null == lunchStartTime
                ? _value.lunchStartTime
                : lunchStartTime // ignore: cast_nullable_to_non_nullable
                      as String,
            lunchEndTime: null == lunchEndTime
                ? _value.lunchEndTime
                : lunchEndTime // ignore: cast_nullable_to_non_nullable
                      as String,
            teaStartTime: null == teaStartTime
                ? _value.teaStartTime
                : teaStartTime // ignore: cast_nullable_to_non_nullable
                      as String,
            teaEndTime: null == teaEndTime
                ? _value.teaEndTime
                : teaEndTime // ignore: cast_nullable_to_non_nullable
                      as String,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BreakTimeSettingsModelImplCopyWith<$Res>
    implements $BreakTimeSettingsModelCopyWith<$Res> {
  factory _$$BreakTimeSettingsModelImplCopyWith(
    _$BreakTimeSettingsModelImpl value,
    $Res Function(_$BreakTimeSettingsModelImpl) then,
  ) = __$$BreakTimeSettingsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String lunchStartTime,
    String lunchEndTime,
    String teaStartTime,
    String teaEndTime,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$BreakTimeSettingsModelImplCopyWithImpl<$Res>
    extends
        _$BreakTimeSettingsModelCopyWithImpl<$Res, _$BreakTimeSettingsModelImpl>
    implements _$$BreakTimeSettingsModelImplCopyWith<$Res> {
  __$$BreakTimeSettingsModelImplCopyWithImpl(
    _$BreakTimeSettingsModelImpl _value,
    $Res Function(_$BreakTimeSettingsModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BreakTimeSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lunchStartTime = null,
    Object? lunchEndTime = null,
    Object? teaStartTime = null,
    Object? teaEndTime = null,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$BreakTimeSettingsModelImpl(
        lunchStartTime: null == lunchStartTime
            ? _value.lunchStartTime
            : lunchStartTime // ignore: cast_nullable_to_non_nullable
                  as String,
        lunchEndTime: null == lunchEndTime
            ? _value.lunchEndTime
            : lunchEndTime // ignore: cast_nullable_to_non_nullable
                  as String,
        teaStartTime: null == teaStartTime
            ? _value.teaStartTime
            : teaStartTime // ignore: cast_nullable_to_non_nullable
                  as String,
        teaEndTime: null == teaEndTime
            ? _value.teaEndTime
            : teaEndTime // ignore: cast_nullable_to_non_nullable
                  as String,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BreakTimeSettingsModelImpl implements _BreakTimeSettingsModel {
  const _$BreakTimeSettingsModelImpl({
    required this.lunchStartTime,
    required this.lunchEndTime,
    required this.teaStartTime,
    required this.teaEndTime,
    this.updatedAt,
  });

  factory _$BreakTimeSettingsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BreakTimeSettingsModelImplFromJson(json);

  @override
  final String lunchStartTime;
  @override
  final String lunchEndTime;
  @override
  final String teaStartTime;
  @override
  final String teaEndTime;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'BreakTimeSettingsModel(lunchStartTime: $lunchStartTime, lunchEndTime: $lunchEndTime, teaStartTime: $teaStartTime, teaEndTime: $teaEndTime, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BreakTimeSettingsModelImpl &&
            (identical(other.lunchStartTime, lunchStartTime) ||
                other.lunchStartTime == lunchStartTime) &&
            (identical(other.lunchEndTime, lunchEndTime) ||
                other.lunchEndTime == lunchEndTime) &&
            (identical(other.teaStartTime, teaStartTime) ||
                other.teaStartTime == teaStartTime) &&
            (identical(other.teaEndTime, teaEndTime) ||
                other.teaEndTime == teaEndTime) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    lunchStartTime,
    lunchEndTime,
    teaStartTime,
    teaEndTime,
    updatedAt,
  );

  /// Create a copy of BreakTimeSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BreakTimeSettingsModelImplCopyWith<_$BreakTimeSettingsModelImpl>
  get copyWith =>
      __$$BreakTimeSettingsModelImplCopyWithImpl<_$BreakTimeSettingsModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$BreakTimeSettingsModelImplToJson(this);
  }
}

abstract class _BreakTimeSettingsModel implements BreakTimeSettingsModel {
  const factory _BreakTimeSettingsModel({
    required final String lunchStartTime,
    required final String lunchEndTime,
    required final String teaStartTime,
    required final String teaEndTime,
    final DateTime? updatedAt,
  }) = _$BreakTimeSettingsModelImpl;

  factory _BreakTimeSettingsModel.fromJson(Map<String, dynamic> json) =
      _$BreakTimeSettingsModelImpl.fromJson;

  @override
  String get lunchStartTime;
  @override
  String get lunchEndTime;
  @override
  String get teaStartTime;
  @override
  String get teaEndTime;
  @override
  DateTime? get updatedAt;

  /// Create a copy of BreakTimeSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BreakTimeSettingsModelImplCopyWith<_$BreakTimeSettingsModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
