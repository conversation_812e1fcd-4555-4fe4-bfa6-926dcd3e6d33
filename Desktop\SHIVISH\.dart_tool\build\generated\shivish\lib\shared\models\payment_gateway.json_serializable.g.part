// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentGatewayImpl _$$PaymentGatewayImplFromJson(
  Map<String, dynamic> json,
) => _$PaymentGatewayImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  type: json['type'] as String,
  isActive: json['isActive'] as bool,
  credentials: json['credentials'] as Map<String, dynamic>,
  transactionFee: (json['transactionFee'] as num).toDouble(),
  minimumAmount: (json['minimumAmount'] as num).toDouble(),
  maximumAmount: (json['maximumAmount'] as num).toDouble(),
  supportedCurrencies: (json['supportedCurrencies'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  supportedPaymentMethods: (json['supportedPaymentMethods'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  transactionFees: json['transactionFees'] as Map<String, dynamic>? ?? const {},
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$PaymentGatewayImplToJson(
  _$PaymentGatewayImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'type': instance.type,
  'isActive': instance.isActive,
  'credentials': instance.credentials,
  'transactionFee': instance.transactionFee,
  'minimumAmount': instance.minimumAmount,
  'maximumAmount': instance.maximumAmount,
  'supportedCurrencies': instance.supportedCurrencies,
  'supportedPaymentMethods': instance.supportedPaymentMethods,
  'transactionFees': instance.transactionFees,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
