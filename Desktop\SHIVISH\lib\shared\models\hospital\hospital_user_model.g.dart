// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hospital_user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HospitalUserModelImpl _$$HospitalUserModelImplFromJson(
  Map<String, dynamic> json,
) => _$HospitalUserModelImpl(
  id: json['id'] as String,
  hospitalId: json['hospitalId'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  phone: json['phone'] as String,
  role: $enumDecode(_$HospitalUserRoleEnumMap, json['role']),
  isApproved: json['isApproved'] as bool,
  isActive: json['isActive'] as bool,
  isFirstLogin: json['isFirstLogin'] as bool,
  profileImage: json['profileImage'] as String?,
  accessCode: json['accessCode'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
);

Map<String, dynamic> _$$HospitalUserModelImplToJson(
  _$HospitalUserModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'hospitalId': instance.hospitalId,
  'name': instance.name,
  'email': instance.email,
  'phone': instance.phone,
  'role': _$HospitalUserRoleEnumMap[instance.role]!,
  'isApproved': instance.isApproved,
  'isActive': instance.isActive,
  'isFirstLogin': instance.isFirstLogin,
  'profileImage': instance.profileImage,
  'accessCode': instance.accessCode,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
};

const _$HospitalUserRoleEnumMap = {
  HospitalUserRole.admin: 'admin',
  HospitalUserRole.receptionist: 'receptionist',
  HospitalUserRole.doctor: 'doctor',
  HospitalUserRole.labTechnician: 'lab_technician',
  HospitalUserRole.pharmacyStaff: 'pharmacy_staff',
};
