// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'technician.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TechnicianImpl _$$TechnicianImplFromJson(
  Map<String, dynamic> json,
) => _$TechnicianImpl(
  id: json['id'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
  name: json['name'] as String,
  email: json['email'] as String,
  phone: json['phone'] as String,
  profileImage: json['profileImage'] as String?,
  qualificationProof: json['qualificationProof'] as String?,
  experienceYears: (json['experienceYears'] as num).toInt(),
  specializations: (json['specializations'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  serviceAreas: (json['serviceAreas'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  availabilitySchedule: (json['availabilitySchedule'] as Map<String, dynamic>)
      .map(
        (k, e) =>
            MapEntry(k, (e as List<dynamic>).map((e) => e as String).toList()),
      ),
  certifications: (json['certifications'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
  totalReviews: (json['totalReviews'] as num?)?.toInt() ?? 0,
  isVerified: json['isVerified'] as bool? ?? false,
  isActive: json['isActive'] as bool? ?? true,
  verificationStatus: json['verificationStatus'] as String? ?? 'pending',
  verificationNotes: json['verificationNotes'] as String?,
  partsInventory: Map<String, int>.from(json['partsInventory'] as Map),
  serviceRates: (json['serviceRates'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
  toolsEquipment: (json['toolsEquipment'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  insuranceInfo: Map<String, String>.from(json['insuranceInfo'] as Map),
  emergencyContact: Map<String, String>.from(json['emergencyContact'] as Map),
  businessHours: (json['businessHours'] as Map<String, dynamic>).map(
    (k, e) =>
        MapEntry(k, (e as List<dynamic>).map((e) => e as String).toList()),
  ),
  responseTime: (json['responseTime'] as num?)?.toInt() ?? 60,
  maxDistance: (json['maxDistance'] as num?)?.toInt() ?? 50,
  bankAccount: json['bankAccount'] == null
      ? null
      : BankAccount.fromJson(json['bankAccount'] as Map<String, dynamic>),
);

Map<String, dynamic> _$$TechnicianImplToJson(_$TechnicianImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'profileImage': instance.profileImage,
      'qualificationProof': instance.qualificationProof,
      'experienceYears': instance.experienceYears,
      'specializations': instance.specializations,
      'serviceAreas': instance.serviceAreas,
      'availabilitySchedule': instance.availabilitySchedule,
      'certifications': instance.certifications,
      'rating': instance.rating,
      'totalReviews': instance.totalReviews,
      'isVerified': instance.isVerified,
      'isActive': instance.isActive,
      'verificationStatus': instance.verificationStatus,
      'verificationNotes': instance.verificationNotes,
      'partsInventory': instance.partsInventory,
      'serviceRates': instance.serviceRates,
      'toolsEquipment': instance.toolsEquipment,
      'insuranceInfo': instance.insuranceInfo,
      'emergencyContact': instance.emergencyContact,
      'businessHours': instance.businessHours,
      'responseTime': instance.responseTime,
      'maxDistance': instance.maxDistance,
      'bankAccount': instance.bankAccount?.toJson(),
    };
