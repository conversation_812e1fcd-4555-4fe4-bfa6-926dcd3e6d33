// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MedicineModelImpl _$$MedicineModelImplFromJson(Map<String, dynamic> json) =>
    _$MedicineModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      hospitalId: json['hospitalId'] as String,
      type: $enumDecode(_$MedicineTypeEnumMap, json['type']),
      category: $enumDecode(_$MedicineCategoryEnumMap, json['category']),
      manufacturer: json['manufacturer'] as String,
      composition: json['composition'] as String,
      price: (json['price'] as num).toDouble(),
      stockQuantity: (json['stockQuantity'] as num).toInt(),
      dosageForm: json['dosageForm'] as String,
      strength: json['strength'] as String,
      requiresPrescription: json['requiresPrescription'] as bool,
      isAvailable: json['isAvailable'] as bool,
      imageUrl: json['imageUrl'] as String?,
      batchNumber: json['batchNumber'] as String,
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
    );

Map<String, dynamic> _$$MedicineModelImplToJson(_$MedicineModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'hospitalId': instance.hospitalId,
      'type': _$MedicineTypeEnumMap[instance.type]!,
      'category': _$MedicineCategoryEnumMap[instance.category]!,
      'manufacturer': instance.manufacturer,
      'composition': instance.composition,
      'price': instance.price,
      'stockQuantity': instance.stockQuantity,
      'dosageForm': instance.dosageForm,
      'strength': instance.strength,
      'requiresPrescription': instance.requiresPrescription,
      'isAvailable': instance.isAvailable,
      'imageUrl': instance.imageUrl,
      'batchNumber': instance.batchNumber,
      'expiryDate': instance.expiryDate.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'deletedAt': instance.deletedAt?.toIso8601String(),
    };

const _$MedicineTypeEnumMap = {
  MedicineType.tablet: 'tablet',
  MedicineType.capsule: 'capsule',
  MedicineType.syrup: 'syrup',
  MedicineType.injection: 'injection',
  MedicineType.cream: 'cream',
  MedicineType.ointment: 'ointment',
  MedicineType.drops: 'drops',
  MedicineType.inhaler: 'inhaler',
  MedicineType.powder: 'powder',
  MedicineType.other: 'other',
};

const _$MedicineCategoryEnumMap = {
  MedicineCategory.analgesic: 'analgesic',
  MedicineCategory.antibiotic: 'antibiotic',
  MedicineCategory.antihistamine: 'antihistamine',
  MedicineCategory.antiviral: 'antiviral',
  MedicineCategory.cardiovascular: 'cardiovascular',
  MedicineCategory.gastrointestinal: 'gastrointestinal',
  MedicineCategory.respiratory: 'respiratory',
  MedicineCategory.vitamin: 'vitamin',
  MedicineCategory.other: 'other',
};
