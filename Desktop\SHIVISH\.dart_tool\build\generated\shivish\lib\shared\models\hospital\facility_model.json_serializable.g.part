// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FacilityModelImpl _$$FacilityModelImplFromJson(Map<String, dynamic> json) =>
    _$FacilityModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      hospitalId: json['hospitalId'] as String,
      icon: json['icon'] as String?,
      isAvailable: json['isAvailable'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
    );

Map<String, dynamic> _$$FacilityModelImplToJson(_$FacilityModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'hospitalId': instance.hospitalId,
      'icon': instance.icon,
      'isAvailable': instance.isAvailable,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'deletedAt': instance.deletedAt?.toIso8601String(),
    };
