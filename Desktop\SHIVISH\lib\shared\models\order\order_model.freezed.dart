// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

OrderItem _$OrderItemFromJson(Map<String, dynamic> json) {
  return _OrderItem.fromJson(json);
}

/// @nodoc
mixin _$OrderItem {
  String get id => throw _privateConstructorUsedError;
  String get productId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get productName => throw _privateConstructorUsedError;
  String? get productImage => throw _privateConstructorUsedError;

  /// Serializes this OrderItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderItemCopyWith<OrderItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderItemCopyWith<$Res> {
  factory $OrderItemCopyWith(OrderItem value, $Res Function(OrderItem) then) =
      _$OrderItemCopyWithImpl<$Res, OrderItem>;
  @useResult
  $Res call({
    String id,
    String productId,
    String name,
    double price,
    int quantity,
    String? imageUrl,
    String? description,
    String? productName,
    String? productImage,
  });
}

/// @nodoc
class _$OrderItemCopyWithImpl<$Res, $Val extends OrderItem>
    implements $OrderItemCopyWith<$Res> {
  _$OrderItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? productId = null,
    Object? name = null,
    Object? price = null,
    Object? quantity = null,
    Object? imageUrl = freezed,
    Object? description = freezed,
    Object? productName = freezed,
    Object? productImage = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            productId: null == productId
                ? _value.productId
                : productId // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            quantity: null == quantity
                ? _value.quantity
                : quantity // ignore: cast_nullable_to_non_nullable
                      as int,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            productName: freezed == productName
                ? _value.productName
                : productName // ignore: cast_nullable_to_non_nullable
                      as String?,
            productImage: freezed == productImage
                ? _value.productImage
                : productImage // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$OrderItemImplCopyWith<$Res>
    implements $OrderItemCopyWith<$Res> {
  factory _$$OrderItemImplCopyWith(
    _$OrderItemImpl value,
    $Res Function(_$OrderItemImpl) then,
  ) = __$$OrderItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String productId,
    String name,
    double price,
    int quantity,
    String? imageUrl,
    String? description,
    String? productName,
    String? productImage,
  });
}

/// @nodoc
class __$$OrderItemImplCopyWithImpl<$Res>
    extends _$OrderItemCopyWithImpl<$Res, _$OrderItemImpl>
    implements _$$OrderItemImplCopyWith<$Res> {
  __$$OrderItemImplCopyWithImpl(
    _$OrderItemImpl _value,
    $Res Function(_$OrderItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of OrderItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? productId = null,
    Object? name = null,
    Object? price = null,
    Object? quantity = null,
    Object? imageUrl = freezed,
    Object? description = freezed,
    Object? productName = freezed,
    Object? productImage = freezed,
  }) {
    return _then(
      _$OrderItemImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        productId: null == productId
            ? _value.productId
            : productId // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        quantity: null == quantity
            ? _value.quantity
            : quantity // ignore: cast_nullable_to_non_nullable
                  as int,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        productName: freezed == productName
            ? _value.productName
            : productName // ignore: cast_nullable_to_non_nullable
                  as String?,
        productImage: freezed == productImage
            ? _value.productImage
            : productImage // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderItemImpl with DiagnosticableTreeMixin implements _OrderItem {
  const _$OrderItemImpl({
    required this.id,
    required this.productId,
    required this.name,
    required this.price,
    required this.quantity,
    this.imageUrl,
    this.description,
    this.productName,
    this.productImage,
  });

  factory _$OrderItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderItemImplFromJson(json);

  @override
  final String id;
  @override
  final String productId;
  @override
  final String name;
  @override
  final double price;
  @override
  final int quantity;
  @override
  final String? imageUrl;
  @override
  final String? description;
  @override
  final String? productName;
  @override
  final String? productImage;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrderItem(id: $id, productId: $productId, name: $name, price: $price, quantity: $quantity, imageUrl: $imageUrl, description: $description, productName: $productName, productImage: $productImage)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrderItem'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('productId', productId))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('price', price))
      ..add(DiagnosticsProperty('quantity', quantity))
      ..add(DiagnosticsProperty('imageUrl', imageUrl))
      ..add(DiagnosticsProperty('description', description))
      ..add(DiagnosticsProperty('productName', productName))
      ..add(DiagnosticsProperty('productImage', productImage));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.productImage, productImage) ||
                other.productImage == productImage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    productId,
    name,
    price,
    quantity,
    imageUrl,
    description,
    productName,
    productImage,
  );

  /// Create a copy of OrderItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderItemImplCopyWith<_$OrderItemImpl> get copyWith =>
      __$$OrderItemImplCopyWithImpl<_$OrderItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderItemImplToJson(this);
  }
}

abstract class _OrderItem implements OrderItem {
  const factory _OrderItem({
    required final String id,
    required final String productId,
    required final String name,
    required final double price,
    required final int quantity,
    final String? imageUrl,
    final String? description,
    final String? productName,
    final String? productImage,
  }) = _$OrderItemImpl;

  factory _OrderItem.fromJson(Map<String, dynamic> json) =
      _$OrderItemImpl.fromJson;

  @override
  String get id;
  @override
  String get productId;
  @override
  String get name;
  @override
  double get price;
  @override
  int get quantity;
  @override
  String? get imageUrl;
  @override
  String? get description;
  @override
  String? get productName;
  @override
  String? get productImage;

  /// Create a copy of OrderItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderItemImplCopyWith<_$OrderItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderAddress _$OrderAddressFromJson(Map<String, dynamic> json) {
  return _OrderAddress.fromJson(json);
}

/// @nodoc
mixin _$OrderAddress {
  String get street => throw _privateConstructorUsedError;
  String get city => throw _privateConstructorUsedError;
  String get state => throw _privateConstructorUsedError;
  String get country => throw _privateConstructorUsedError;
  String get postalCode => throw _privateConstructorUsedError;
  String? get landmark => throw _privateConstructorUsedError;
  String get contactName => throw _privateConstructorUsedError;
  String get contactPhone => throw _privateConstructorUsedError;
  String? get contactEmail => throw _privateConstructorUsedError;
  bool get isDefault => throw _privateConstructorUsedError;
  double? get latitude => throw _privateConstructorUsedError;
  double? get longitude => throw _privateConstructorUsedError;

  /// Serializes this OrderAddress to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderAddress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderAddressCopyWith<OrderAddress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderAddressCopyWith<$Res> {
  factory $OrderAddressCopyWith(
    OrderAddress value,
    $Res Function(OrderAddress) then,
  ) = _$OrderAddressCopyWithImpl<$Res, OrderAddress>;
  @useResult
  $Res call({
    String street,
    String city,
    String state,
    String country,
    String postalCode,
    String? landmark,
    String contactName,
    String contactPhone,
    String? contactEmail,
    bool isDefault,
    double? latitude,
    double? longitude,
  });
}

/// @nodoc
class _$OrderAddressCopyWithImpl<$Res, $Val extends OrderAddress>
    implements $OrderAddressCopyWith<$Res> {
  _$OrderAddressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderAddress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? street = null,
    Object? city = null,
    Object? state = null,
    Object? country = null,
    Object? postalCode = null,
    Object? landmark = freezed,
    Object? contactName = null,
    Object? contactPhone = null,
    Object? contactEmail = freezed,
    Object? isDefault = null,
    Object? latitude = freezed,
    Object? longitude = freezed,
  }) {
    return _then(
      _value.copyWith(
            street: null == street
                ? _value.street
                : street // ignore: cast_nullable_to_non_nullable
                      as String,
            city: null == city
                ? _value.city
                : city // ignore: cast_nullable_to_non_nullable
                      as String,
            state: null == state
                ? _value.state
                : state // ignore: cast_nullable_to_non_nullable
                      as String,
            country: null == country
                ? _value.country
                : country // ignore: cast_nullable_to_non_nullable
                      as String,
            postalCode: null == postalCode
                ? _value.postalCode
                : postalCode // ignore: cast_nullable_to_non_nullable
                      as String,
            landmark: freezed == landmark
                ? _value.landmark
                : landmark // ignore: cast_nullable_to_non_nullable
                      as String?,
            contactName: null == contactName
                ? _value.contactName
                : contactName // ignore: cast_nullable_to_non_nullable
                      as String,
            contactPhone: null == contactPhone
                ? _value.contactPhone
                : contactPhone // ignore: cast_nullable_to_non_nullable
                      as String,
            contactEmail: freezed == contactEmail
                ? _value.contactEmail
                : contactEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            isDefault: null == isDefault
                ? _value.isDefault
                : isDefault // ignore: cast_nullable_to_non_nullable
                      as bool,
            latitude: freezed == latitude
                ? _value.latitude
                : latitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            longitude: freezed == longitude
                ? _value.longitude
                : longitude // ignore: cast_nullable_to_non_nullable
                      as double?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$OrderAddressImplCopyWith<$Res>
    implements $OrderAddressCopyWith<$Res> {
  factory _$$OrderAddressImplCopyWith(
    _$OrderAddressImpl value,
    $Res Function(_$OrderAddressImpl) then,
  ) = __$$OrderAddressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String street,
    String city,
    String state,
    String country,
    String postalCode,
    String? landmark,
    String contactName,
    String contactPhone,
    String? contactEmail,
    bool isDefault,
    double? latitude,
    double? longitude,
  });
}

/// @nodoc
class __$$OrderAddressImplCopyWithImpl<$Res>
    extends _$OrderAddressCopyWithImpl<$Res, _$OrderAddressImpl>
    implements _$$OrderAddressImplCopyWith<$Res> {
  __$$OrderAddressImplCopyWithImpl(
    _$OrderAddressImpl _value,
    $Res Function(_$OrderAddressImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of OrderAddress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? street = null,
    Object? city = null,
    Object? state = null,
    Object? country = null,
    Object? postalCode = null,
    Object? landmark = freezed,
    Object? contactName = null,
    Object? contactPhone = null,
    Object? contactEmail = freezed,
    Object? isDefault = null,
    Object? latitude = freezed,
    Object? longitude = freezed,
  }) {
    return _then(
      _$OrderAddressImpl(
        street: null == street
            ? _value.street
            : street // ignore: cast_nullable_to_non_nullable
                  as String,
        city: null == city
            ? _value.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String,
        state: null == state
            ? _value.state
            : state // ignore: cast_nullable_to_non_nullable
                  as String,
        country: null == country
            ? _value.country
            : country // ignore: cast_nullable_to_non_nullable
                  as String,
        postalCode: null == postalCode
            ? _value.postalCode
            : postalCode // ignore: cast_nullable_to_non_nullable
                  as String,
        landmark: freezed == landmark
            ? _value.landmark
            : landmark // ignore: cast_nullable_to_non_nullable
                  as String?,
        contactName: null == contactName
            ? _value.contactName
            : contactName // ignore: cast_nullable_to_non_nullable
                  as String,
        contactPhone: null == contactPhone
            ? _value.contactPhone
            : contactPhone // ignore: cast_nullable_to_non_nullable
                  as String,
        contactEmail: freezed == contactEmail
            ? _value.contactEmail
            : contactEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        isDefault: null == isDefault
            ? _value.isDefault
            : isDefault // ignore: cast_nullable_to_non_nullable
                  as bool,
        latitude: freezed == latitude
            ? _value.latitude
            : latitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        longitude: freezed == longitude
            ? _value.longitude
            : longitude // ignore: cast_nullable_to_non_nullable
                  as double?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderAddressImpl with DiagnosticableTreeMixin implements _OrderAddress {
  const _$OrderAddressImpl({
    required this.street,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    this.landmark,
    required this.contactName,
    required this.contactPhone,
    this.contactEmail,
    this.isDefault = false,
    this.latitude,
    this.longitude,
  });

  factory _$OrderAddressImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderAddressImplFromJson(json);

  @override
  final String street;
  @override
  final String city;
  @override
  final String state;
  @override
  final String country;
  @override
  final String postalCode;
  @override
  final String? landmark;
  @override
  final String contactName;
  @override
  final String contactPhone;
  @override
  final String? contactEmail;
  @override
  @JsonKey()
  final bool isDefault;
  @override
  final double? latitude;
  @override
  final double? longitude;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrderAddress(street: $street, city: $city, state: $state, country: $country, postalCode: $postalCode, landmark: $landmark, contactName: $contactName, contactPhone: $contactPhone, contactEmail: $contactEmail, isDefault: $isDefault, latitude: $latitude, longitude: $longitude)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrderAddress'))
      ..add(DiagnosticsProperty('street', street))
      ..add(DiagnosticsProperty('city', city))
      ..add(DiagnosticsProperty('state', state))
      ..add(DiagnosticsProperty('country', country))
      ..add(DiagnosticsProperty('postalCode', postalCode))
      ..add(DiagnosticsProperty('landmark', landmark))
      ..add(DiagnosticsProperty('contactName', contactName))
      ..add(DiagnosticsProperty('contactPhone', contactPhone))
      ..add(DiagnosticsProperty('contactEmail', contactEmail))
      ..add(DiagnosticsProperty('isDefault', isDefault))
      ..add(DiagnosticsProperty('latitude', latitude))
      ..add(DiagnosticsProperty('longitude', longitude));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderAddressImpl &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.landmark, landmark) ||
                other.landmark == landmark) &&
            (identical(other.contactName, contactName) ||
                other.contactName == contactName) &&
            (identical(other.contactPhone, contactPhone) ||
                other.contactPhone == contactPhone) &&
            (identical(other.contactEmail, contactEmail) ||
                other.contactEmail == contactEmail) &&
            (identical(other.isDefault, isDefault) ||
                other.isDefault == isDefault) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    street,
    city,
    state,
    country,
    postalCode,
    landmark,
    contactName,
    contactPhone,
    contactEmail,
    isDefault,
    latitude,
    longitude,
  );

  /// Create a copy of OrderAddress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderAddressImplCopyWith<_$OrderAddressImpl> get copyWith =>
      __$$OrderAddressImplCopyWithImpl<_$OrderAddressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderAddressImplToJson(this);
  }
}

abstract class _OrderAddress implements OrderAddress {
  const factory _OrderAddress({
    required final String street,
    required final String city,
    required final String state,
    required final String country,
    required final String postalCode,
    final String? landmark,
    required final String contactName,
    required final String contactPhone,
    final String? contactEmail,
    final bool isDefault,
    final double? latitude,
    final double? longitude,
  }) = _$OrderAddressImpl;

  factory _OrderAddress.fromJson(Map<String, dynamic> json) =
      _$OrderAddressImpl.fromJson;

  @override
  String get street;
  @override
  String get city;
  @override
  String get state;
  @override
  String get country;
  @override
  String get postalCode;
  @override
  String? get landmark;
  @override
  String get contactName;
  @override
  String get contactPhone;
  @override
  String? get contactEmail;
  @override
  bool get isDefault;
  @override
  double? get latitude;
  @override
  double? get longitude;

  /// Create a copy of OrderAddress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderAddressImplCopyWith<_$OrderAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentDetails _$PaymentDetailsFromJson(Map<String, dynamic> json) {
  return _PaymentDetails.fromJson(json);
}

/// @nodoc
mixin _$PaymentDetails {
  String get method => throw _privateConstructorUsedError;
  PaymentStatus get status => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String? get transactionId => throw _privateConstructorUsedError;
  DateTime? get paidAt => throw _privateConstructorUsedError;
  String? get cardLast4 => throw _privateConstructorUsedError;
  String? get cardBrand => throw _privateConstructorUsedError;
  String? get paymentId => throw _privateConstructorUsedError;
  DateTime? get paymentDate => throw _privateConstructorUsedError;

  /// Serializes this PaymentDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentDetailsCopyWith<PaymentDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentDetailsCopyWith<$Res> {
  factory $PaymentDetailsCopyWith(
    PaymentDetails value,
    $Res Function(PaymentDetails) then,
  ) = _$PaymentDetailsCopyWithImpl<$Res, PaymentDetails>;
  @useResult
  $Res call({
    String method,
    PaymentStatus status,
    double amount,
    String? transactionId,
    DateTime? paidAt,
    String? cardLast4,
    String? cardBrand,
    String? paymentId,
    DateTime? paymentDate,
  });
}

/// @nodoc
class _$PaymentDetailsCopyWithImpl<$Res, $Val extends PaymentDetails>
    implements $PaymentDetailsCopyWith<$Res> {
  _$PaymentDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? method = null,
    Object? status = null,
    Object? amount = null,
    Object? transactionId = freezed,
    Object? paidAt = freezed,
    Object? cardLast4 = freezed,
    Object? cardBrand = freezed,
    Object? paymentId = freezed,
    Object? paymentDate = freezed,
  }) {
    return _then(
      _value.copyWith(
            method: null == method
                ? _value.method
                : method // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as PaymentStatus,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as double,
            transactionId: freezed == transactionId
                ? _value.transactionId
                : transactionId // ignore: cast_nullable_to_non_nullable
                      as String?,
            paidAt: freezed == paidAt
                ? _value.paidAt
                : paidAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            cardLast4: freezed == cardLast4
                ? _value.cardLast4
                : cardLast4 // ignore: cast_nullable_to_non_nullable
                      as String?,
            cardBrand: freezed == cardBrand
                ? _value.cardBrand
                : cardBrand // ignore: cast_nullable_to_non_nullable
                      as String?,
            paymentId: freezed == paymentId
                ? _value.paymentId
                : paymentId // ignore: cast_nullable_to_non_nullable
                      as String?,
            paymentDate: freezed == paymentDate
                ? _value.paymentDate
                : paymentDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentDetailsImplCopyWith<$Res>
    implements $PaymentDetailsCopyWith<$Res> {
  factory _$$PaymentDetailsImplCopyWith(
    _$PaymentDetailsImpl value,
    $Res Function(_$PaymentDetailsImpl) then,
  ) = __$$PaymentDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String method,
    PaymentStatus status,
    double amount,
    String? transactionId,
    DateTime? paidAt,
    String? cardLast4,
    String? cardBrand,
    String? paymentId,
    DateTime? paymentDate,
  });
}

/// @nodoc
class __$$PaymentDetailsImplCopyWithImpl<$Res>
    extends _$PaymentDetailsCopyWithImpl<$Res, _$PaymentDetailsImpl>
    implements _$$PaymentDetailsImplCopyWith<$Res> {
  __$$PaymentDetailsImplCopyWithImpl(
    _$PaymentDetailsImpl _value,
    $Res Function(_$PaymentDetailsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? method = null,
    Object? status = null,
    Object? amount = null,
    Object? transactionId = freezed,
    Object? paidAt = freezed,
    Object? cardLast4 = freezed,
    Object? cardBrand = freezed,
    Object? paymentId = freezed,
    Object? paymentDate = freezed,
  }) {
    return _then(
      _$PaymentDetailsImpl(
        method: null == method
            ? _value.method
            : method // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as PaymentStatus,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        transactionId: freezed == transactionId
            ? _value.transactionId
            : transactionId // ignore: cast_nullable_to_non_nullable
                  as String?,
        paidAt: freezed == paidAt
            ? _value.paidAt
            : paidAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        cardLast4: freezed == cardLast4
            ? _value.cardLast4
            : cardLast4 // ignore: cast_nullable_to_non_nullable
                  as String?,
        cardBrand: freezed == cardBrand
            ? _value.cardBrand
            : cardBrand // ignore: cast_nullable_to_non_nullable
                  as String?,
        paymentId: freezed == paymentId
            ? _value.paymentId
            : paymentId // ignore: cast_nullable_to_non_nullable
                  as String?,
        paymentDate: freezed == paymentDate
            ? _value.paymentDate
            : paymentDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentDetailsImpl
    with DiagnosticableTreeMixin
    implements _PaymentDetails {
  const _$PaymentDetailsImpl({
    required this.method,
    required this.status,
    required this.amount,
    this.transactionId,
    this.paidAt,
    this.cardLast4,
    this.cardBrand,
    this.paymentId,
    this.paymentDate,
  });

  factory _$PaymentDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentDetailsImplFromJson(json);

  @override
  final String method;
  @override
  final PaymentStatus status;
  @override
  final double amount;
  @override
  final String? transactionId;
  @override
  final DateTime? paidAt;
  @override
  final String? cardLast4;
  @override
  final String? cardBrand;
  @override
  final String? paymentId;
  @override
  final DateTime? paymentDate;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PaymentDetails(method: $method, status: $status, amount: $amount, transactionId: $transactionId, paidAt: $paidAt, cardLast4: $cardLast4, cardBrand: $cardBrand, paymentId: $paymentId, paymentDate: $paymentDate)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'PaymentDetails'))
      ..add(DiagnosticsProperty('method', method))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('amount', amount))
      ..add(DiagnosticsProperty('transactionId', transactionId))
      ..add(DiagnosticsProperty('paidAt', paidAt))
      ..add(DiagnosticsProperty('cardLast4', cardLast4))
      ..add(DiagnosticsProperty('cardBrand', cardBrand))
      ..add(DiagnosticsProperty('paymentId', paymentId))
      ..add(DiagnosticsProperty('paymentDate', paymentDate));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentDetailsImpl &&
            (identical(other.method, method) || other.method == method) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.paidAt, paidAt) || other.paidAt == paidAt) &&
            (identical(other.cardLast4, cardLast4) ||
                other.cardLast4 == cardLast4) &&
            (identical(other.cardBrand, cardBrand) ||
                other.cardBrand == cardBrand) &&
            (identical(other.paymentId, paymentId) ||
                other.paymentId == paymentId) &&
            (identical(other.paymentDate, paymentDate) ||
                other.paymentDate == paymentDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    method,
    status,
    amount,
    transactionId,
    paidAt,
    cardLast4,
    cardBrand,
    paymentId,
    paymentDate,
  );

  /// Create a copy of PaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentDetailsImplCopyWith<_$PaymentDetailsImpl> get copyWith =>
      __$$PaymentDetailsImplCopyWithImpl<_$PaymentDetailsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentDetailsImplToJson(this);
  }
}

abstract class _PaymentDetails implements PaymentDetails {
  const factory _PaymentDetails({
    required final String method,
    required final PaymentStatus status,
    required final double amount,
    final String? transactionId,
    final DateTime? paidAt,
    final String? cardLast4,
    final String? cardBrand,
    final String? paymentId,
    final DateTime? paymentDate,
  }) = _$PaymentDetailsImpl;

  factory _PaymentDetails.fromJson(Map<String, dynamic> json) =
      _$PaymentDetailsImpl.fromJson;

  @override
  String get method;
  @override
  PaymentStatus get status;
  @override
  double get amount;
  @override
  String? get transactionId;
  @override
  DateTime? get paidAt;
  @override
  String? get cardLast4;
  @override
  String? get cardBrand;
  @override
  String? get paymentId;
  @override
  DateTime? get paymentDate;

  /// Create a copy of PaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentDetailsImplCopyWith<_$PaymentDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ShippingDetails _$ShippingDetailsFromJson(Map<String, dynamic> json) {
  return _ShippingDetails.fromJson(json);
}

/// @nodoc
mixin _$ShippingDetails {
  String get trackingNumber => throw _privateConstructorUsedError;
  String get provider => throw _privateConstructorUsedError;
  DeliveryProviderType get providerType => throw _privateConstructorUsedError;
  DateTime? get estimatedDeliveryDate => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get deliveryPersonId => throw _privateConstructorUsedError;
  double? get deliveryCharge => throw _privateConstructorUsedError;
  String? get deliveryRequestId => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this ShippingDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ShippingDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShippingDetailsCopyWith<ShippingDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShippingDetailsCopyWith<$Res> {
  factory $ShippingDetailsCopyWith(
    ShippingDetails value,
    $Res Function(ShippingDetails) then,
  ) = _$ShippingDetailsCopyWithImpl<$Res, ShippingDetails>;
  @useResult
  $Res call({
    String trackingNumber,
    String provider,
    DeliveryProviderType providerType,
    DateTime? estimatedDeliveryDate,
    String? notes,
    String? deliveryPersonId,
    double? deliveryCharge,
    String? deliveryRequestId,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class _$ShippingDetailsCopyWithImpl<$Res, $Val extends ShippingDetails>
    implements $ShippingDetailsCopyWith<$Res> {
  _$ShippingDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShippingDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? trackingNumber = null,
    Object? provider = null,
    Object? providerType = null,
    Object? estimatedDeliveryDate = freezed,
    Object? notes = freezed,
    Object? deliveryPersonId = freezed,
    Object? deliveryCharge = freezed,
    Object? deliveryRequestId = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            trackingNumber: null == trackingNumber
                ? _value.trackingNumber
                : trackingNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            provider: null == provider
                ? _value.provider
                : provider // ignore: cast_nullable_to_non_nullable
                      as String,
            providerType: null == providerType
                ? _value.providerType
                : providerType // ignore: cast_nullable_to_non_nullable
                      as DeliveryProviderType,
            estimatedDeliveryDate: freezed == estimatedDeliveryDate
                ? _value.estimatedDeliveryDate
                : estimatedDeliveryDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            deliveryPersonId: freezed == deliveryPersonId
                ? _value.deliveryPersonId
                : deliveryPersonId // ignore: cast_nullable_to_non_nullable
                      as String?,
            deliveryCharge: freezed == deliveryCharge
                ? _value.deliveryCharge
                : deliveryCharge // ignore: cast_nullable_to_non_nullable
                      as double?,
            deliveryRequestId: freezed == deliveryRequestId
                ? _value.deliveryRequestId
                : deliveryRequestId // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ShippingDetailsImplCopyWith<$Res>
    implements $ShippingDetailsCopyWith<$Res> {
  factory _$$ShippingDetailsImplCopyWith(
    _$ShippingDetailsImpl value,
    $Res Function(_$ShippingDetailsImpl) then,
  ) = __$$ShippingDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String trackingNumber,
    String provider,
    DeliveryProviderType providerType,
    DateTime? estimatedDeliveryDate,
    String? notes,
    String? deliveryPersonId,
    double? deliveryCharge,
    String? deliveryRequestId,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class __$$ShippingDetailsImplCopyWithImpl<$Res>
    extends _$ShippingDetailsCopyWithImpl<$Res, _$ShippingDetailsImpl>
    implements _$$ShippingDetailsImplCopyWith<$Res> {
  __$$ShippingDetailsImplCopyWithImpl(
    _$ShippingDetailsImpl _value,
    $Res Function(_$ShippingDetailsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ShippingDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? trackingNumber = null,
    Object? provider = null,
    Object? providerType = null,
    Object? estimatedDeliveryDate = freezed,
    Object? notes = freezed,
    Object? deliveryPersonId = freezed,
    Object? deliveryCharge = freezed,
    Object? deliveryRequestId = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _$ShippingDetailsImpl(
        trackingNumber: null == trackingNumber
            ? _value.trackingNumber
            : trackingNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        provider: null == provider
            ? _value.provider
            : provider // ignore: cast_nullable_to_non_nullable
                  as String,
        providerType: null == providerType
            ? _value.providerType
            : providerType // ignore: cast_nullable_to_non_nullable
                  as DeliveryProviderType,
        estimatedDeliveryDate: freezed == estimatedDeliveryDate
            ? _value.estimatedDeliveryDate
            : estimatedDeliveryDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        deliveryPersonId: freezed == deliveryPersonId
            ? _value.deliveryPersonId
            : deliveryPersonId // ignore: cast_nullable_to_non_nullable
                  as String?,
        deliveryCharge: freezed == deliveryCharge
            ? _value.deliveryCharge
            : deliveryCharge // ignore: cast_nullable_to_non_nullable
                  as double?,
        deliveryRequestId: freezed == deliveryRequestId
            ? _value.deliveryRequestId
            : deliveryRequestId // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ShippingDetailsImpl
    with DiagnosticableTreeMixin
    implements _ShippingDetails {
  const _$ShippingDetailsImpl({
    required this.trackingNumber,
    required this.provider,
    required this.providerType,
    this.estimatedDeliveryDate,
    this.notes,
    this.deliveryPersonId,
    this.deliveryCharge,
    this.deliveryRequestId,
    final Map<String, dynamic> metadata = const {},
  }) : _metadata = metadata;

  factory _$ShippingDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShippingDetailsImplFromJson(json);

  @override
  final String trackingNumber;
  @override
  final String provider;
  @override
  final DeliveryProviderType providerType;
  @override
  final DateTime? estimatedDeliveryDate;
  @override
  final String? notes;
  @override
  final String? deliveryPersonId;
  @override
  final double? deliveryCharge;
  @override
  final String? deliveryRequestId;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ShippingDetails(trackingNumber: $trackingNumber, provider: $provider, providerType: $providerType, estimatedDeliveryDate: $estimatedDeliveryDate, notes: $notes, deliveryPersonId: $deliveryPersonId, deliveryCharge: $deliveryCharge, deliveryRequestId: $deliveryRequestId, metadata: $metadata)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ShippingDetails'))
      ..add(DiagnosticsProperty('trackingNumber', trackingNumber))
      ..add(DiagnosticsProperty('provider', provider))
      ..add(DiagnosticsProperty('providerType', providerType))
      ..add(DiagnosticsProperty('estimatedDeliveryDate', estimatedDeliveryDate))
      ..add(DiagnosticsProperty('notes', notes))
      ..add(DiagnosticsProperty('deliveryPersonId', deliveryPersonId))
      ..add(DiagnosticsProperty('deliveryCharge', deliveryCharge))
      ..add(DiagnosticsProperty('deliveryRequestId', deliveryRequestId))
      ..add(DiagnosticsProperty('metadata', metadata));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShippingDetailsImpl &&
            (identical(other.trackingNumber, trackingNumber) ||
                other.trackingNumber == trackingNumber) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.providerType, providerType) ||
                other.providerType == providerType) &&
            (identical(other.estimatedDeliveryDate, estimatedDeliveryDate) ||
                other.estimatedDeliveryDate == estimatedDeliveryDate) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.deliveryPersonId, deliveryPersonId) ||
                other.deliveryPersonId == deliveryPersonId) &&
            (identical(other.deliveryCharge, deliveryCharge) ||
                other.deliveryCharge == deliveryCharge) &&
            (identical(other.deliveryRequestId, deliveryRequestId) ||
                other.deliveryRequestId == deliveryRequestId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    trackingNumber,
    provider,
    providerType,
    estimatedDeliveryDate,
    notes,
    deliveryPersonId,
    deliveryCharge,
    deliveryRequestId,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of ShippingDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShippingDetailsImplCopyWith<_$ShippingDetailsImpl> get copyWith =>
      __$$ShippingDetailsImplCopyWithImpl<_$ShippingDetailsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ShippingDetailsImplToJson(this);
  }
}

abstract class _ShippingDetails implements ShippingDetails {
  const factory _ShippingDetails({
    required final String trackingNumber,
    required final String provider,
    required final DeliveryProviderType providerType,
    final DateTime? estimatedDeliveryDate,
    final String? notes,
    final String? deliveryPersonId,
    final double? deliveryCharge,
    final String? deliveryRequestId,
    final Map<String, dynamic> metadata,
  }) = _$ShippingDetailsImpl;

  factory _ShippingDetails.fromJson(Map<String, dynamic> json) =
      _$ShippingDetailsImpl.fromJson;

  @override
  String get trackingNumber;
  @override
  String get provider;
  @override
  DeliveryProviderType get providerType;
  @override
  DateTime? get estimatedDeliveryDate;
  @override
  String? get notes;
  @override
  String? get deliveryPersonId;
  @override
  double? get deliveryCharge;
  @override
  String? get deliveryRequestId;
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of ShippingDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShippingDetailsImplCopyWith<_$ShippingDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$OrderModel {
  String get id => throw _privateConstructorUsedError;
  String get buyerId => throw _privateConstructorUsedError;
  String get sellerId => throw _privateConstructorUsedError;
  List<OrderItem> get items => throw _privateConstructorUsedError;
  double get total => throw _privateConstructorUsedError;
  OrderStatus get status => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt =>
      throw _privateConstructorUsedError; // Customer details
  String get customerName => throw _privateConstructorUsedError;
  String get customerPhone => throw _privateConstructorUsedError;
  String? get customerEmail =>
      throw _privateConstructorUsedError; // Address details
  OrderAddress get deliveryAddress =>
      throw _privateConstructorUsedError; // Payment details
  PaymentMethod get paymentMethod => throw _privateConstructorUsedError;
  PaymentDetails get paymentDetails =>
      throw _privateConstructorUsedError; // Shipping details
  ShippingDetails? get shippingDetails =>
      throw _privateConstructorUsedError; // Additional details
  String? get notes => throw _privateConstructorUsedError;
  String? get cancellationReason => throw _privateConstructorUsedError;
  List<String> get orderNotes => throw _privateConstructorUsedError;
  bool get isDeleted =>
      throw _privateConstructorUsedError; // Shopping list related fields
  String? get shoppingListId => throw _privateConstructorUsedError;
  bool get isPricingRequested => throw _privateConstructorUsedError;
  bool get isPriced => throw _privateConstructorUsedError;
  String? get orderType => throw _privateConstructorUsedError;

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderModelCopyWith<OrderModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderModelCopyWith<$Res> {
  factory $OrderModelCopyWith(
    OrderModel value,
    $Res Function(OrderModel) then,
  ) = _$OrderModelCopyWithImpl<$Res, OrderModel>;
  @useResult
  $Res call({
    String id,
    String buyerId,
    String sellerId,
    List<OrderItem> items,
    double total,
    OrderStatus status,
    DateTime createdAt,
    DateTime updatedAt,
    String customerName,
    String customerPhone,
    String? customerEmail,
    OrderAddress deliveryAddress,
    PaymentMethod paymentMethod,
    PaymentDetails paymentDetails,
    ShippingDetails? shippingDetails,
    String? notes,
    String? cancellationReason,
    List<String> orderNotes,
    bool isDeleted,
    String? shoppingListId,
    bool isPricingRequested,
    bool isPriced,
    String? orderType,
  });

  $OrderAddressCopyWith<$Res> get deliveryAddress;
  $PaymentDetailsCopyWith<$Res> get paymentDetails;
  $ShippingDetailsCopyWith<$Res>? get shippingDetails;
}

/// @nodoc
class _$OrderModelCopyWithImpl<$Res, $Val extends OrderModel>
    implements $OrderModelCopyWith<$Res> {
  _$OrderModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? buyerId = null,
    Object? sellerId = null,
    Object? items = null,
    Object? total = null,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? customerName = null,
    Object? customerPhone = null,
    Object? customerEmail = freezed,
    Object? deliveryAddress = null,
    Object? paymentMethod = null,
    Object? paymentDetails = null,
    Object? shippingDetails = freezed,
    Object? notes = freezed,
    Object? cancellationReason = freezed,
    Object? orderNotes = null,
    Object? isDeleted = null,
    Object? shoppingListId = freezed,
    Object? isPricingRequested = null,
    Object? isPriced = null,
    Object? orderType = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            buyerId: null == buyerId
                ? _value.buyerId
                : buyerId // ignore: cast_nullable_to_non_nullable
                      as String,
            sellerId: null == sellerId
                ? _value.sellerId
                : sellerId // ignore: cast_nullable_to_non_nullable
                      as String,
            items: null == items
                ? _value.items
                : items // ignore: cast_nullable_to_non_nullable
                      as List<OrderItem>,
            total: null == total
                ? _value.total
                : total // ignore: cast_nullable_to_non_nullable
                      as double,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as OrderStatus,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            customerName: null == customerName
                ? _value.customerName
                : customerName // ignore: cast_nullable_to_non_nullable
                      as String,
            customerPhone: null == customerPhone
                ? _value.customerPhone
                : customerPhone // ignore: cast_nullable_to_non_nullable
                      as String,
            customerEmail: freezed == customerEmail
                ? _value.customerEmail
                : customerEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            deliveryAddress: null == deliveryAddress
                ? _value.deliveryAddress
                : deliveryAddress // ignore: cast_nullable_to_non_nullable
                      as OrderAddress,
            paymentMethod: null == paymentMethod
                ? _value.paymentMethod
                : paymentMethod // ignore: cast_nullable_to_non_nullable
                      as PaymentMethod,
            paymentDetails: null == paymentDetails
                ? _value.paymentDetails
                : paymentDetails // ignore: cast_nullable_to_non_nullable
                      as PaymentDetails,
            shippingDetails: freezed == shippingDetails
                ? _value.shippingDetails
                : shippingDetails // ignore: cast_nullable_to_non_nullable
                      as ShippingDetails?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            cancellationReason: freezed == cancellationReason
                ? _value.cancellationReason
                : cancellationReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            orderNotes: null == orderNotes
                ? _value.orderNotes
                : orderNotes // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            shoppingListId: freezed == shoppingListId
                ? _value.shoppingListId
                : shoppingListId // ignore: cast_nullable_to_non_nullable
                      as String?,
            isPricingRequested: null == isPricingRequested
                ? _value.isPricingRequested
                : isPricingRequested // ignore: cast_nullable_to_non_nullable
                      as bool,
            isPriced: null == isPriced
                ? _value.isPriced
                : isPriced // ignore: cast_nullable_to_non_nullable
                      as bool,
            orderType: freezed == orderType
                ? _value.orderType
                : orderType // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderAddressCopyWith<$Res> get deliveryAddress {
    return $OrderAddressCopyWith<$Res>(_value.deliveryAddress, (value) {
      return _then(_value.copyWith(deliveryAddress: value) as $Val);
    });
  }

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentDetailsCopyWith<$Res> get paymentDetails {
    return $PaymentDetailsCopyWith<$Res>(_value.paymentDetails, (value) {
      return _then(_value.copyWith(paymentDetails: value) as $Val);
    });
  }

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShippingDetailsCopyWith<$Res>? get shippingDetails {
    if (_value.shippingDetails == null) {
      return null;
    }

    return $ShippingDetailsCopyWith<$Res>(_value.shippingDetails!, (value) {
      return _then(_value.copyWith(shippingDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderModelImplCopyWith<$Res>
    implements $OrderModelCopyWith<$Res> {
  factory _$$OrderModelImplCopyWith(
    _$OrderModelImpl value,
    $Res Function(_$OrderModelImpl) then,
  ) = __$$OrderModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String buyerId,
    String sellerId,
    List<OrderItem> items,
    double total,
    OrderStatus status,
    DateTime createdAt,
    DateTime updatedAt,
    String customerName,
    String customerPhone,
    String? customerEmail,
    OrderAddress deliveryAddress,
    PaymentMethod paymentMethod,
    PaymentDetails paymentDetails,
    ShippingDetails? shippingDetails,
    String? notes,
    String? cancellationReason,
    List<String> orderNotes,
    bool isDeleted,
    String? shoppingListId,
    bool isPricingRequested,
    bool isPriced,
    String? orderType,
  });

  @override
  $OrderAddressCopyWith<$Res> get deliveryAddress;
  @override
  $PaymentDetailsCopyWith<$Res> get paymentDetails;
  @override
  $ShippingDetailsCopyWith<$Res>? get shippingDetails;
}

/// @nodoc
class __$$OrderModelImplCopyWithImpl<$Res>
    extends _$OrderModelCopyWithImpl<$Res, _$OrderModelImpl>
    implements _$$OrderModelImplCopyWith<$Res> {
  __$$OrderModelImplCopyWithImpl(
    _$OrderModelImpl _value,
    $Res Function(_$OrderModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? buyerId = null,
    Object? sellerId = null,
    Object? items = null,
    Object? total = null,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? customerName = null,
    Object? customerPhone = null,
    Object? customerEmail = freezed,
    Object? deliveryAddress = null,
    Object? paymentMethod = null,
    Object? paymentDetails = null,
    Object? shippingDetails = freezed,
    Object? notes = freezed,
    Object? cancellationReason = freezed,
    Object? orderNotes = null,
    Object? isDeleted = null,
    Object? shoppingListId = freezed,
    Object? isPricingRequested = null,
    Object? isPriced = null,
    Object? orderType = freezed,
  }) {
    return _then(
      _$OrderModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        buyerId: null == buyerId
            ? _value.buyerId
            : buyerId // ignore: cast_nullable_to_non_nullable
                  as String,
        sellerId: null == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String,
        items: null == items
            ? _value._items
            : items // ignore: cast_nullable_to_non_nullable
                  as List<OrderItem>,
        total: null == total
            ? _value.total
            : total // ignore: cast_nullable_to_non_nullable
                  as double,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as OrderStatus,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        customerName: null == customerName
            ? _value.customerName
            : customerName // ignore: cast_nullable_to_non_nullable
                  as String,
        customerPhone: null == customerPhone
            ? _value.customerPhone
            : customerPhone // ignore: cast_nullable_to_non_nullable
                  as String,
        customerEmail: freezed == customerEmail
            ? _value.customerEmail
            : customerEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        deliveryAddress: null == deliveryAddress
            ? _value.deliveryAddress
            : deliveryAddress // ignore: cast_nullable_to_non_nullable
                  as OrderAddress,
        paymentMethod: null == paymentMethod
            ? _value.paymentMethod
            : paymentMethod // ignore: cast_nullable_to_non_nullable
                  as PaymentMethod,
        paymentDetails: null == paymentDetails
            ? _value.paymentDetails
            : paymentDetails // ignore: cast_nullable_to_non_nullable
                  as PaymentDetails,
        shippingDetails: freezed == shippingDetails
            ? _value.shippingDetails
            : shippingDetails // ignore: cast_nullable_to_non_nullable
                  as ShippingDetails?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        cancellationReason: freezed == cancellationReason
            ? _value.cancellationReason
            : cancellationReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        orderNotes: null == orderNotes
            ? _value._orderNotes
            : orderNotes // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        shoppingListId: freezed == shoppingListId
            ? _value.shoppingListId
            : shoppingListId // ignore: cast_nullable_to_non_nullable
                  as String?,
        isPricingRequested: null == isPricingRequested
            ? _value.isPricingRequested
            : isPricingRequested // ignore: cast_nullable_to_non_nullable
                  as bool,
        isPriced: null == isPriced
            ? _value.isPriced
            : isPriced // ignore: cast_nullable_to_non_nullable
                  as bool,
        orderType: freezed == orderType
            ? _value.orderType
            : orderType // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$OrderModelImpl extends _OrderModel with DiagnosticableTreeMixin {
  const _$OrderModelImpl({
    required this.id,
    required this.buyerId,
    required this.sellerId,
    required final List<OrderItem> items,
    required this.total,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.customerName,
    required this.customerPhone,
    this.customerEmail,
    required this.deliveryAddress,
    required this.paymentMethod,
    required this.paymentDetails,
    this.shippingDetails,
    this.notes,
    this.cancellationReason,
    final List<String> orderNotes = const [],
    this.isDeleted = false,
    this.shoppingListId,
    this.isPricingRequested = false,
    this.isPriced = false,
    this.orderType,
  }) : _items = items,
       _orderNotes = orderNotes,
       super._();

  @override
  final String id;
  @override
  final String buyerId;
  @override
  final String sellerId;
  final List<OrderItem> _items;
  @override
  List<OrderItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  final double total;
  @override
  final OrderStatus status;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  // Customer details
  @override
  final String customerName;
  @override
  final String customerPhone;
  @override
  final String? customerEmail;
  // Address details
  @override
  final OrderAddress deliveryAddress;
  // Payment details
  @override
  final PaymentMethod paymentMethod;
  @override
  final PaymentDetails paymentDetails;
  // Shipping details
  @override
  final ShippingDetails? shippingDetails;
  // Additional details
  @override
  final String? notes;
  @override
  final String? cancellationReason;
  final List<String> _orderNotes;
  @override
  @JsonKey()
  List<String> get orderNotes {
    if (_orderNotes is EqualUnmodifiableListView) return _orderNotes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orderNotes);
  }

  @override
  @JsonKey()
  final bool isDeleted;
  // Shopping list related fields
  @override
  final String? shoppingListId;
  @override
  @JsonKey()
  final bool isPricingRequested;
  @override
  @JsonKey()
  final bool isPriced;
  @override
  final String? orderType;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'OrderModel(id: $id, buyerId: $buyerId, sellerId: $sellerId, items: $items, total: $total, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, customerName: $customerName, customerPhone: $customerPhone, customerEmail: $customerEmail, deliveryAddress: $deliveryAddress, paymentMethod: $paymentMethod, paymentDetails: $paymentDetails, shippingDetails: $shippingDetails, notes: $notes, cancellationReason: $cancellationReason, orderNotes: $orderNotes, isDeleted: $isDeleted, shoppingListId: $shoppingListId, isPricingRequested: $isPricingRequested, isPriced: $isPriced, orderType: $orderType)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'OrderModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('buyerId', buyerId))
      ..add(DiagnosticsProperty('sellerId', sellerId))
      ..add(DiagnosticsProperty('items', items))
      ..add(DiagnosticsProperty('total', total))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt))
      ..add(DiagnosticsProperty('customerName', customerName))
      ..add(DiagnosticsProperty('customerPhone', customerPhone))
      ..add(DiagnosticsProperty('customerEmail', customerEmail))
      ..add(DiagnosticsProperty('deliveryAddress', deliveryAddress))
      ..add(DiagnosticsProperty('paymentMethod', paymentMethod))
      ..add(DiagnosticsProperty('paymentDetails', paymentDetails))
      ..add(DiagnosticsProperty('shippingDetails', shippingDetails))
      ..add(DiagnosticsProperty('notes', notes))
      ..add(DiagnosticsProperty('cancellationReason', cancellationReason))
      ..add(DiagnosticsProperty('orderNotes', orderNotes))
      ..add(DiagnosticsProperty('isDeleted', isDeleted))
      ..add(DiagnosticsProperty('shoppingListId', shoppingListId))
      ..add(DiagnosticsProperty('isPricingRequested', isPricingRequested))
      ..add(DiagnosticsProperty('isPriced', isPriced))
      ..add(DiagnosticsProperty('orderType', orderType));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.buyerId, buyerId) || other.buyerId == buyerId) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.customerPhone, customerPhone) ||
                other.customerPhone == customerPhone) &&
            (identical(other.customerEmail, customerEmail) ||
                other.customerEmail == customerEmail) &&
            (identical(other.deliveryAddress, deliveryAddress) ||
                other.deliveryAddress == deliveryAddress) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.paymentDetails, paymentDetails) ||
                other.paymentDetails == paymentDetails) &&
            (identical(other.shippingDetails, shippingDetails) ||
                other.shippingDetails == shippingDetails) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.cancellationReason, cancellationReason) ||
                other.cancellationReason == cancellationReason) &&
            const DeepCollectionEquality().equals(
              other._orderNotes,
              _orderNotes,
            ) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.shoppingListId, shoppingListId) ||
                other.shoppingListId == shoppingListId) &&
            (identical(other.isPricingRequested, isPricingRequested) ||
                other.isPricingRequested == isPricingRequested) &&
            (identical(other.isPriced, isPriced) ||
                other.isPriced == isPriced) &&
            (identical(other.orderType, orderType) ||
                other.orderType == orderType));
  }

  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    buyerId,
    sellerId,
    const DeepCollectionEquality().hash(_items),
    total,
    status,
    createdAt,
    updatedAt,
    customerName,
    customerPhone,
    customerEmail,
    deliveryAddress,
    paymentMethod,
    paymentDetails,
    shippingDetails,
    notes,
    cancellationReason,
    const DeepCollectionEquality().hash(_orderNotes),
    isDeleted,
    shoppingListId,
    isPricingRequested,
    isPriced,
    orderType,
  ]);

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderModelImplCopyWith<_$OrderModelImpl> get copyWith =>
      __$$OrderModelImplCopyWithImpl<_$OrderModelImpl>(this, _$identity);
}

abstract class _OrderModel extends OrderModel {
  const factory _OrderModel({
    required final String id,
    required final String buyerId,
    required final String sellerId,
    required final List<OrderItem> items,
    required final double total,
    required final OrderStatus status,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    required final String customerName,
    required final String customerPhone,
    final String? customerEmail,
    required final OrderAddress deliveryAddress,
    required final PaymentMethod paymentMethod,
    required final PaymentDetails paymentDetails,
    final ShippingDetails? shippingDetails,
    final String? notes,
    final String? cancellationReason,
    final List<String> orderNotes,
    final bool isDeleted,
    final String? shoppingListId,
    final bool isPricingRequested,
    final bool isPriced,
    final String? orderType,
  }) = _$OrderModelImpl;
  const _OrderModel._() : super._();

  @override
  String get id;
  @override
  String get buyerId;
  @override
  String get sellerId;
  @override
  List<OrderItem> get items;
  @override
  double get total;
  @override
  OrderStatus get status;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt; // Customer details
  @override
  String get customerName;
  @override
  String get customerPhone;
  @override
  String? get customerEmail; // Address details
  @override
  OrderAddress get deliveryAddress; // Payment details
  @override
  PaymentMethod get paymentMethod;
  @override
  PaymentDetails get paymentDetails; // Shipping details
  @override
  ShippingDetails? get shippingDetails; // Additional details
  @override
  String? get notes;
  @override
  String? get cancellationReason;
  @override
  List<String> get orderNotes;
  @override
  bool get isDeleted; // Shopping list related fields
  @override
  String? get shoppingListId;
  @override
  bool get isPricingRequested;
  @override
  bool get isPriced;
  @override
  String? get orderType;

  /// Create a copy of OrderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderModelImplCopyWith<_$OrderModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
