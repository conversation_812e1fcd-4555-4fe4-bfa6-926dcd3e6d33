// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hospital_staff_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HospitalStaffModelImpl _$$HospitalStaffModelImplFromJson(
  Map<String, dynamic> json,
) => _$HospitalStaffModelImpl(
  id: json['id'] as String,
  hospitalId: json['hospitalId'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  phone: json['phone'] as String,
  role: $enumDecode(_$HospitalUserRoleEnumMap, json['role']),
  accessCode: json['accessCode'] as String,
  isActive: json['isActive'] as bool,
  profileImage: json['profileImage'] as String?,
  createdBy: json['createdBy'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
);

Map<String, dynamic> _$$HospitalStaffModelImplToJson(
  _$HospitalStaffModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'hospitalId': instance.hospitalId,
  'name': instance.name,
  'email': instance.email,
  'phone': instance.phone,
  'role': _$HospitalUserRoleEnumMap[instance.role]!,
  'accessCode': instance.accessCode,
  'isActive': instance.isActive,
  'profileImage': instance.profileImage,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
};

const _$HospitalUserRoleEnumMap = {
  HospitalUserRole.admin: 'admin',
  HospitalUserRole.receptionist: 'receptionist',
  HospitalUserRole.doctor: 'doctor',
  HospitalUserRole.labTechnician: 'lab_technician',
  HospitalUserRole.pharmacyStaff: 'pharmacy_staff',
};
