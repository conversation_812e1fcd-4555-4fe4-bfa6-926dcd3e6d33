// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FileModelImpl _$$FileModelImplFromJson(Map<String, dynamic> json) =>
    _$FileModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      path: json['path'] as String,
      extension: json['extension'] as String,
      mimeType: json['mimeType'] as String,
      size: (json['size'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      isFavorite: json['isFavorite'] as bool? ?? false,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      thumbnailUrl: json['thumbnailUrl'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$FileModelImplToJson(_$FileModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'path': instance.path,
      'extension': instance.extension,
      'mimeType': instance.mimeType,
      'size': instance.size,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'isFavorite': instance.isFavorite,
      'tags': instance.tags,
      'thumbnailUrl': instance.thumbnailUrl,
      'metadata': instance.metadata,
    };

_$FileFilterImpl _$$FileFilterImplFromJson(Map<String, dynamic> json) =>
    _$FileFilterImpl(
      extensions:
          (json['extensions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      showFavorites: json['showFavorites'] as bool? ?? false,
      showDeleted: json['showDeleted'] as bool? ?? false,
      sortBy:
          $enumDecodeNullable(_$FileSortEnumMap, json['sortBy']) ??
          FileSort.nameAsc,
      searchQuery: json['searchQuery'] as String?,
    );

Map<String, dynamic> _$$FileFilterImplToJson(_$FileFilterImpl instance) =>
    <String, dynamic>{
      'extensions': instance.extensions,
      'tags': instance.tags,
      'showFavorites': instance.showFavorites,
      'showDeleted': instance.showDeleted,
      'sortBy': _$FileSortEnumMap[instance.sortBy]!,
      'searchQuery': instance.searchQuery,
    };

const _$FileSortEnumMap = {
  FileSort.nameAsc: 'nameAsc',
  FileSort.nameDesc: 'nameDesc',
  FileSort.dateAsc: 'dateAsc',
  FileSort.dateDesc: 'dateDesc',
  FileSort.sizeAsc: 'sizeAsc',
  FileSort.sizeDesc: 'sizeDesc',
};
