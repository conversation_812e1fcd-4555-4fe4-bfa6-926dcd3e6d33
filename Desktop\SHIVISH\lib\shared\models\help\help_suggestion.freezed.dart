// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'help_suggestion.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

HelpSuggestion _$HelpSuggestionFromJson(Map<String, dynamic> json) {
  return _HelpSuggestion.fromJson(json);
}

/// @nodoc
mixin _$HelpSuggestion {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  int get relevance => throw _privateConstructorUsedError;

  /// Serializes this HelpSuggestion to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HelpSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HelpSuggestionCopyWith<HelpSuggestion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpSuggestionCopyWith<$Res> {
  factory $HelpSuggestionCopyWith(
    HelpSuggestion value,
    $Res Function(HelpSuggestion) then,
  ) = _$HelpSuggestionCopyWithImpl<$Res, HelpSuggestion>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String category,
    int relevance,
  });
}

/// @nodoc
class _$HelpSuggestionCopyWithImpl<$Res, $Val extends HelpSuggestion>
    implements $HelpSuggestionCopyWith<$Res> {
  _$HelpSuggestionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? category = null,
    Object? relevance = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            category: null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                      as String,
            relevance: null == relevance
                ? _value.relevance
                : relevance // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$HelpSuggestionImplCopyWith<$Res>
    implements $HelpSuggestionCopyWith<$Res> {
  factory _$$HelpSuggestionImplCopyWith(
    _$HelpSuggestionImpl value,
    $Res Function(_$HelpSuggestionImpl) then,
  ) = __$$HelpSuggestionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String category,
    int relevance,
  });
}

/// @nodoc
class __$$HelpSuggestionImplCopyWithImpl<$Res>
    extends _$HelpSuggestionCopyWithImpl<$Res, _$HelpSuggestionImpl>
    implements _$$HelpSuggestionImplCopyWith<$Res> {
  __$$HelpSuggestionImplCopyWithImpl(
    _$HelpSuggestionImpl _value,
    $Res Function(_$HelpSuggestionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of HelpSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? category = null,
    Object? relevance = null,
  }) {
    return _then(
      _$HelpSuggestionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        category: null == category
            ? _value.category
            : category // ignore: cast_nullable_to_non_nullable
                  as String,
        relevance: null == relevance
            ? _value.relevance
            : relevance // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$HelpSuggestionImpl implements _HelpSuggestion {
  const _$HelpSuggestionImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    this.relevance = 0,
  });

  factory _$HelpSuggestionImpl.fromJson(Map<String, dynamic> json) =>
      _$$HelpSuggestionImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String category;
  @override
  @JsonKey()
  final int relevance;

  @override
  String toString() {
    return 'HelpSuggestion(id: $id, title: $title, description: $description, category: $category, relevance: $relevance)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HelpSuggestionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.relevance, relevance) ||
                other.relevance == relevance));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, title, description, category, relevance);

  /// Create a copy of HelpSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HelpSuggestionImplCopyWith<_$HelpSuggestionImpl> get copyWith =>
      __$$HelpSuggestionImplCopyWithImpl<_$HelpSuggestionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$HelpSuggestionImplToJson(this);
  }
}

abstract class _HelpSuggestion implements HelpSuggestion {
  const factory _HelpSuggestion({
    required final String id,
    required final String title,
    required final String description,
    required final String category,
    final int relevance,
  }) = _$HelpSuggestionImpl;

  factory _HelpSuggestion.fromJson(Map<String, dynamic> json) =
      _$HelpSuggestionImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get category;
  @override
  int get relevance;

  /// Create a copy of HelpSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HelpSuggestionImplCopyWith<_$HelpSuggestionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
