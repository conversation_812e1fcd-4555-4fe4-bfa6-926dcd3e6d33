// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StorageQuotaImpl _$$StorageQuotaImplFromJson(Map<String, dynamic> json) =>
    _$StorageQuotaImpl(
      total: (json['total'] as num).toInt(),
      used: (json['used'] as num).toInt(),
      remaining: (json['remaining'] as num).toInt(),
    );

Map<String, dynamic> _$$StorageQuotaImplToJson(_$StorageQuotaImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'used': instance.used,
      'remaining': instance.remaining,
    };

_$StorageAccountImpl _$$StorageAccountImplFromJson(Map<String, dynamic> json) =>
    _$StorageAccountImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      email: json['email'] as String,
      accessToken: json['accessToken'] as String,
      addedAt: DateTime.parse(json['addedAt'] as String),
      lastSyncAt: json['lastSyncAt'] == null
          ? null
          : DateTime.parse(json['lastSyncAt'] as String),
      quota: json['quota'] == null
          ? null
          : StorageQuota.fromJson(json['quota'] as Map<String, dynamic>),
      syncStatus: json['syncStatus'] as String?,
      lastSyncResult: json['lastSyncResult'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$StorageAccountImplToJson(
  _$StorageAccountImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'type': instance.type,
  'email': instance.email,
  'accessToken': instance.accessToken,
  'addedAt': instance.addedAt.toIso8601String(),
  'lastSyncAt': instance.lastSyncAt?.toIso8601String(),
  'quota': instance.quota?.toJson(),
  'syncStatus': instance.syncStatus,
  'lastSyncResult': instance.lastSyncResult,
};

_$BackupSettingsImpl _$$BackupSettingsImplFromJson(Map<String, dynamic> json) =>
    _$BackupSettingsImpl(
      encryptionEnabled: json['encryptionEnabled'] as bool,
      compressionEnabled: json['compressionEnabled'] as bool,
      validationEnabled: json['validationEnabled'] as bool,
      schedule: json['schedule'] as Map<String, dynamic>,
      retentionDays: (json['retentionDays'] as num).toInt(),
      excludedCollections: (json['excludedCollections'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      compressionConfig: json['compressionConfig'] as Map<String, dynamic>?,
      validationConfig: json['validationConfig'] as Map<String, dynamic>?,
      notificationConfig: json['notificationConfig'] as Map<String, dynamic>?,
      monitoringConfig: json['monitoringConfig'] as Map<String, dynamic>?,
      partitionConfig: json['partitionConfig'] as Map<String, dynamic>?,
      maxUsersPerPartition: (json['maxUsersPerPartition'] as num?)?.toInt(),
      autoSwitchDrives: json['autoSwitchDrives'] as bool?,
      driveUsageThreshold: (json['driveUsageThreshold'] as num?)?.toDouble(),
      currentPartitionRange: json['currentPartitionRange'] as String?,
    );

Map<String, dynamic> _$$BackupSettingsImplToJson(
  _$BackupSettingsImpl instance,
) => <String, dynamic>{
  'encryptionEnabled': instance.encryptionEnabled,
  'compressionEnabled': instance.compressionEnabled,
  'validationEnabled': instance.validationEnabled,
  'schedule': instance.schedule,
  'retentionDays': instance.retentionDays,
  'excludedCollections': instance.excludedCollections,
  'compressionConfig': instance.compressionConfig,
  'validationConfig': instance.validationConfig,
  'notificationConfig': instance.notificationConfig,
  'monitoringConfig': instance.monitoringConfig,
  'partitionConfig': instance.partitionConfig,
  'maxUsersPerPartition': instance.maxUsersPerPartition,
  'autoSwitchDrives': instance.autoSwitchDrives,
  'driveUsageThreshold': instance.driveUsageThreshold,
  'currentPartitionRange': instance.currentPartitionRange,
};

_$BackupScheduleImpl _$$BackupScheduleImplFromJson(Map<String, dynamic> json) =>
    _$BackupScheduleImpl(
      frequency: json['frequency'] as String,
      time: json['time'] as String,
      days: (json['days'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      dayOfMonth: (json['dayOfMonth'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$BackupScheduleImplToJson(
  _$BackupScheduleImpl instance,
) => <String, dynamic>{
  'frequency': instance.frequency,
  'time': instance.time,
  'days': instance.days,
  'dayOfMonth': instance.dayOfMonth,
};

_$BackupImpl _$$BackupImplFromJson(Map<String, dynamic> json) => _$BackupImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  status: json['status'] as String,
  storageAccounts: (json['storageAccounts'] as List<dynamic>)
      .map((e) => StorageAccount.fromJson(e as Map<String, dynamic>))
      .toList(),
  settings: BackupSettings.fromJson(json['settings'] as Map<String, dynamic>),
  schedule: json['schedule'] == null
      ? null
      : BackupSchedule.fromJson(json['schedule'] as Map<String, dynamic>),
  lastBackupAt: json['lastBackupAt'] == null
      ? null
      : DateTime.parse(json['lastBackupAt'] as String),
  lastRestoredAt: json['lastRestoredAt'] == null
      ? null
      : DateTime.parse(json['lastRestoredAt'] as String),
  lastValidatedAt: json['lastValidatedAt'] == null
      ? null
      : DateTime.parse(json['lastValidatedAt'] as String),
);

Map<String, dynamic> _$$BackupImplToJson(
  _$BackupImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'createdAt': instance.createdAt.toIso8601String(),
  'status': instance.status,
  'storageAccounts': instance.storageAccounts.map((e) => e.toJson()).toList(),
  'settings': instance.settings.toJson(),
  'schedule': instance.schedule?.toJson(),
  'lastBackupAt': instance.lastBackupAt?.toIso8601String(),
  'lastRestoredAt': instance.lastRestoredAt?.toIso8601String(),
  'lastValidatedAt': instance.lastValidatedAt?.toIso8601String(),
};
