// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inventory_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InventoryModelImpl _$$InventoryModelImplFromJson(Map<String, dynamic> json) =>
    _$InventoryModelImpl(
      id: json['id'] as String,
      productId: json['productId'] as String,
      sellerId: json['sellerId'] as String,
      currentStock: (json['currentStock'] as num).toInt(),
      minimumStock: (json['minimumStock'] as num).toInt(),
      maximumStock: (json['maximumStock'] as num).toInt(),
      unitPrice: (json['unitPrice'] as num).toDouble(),
      unit: json['unit'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      location: json['location'] as String?,
      batchNumber: json['batchNumber'] as String?,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
    );

Map<String, dynamic> _$$InventoryModelImplToJson(
  _$InventoryModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'productId': instance.productId,
  'sellerId': instance.sellerId,
  'currentStock': instance.currentStock,
  'minimumStock': instance.minimumStock,
  'maximumStock': instance.maximumStock,
  'unitPrice': instance.unitPrice,
  'unit': instance.unit,
  'lastUpdated': instance.lastUpdated.toIso8601String(),
  'createdAt': instance.createdAt.toIso8601String(),
  'location': instance.location,
  'batchNumber': instance.batchNumber,
  'expiryDate': instance.expiryDate?.toIso8601String(),
};
