// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'list_submission_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ListSubmissionModel _$ListSubmissionModelFromJson(Map<String, dynamic> json) {
  return _ListSubmissionModel.fromJson(json);
}

/// @nodoc
mixin _$ListSubmissionModel {
  String get id => throw _privateConstructorUsedError;
  String get buyerId => throw _privateConstructorUsedError;
  String get sellerId => throw _privateConstructorUsedError;
  ShoppingListModel get shoppingList => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this ListSubmissionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ListSubmissionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ListSubmissionModelCopyWith<ListSubmissionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListSubmissionModelCopyWith<$Res> {
  factory $ListSubmissionModelCopyWith(
    ListSubmissionModel value,
    $Res Function(ListSubmissionModel) then,
  ) = _$ListSubmissionModelCopyWithImpl<$Res, ListSubmissionModel>;
  @useResult
  $Res call({
    String id,
    String buyerId,
    String sellerId,
    ShoppingListModel shoppingList,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    Map<String, dynamic>? metadata,
  });

  $ShoppingListModelCopyWith<$Res> get shoppingList;
}

/// @nodoc
class _$ListSubmissionModelCopyWithImpl<$Res, $Val extends ListSubmissionModel>
    implements $ListSubmissionModelCopyWith<$Res> {
  _$ListSubmissionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ListSubmissionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? buyerId = null,
    Object? sellerId = null,
    Object? shoppingList = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            buyerId: null == buyerId
                ? _value.buyerId
                : buyerId // ignore: cast_nullable_to_non_nullable
                      as String,
            sellerId: null == sellerId
                ? _value.sellerId
                : sellerId // ignore: cast_nullable_to_non_nullable
                      as String,
            shoppingList: null == shoppingList
                ? _value.shoppingList
                : shoppingList // ignore: cast_nullable_to_non_nullable
                      as ShoppingListModel,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }

  /// Create a copy of ListSubmissionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShoppingListModelCopyWith<$Res> get shoppingList {
    return $ShoppingListModelCopyWith<$Res>(_value.shoppingList, (value) {
      return _then(_value.copyWith(shoppingList: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListSubmissionModelImplCopyWith<$Res>
    implements $ListSubmissionModelCopyWith<$Res> {
  factory _$$ListSubmissionModelImplCopyWith(
    _$ListSubmissionModelImpl value,
    $Res Function(_$ListSubmissionModelImpl) then,
  ) = __$$ListSubmissionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String buyerId,
    String sellerId,
    ShoppingListModel shoppingList,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    Map<String, dynamic>? metadata,
  });

  @override
  $ShoppingListModelCopyWith<$Res> get shoppingList;
}

/// @nodoc
class __$$ListSubmissionModelImplCopyWithImpl<$Res>
    extends _$ListSubmissionModelCopyWithImpl<$Res, _$ListSubmissionModelImpl>
    implements _$$ListSubmissionModelImplCopyWith<$Res> {
  __$$ListSubmissionModelImplCopyWithImpl(
    _$ListSubmissionModelImpl _value,
    $Res Function(_$ListSubmissionModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? buyerId = null,
    Object? sellerId = null,
    Object? shoppingList = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _$ListSubmissionModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        buyerId: null == buyerId
            ? _value.buyerId
            : buyerId // ignore: cast_nullable_to_non_nullable
                  as String,
        sellerId: null == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String,
        shoppingList: null == shoppingList
            ? _value.shoppingList
            : shoppingList // ignore: cast_nullable_to_non_nullable
                  as ShoppingListModel,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionModelImpl implements _ListSubmissionModel {
  const _$ListSubmissionModelImpl({
    required this.id,
    required this.buyerId,
    required this.sellerId,
    required this.shoppingList,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata;

  factory _$ListSubmissionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListSubmissionModelImplFromJson(json);

  @override
  final String id;
  @override
  final String buyerId;
  @override
  final String sellerId;
  @override
  final ShoppingListModel shoppingList;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ListSubmissionModel(id: $id, buyerId: $buyerId, sellerId: $sellerId, shoppingList: $shoppingList, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.buyerId, buyerId) || other.buyerId == buyerId) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            (identical(other.shoppingList, shoppingList) ||
                other.shoppingList == shoppingList) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    buyerId,
    sellerId,
    shoppingList,
    createdAt,
    updatedAt,
    isDeleted,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of ListSubmissionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionModelImplCopyWith<_$ListSubmissionModelImpl> get copyWith =>
      __$$ListSubmissionModelImplCopyWithImpl<_$ListSubmissionModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionModelImplToJson(this);
  }
}

abstract class _ListSubmissionModel implements ListSubmissionModel {
  const factory _ListSubmissionModel({
    required final String id,
    required final String buyerId,
    required final String sellerId,
    required final ShoppingListModel shoppingList,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    final Map<String, dynamic>? metadata,
  }) = _$ListSubmissionModelImpl;

  factory _ListSubmissionModel.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionModelImpl.fromJson;

  @override
  String get id;
  @override
  String get buyerId;
  @override
  String get sellerId;
  @override
  ShoppingListModel get shoppingList;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of ListSubmissionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionModelImplCopyWith<_$ListSubmissionModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ListSubmissionEvent _$ListSubmissionEventFromJson(Map<String, dynamic> json) {
  switch (json['runtimeType']) {
    case 'submitted':
      return ListSubmissionEventSubmitted.fromJson(json);
    case 'quoted':
      return ListSubmissionEventQuoted.fromJson(json);
    case 'counterOffered':
      return ListSubmissionEventCounterOffered.fromJson(json);
    case 'accepted':
      return ListSubmissionEventAccepted.fromJson(json);
    case 'rejected':
      return ListSubmissionEventRejected.fromJson(json);
    case 'expired':
      return ListSubmissionEventExpired.fromJson(json);
    case 'cancelled':
      return ListSubmissionEventCancelled.fromJson(json);

    default:
      throw CheckedFromJsonException(
        json,
        'runtimeType',
        'ListSubmissionEvent',
        'Invalid union type "${json['runtimeType']}"!',
      );
  }
}

/// @nodoc
mixin _$ListSubmissionEvent {
  String get submissionId => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )
    submitted,
    required TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )
    quoted,
    required TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )
    counterOffered,
    required TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )
    accepted,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    rejected,
    required TResult Function(String submissionId, DateTime timestamp) expired,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    cancelled,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult? Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult? Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult? Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult? Function(String submissionId, DateTime timestamp)? expired,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult Function(String submissionId, DateTime timestamp)? expired,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionEventSubmitted value) submitted,
    required TResult Function(ListSubmissionEventQuoted value) quoted,
    required TResult Function(ListSubmissionEventCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionEventAccepted value) accepted,
    required TResult Function(ListSubmissionEventRejected value) rejected,
    required TResult Function(ListSubmissionEventExpired value) expired,
    required TResult Function(ListSubmissionEventCancelled value) cancelled,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionEventSubmitted value)? submitted,
    TResult? Function(ListSubmissionEventQuoted value)? quoted,
    TResult? Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionEventAccepted value)? accepted,
    TResult? Function(ListSubmissionEventRejected value)? rejected,
    TResult? Function(ListSubmissionEventExpired value)? expired,
    TResult? Function(ListSubmissionEventCancelled value)? cancelled,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionEventSubmitted value)? submitted,
    TResult Function(ListSubmissionEventQuoted value)? quoted,
    TResult Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionEventAccepted value)? accepted,
    TResult Function(ListSubmissionEventRejected value)? rejected,
    TResult Function(ListSubmissionEventExpired value)? expired,
    TResult Function(ListSubmissionEventCancelled value)? cancelled,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this ListSubmissionEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ListSubmissionEventCopyWith<ListSubmissionEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListSubmissionEventCopyWith<$Res> {
  factory $ListSubmissionEventCopyWith(
    ListSubmissionEvent value,
    $Res Function(ListSubmissionEvent) then,
  ) = _$ListSubmissionEventCopyWithImpl<$Res, ListSubmissionEvent>;
  @useResult
  $Res call({String submissionId, DateTime timestamp});
}

/// @nodoc
class _$ListSubmissionEventCopyWithImpl<$Res, $Val extends ListSubmissionEvent>
    implements $ListSubmissionEventCopyWith<$Res> {
  _$ListSubmissionEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? submissionId = null, Object? timestamp = null}) {
    return _then(
      _value.copyWith(
            submissionId: null == submissionId
                ? _value.submissionId
                : submissionId // ignore: cast_nullable_to_non_nullable
                      as String,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ListSubmissionEventSubmittedImplCopyWith<$Res>
    implements $ListSubmissionEventCopyWith<$Res> {
  factory _$$ListSubmissionEventSubmittedImplCopyWith(
    _$ListSubmissionEventSubmittedImpl value,
    $Res Function(_$ListSubmissionEventSubmittedImpl) then,
  ) = __$$ListSubmissionEventSubmittedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String submissionId,
    String buyerId,
    String sellerId,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$ListSubmissionEventSubmittedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionEventCopyWithImpl<
          $Res,
          _$ListSubmissionEventSubmittedImpl
        >
    implements _$$ListSubmissionEventSubmittedImplCopyWith<$Res> {
  __$$ListSubmissionEventSubmittedImplCopyWithImpl(
    _$ListSubmissionEventSubmittedImpl _value,
    $Res Function(_$ListSubmissionEventSubmittedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? submissionId = null,
    Object? buyerId = null,
    Object? sellerId = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$ListSubmissionEventSubmittedImpl(
        submissionId: null == submissionId
            ? _value.submissionId
            : submissionId // ignore: cast_nullable_to_non_nullable
                  as String,
        buyerId: null == buyerId
            ? _value.buyerId
            : buyerId // ignore: cast_nullable_to_non_nullable
                  as String,
        sellerId: null == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionEventSubmittedImpl
    implements ListSubmissionEventSubmitted {
  const _$ListSubmissionEventSubmittedImpl({
    required this.submissionId,
    required this.buyerId,
    required this.sellerId,
    required this.timestamp,
    final String? $type,
  }) : $type = $type ?? 'submitted';

  factory _$ListSubmissionEventSubmittedImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionEventSubmittedImplFromJson(json);

  @override
  final String submissionId;
  @override
  final String buyerId;
  @override
  final String sellerId;
  @override
  final DateTime timestamp;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionEvent.submitted(submissionId: $submissionId, buyerId: $buyerId, sellerId: $sellerId, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionEventSubmittedImpl &&
            (identical(other.submissionId, submissionId) ||
                other.submissionId == submissionId) &&
            (identical(other.buyerId, buyerId) || other.buyerId == buyerId) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, submissionId, buyerId, sellerId, timestamp);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionEventSubmittedImplCopyWith<
    _$ListSubmissionEventSubmittedImpl
  >
  get copyWith =>
      __$$ListSubmissionEventSubmittedImplCopyWithImpl<
        _$ListSubmissionEventSubmittedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )
    submitted,
    required TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )
    quoted,
    required TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )
    counterOffered,
    required TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )
    accepted,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    rejected,
    required TResult Function(String submissionId, DateTime timestamp) expired,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    cancelled,
  }) {
    return submitted(submissionId, buyerId, sellerId, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult? Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult? Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult? Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult? Function(String submissionId, DateTime timestamp)? expired,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
  }) {
    return submitted?.call(submissionId, buyerId, sellerId, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult Function(String submissionId, DateTime timestamp)? expired,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
    required TResult orElse(),
  }) {
    if (submitted != null) {
      return submitted(submissionId, buyerId, sellerId, timestamp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionEventSubmitted value) submitted,
    required TResult Function(ListSubmissionEventQuoted value) quoted,
    required TResult Function(ListSubmissionEventCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionEventAccepted value) accepted,
    required TResult Function(ListSubmissionEventRejected value) rejected,
    required TResult Function(ListSubmissionEventExpired value) expired,
    required TResult Function(ListSubmissionEventCancelled value) cancelled,
  }) {
    return submitted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionEventSubmitted value)? submitted,
    TResult? Function(ListSubmissionEventQuoted value)? quoted,
    TResult? Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionEventAccepted value)? accepted,
    TResult? Function(ListSubmissionEventRejected value)? rejected,
    TResult? Function(ListSubmissionEventExpired value)? expired,
    TResult? Function(ListSubmissionEventCancelled value)? cancelled,
  }) {
    return submitted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionEventSubmitted value)? submitted,
    TResult Function(ListSubmissionEventQuoted value)? quoted,
    TResult Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionEventAccepted value)? accepted,
    TResult Function(ListSubmissionEventRejected value)? rejected,
    TResult Function(ListSubmissionEventExpired value)? expired,
    TResult Function(ListSubmissionEventCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (submitted != null) {
      return submitted(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionEventSubmittedImplToJson(this);
  }
}

abstract class ListSubmissionEventSubmitted implements ListSubmissionEvent {
  const factory ListSubmissionEventSubmitted({
    required final String submissionId,
    required final String buyerId,
    required final String sellerId,
    required final DateTime timestamp,
  }) = _$ListSubmissionEventSubmittedImpl;

  factory ListSubmissionEventSubmitted.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionEventSubmittedImpl.fromJson;

  @override
  String get submissionId;
  String get buyerId;
  String get sellerId;
  @override
  DateTime get timestamp;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionEventSubmittedImplCopyWith<
    _$ListSubmissionEventSubmittedImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionEventQuotedImplCopyWith<$Res>
    implements $ListSubmissionEventCopyWith<$Res> {
  factory _$$ListSubmissionEventQuotedImplCopyWith(
    _$ListSubmissionEventQuotedImpl value,
    $Res Function(_$ListSubmissionEventQuotedImpl) then,
  ) = __$$ListSubmissionEventQuotedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String submissionId,
    String sellerId,
    double amount,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$ListSubmissionEventQuotedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionEventCopyWithImpl<$Res, _$ListSubmissionEventQuotedImpl>
    implements _$$ListSubmissionEventQuotedImplCopyWith<$Res> {
  __$$ListSubmissionEventQuotedImplCopyWithImpl(
    _$ListSubmissionEventQuotedImpl _value,
    $Res Function(_$ListSubmissionEventQuotedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? submissionId = null,
    Object? sellerId = null,
    Object? amount = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$ListSubmissionEventQuotedImpl(
        submissionId: null == submissionId
            ? _value.submissionId
            : submissionId // ignore: cast_nullable_to_non_nullable
                  as String,
        sellerId: null == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionEventQuotedImpl implements ListSubmissionEventQuoted {
  const _$ListSubmissionEventQuotedImpl({
    required this.submissionId,
    required this.sellerId,
    required this.amount,
    required this.timestamp,
    final String? $type,
  }) : $type = $type ?? 'quoted';

  factory _$ListSubmissionEventQuotedImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListSubmissionEventQuotedImplFromJson(json);

  @override
  final String submissionId;
  @override
  final String sellerId;
  @override
  final double amount;
  @override
  final DateTime timestamp;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionEvent.quoted(submissionId: $submissionId, sellerId: $sellerId, amount: $amount, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionEventQuotedImpl &&
            (identical(other.submissionId, submissionId) ||
                other.submissionId == submissionId) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, submissionId, sellerId, amount, timestamp);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionEventQuotedImplCopyWith<_$ListSubmissionEventQuotedImpl>
  get copyWith =>
      __$$ListSubmissionEventQuotedImplCopyWithImpl<
        _$ListSubmissionEventQuotedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )
    submitted,
    required TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )
    quoted,
    required TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )
    counterOffered,
    required TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )
    accepted,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    rejected,
    required TResult Function(String submissionId, DateTime timestamp) expired,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    cancelled,
  }) {
    return quoted(submissionId, sellerId, amount, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult? Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult? Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult? Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult? Function(String submissionId, DateTime timestamp)? expired,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
  }) {
    return quoted?.call(submissionId, sellerId, amount, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult Function(String submissionId, DateTime timestamp)? expired,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
    required TResult orElse(),
  }) {
    if (quoted != null) {
      return quoted(submissionId, sellerId, amount, timestamp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionEventSubmitted value) submitted,
    required TResult Function(ListSubmissionEventQuoted value) quoted,
    required TResult Function(ListSubmissionEventCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionEventAccepted value) accepted,
    required TResult Function(ListSubmissionEventRejected value) rejected,
    required TResult Function(ListSubmissionEventExpired value) expired,
    required TResult Function(ListSubmissionEventCancelled value) cancelled,
  }) {
    return quoted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionEventSubmitted value)? submitted,
    TResult? Function(ListSubmissionEventQuoted value)? quoted,
    TResult? Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionEventAccepted value)? accepted,
    TResult? Function(ListSubmissionEventRejected value)? rejected,
    TResult? Function(ListSubmissionEventExpired value)? expired,
    TResult? Function(ListSubmissionEventCancelled value)? cancelled,
  }) {
    return quoted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionEventSubmitted value)? submitted,
    TResult Function(ListSubmissionEventQuoted value)? quoted,
    TResult Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionEventAccepted value)? accepted,
    TResult Function(ListSubmissionEventRejected value)? rejected,
    TResult Function(ListSubmissionEventExpired value)? expired,
    TResult Function(ListSubmissionEventCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (quoted != null) {
      return quoted(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionEventQuotedImplToJson(this);
  }
}

abstract class ListSubmissionEventQuoted implements ListSubmissionEvent {
  const factory ListSubmissionEventQuoted({
    required final String submissionId,
    required final String sellerId,
    required final double amount,
    required final DateTime timestamp,
  }) = _$ListSubmissionEventQuotedImpl;

  factory ListSubmissionEventQuoted.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionEventQuotedImpl.fromJson;

  @override
  String get submissionId;
  String get sellerId;
  double get amount;
  @override
  DateTime get timestamp;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionEventQuotedImplCopyWith<_$ListSubmissionEventQuotedImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionEventCounterOfferedImplCopyWith<$Res>
    implements $ListSubmissionEventCopyWith<$Res> {
  factory _$$ListSubmissionEventCounterOfferedImplCopyWith(
    _$ListSubmissionEventCounterOfferedImpl value,
    $Res Function(_$ListSubmissionEventCounterOfferedImpl) then,
  ) = __$$ListSubmissionEventCounterOfferedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String submissionId,
    String buyerId,
    double amount,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$ListSubmissionEventCounterOfferedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionEventCopyWithImpl<
          $Res,
          _$ListSubmissionEventCounterOfferedImpl
        >
    implements _$$ListSubmissionEventCounterOfferedImplCopyWith<$Res> {
  __$$ListSubmissionEventCounterOfferedImplCopyWithImpl(
    _$ListSubmissionEventCounterOfferedImpl _value,
    $Res Function(_$ListSubmissionEventCounterOfferedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? submissionId = null,
    Object? buyerId = null,
    Object? amount = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$ListSubmissionEventCounterOfferedImpl(
        submissionId: null == submissionId
            ? _value.submissionId
            : submissionId // ignore: cast_nullable_to_non_nullable
                  as String,
        buyerId: null == buyerId
            ? _value.buyerId
            : buyerId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionEventCounterOfferedImpl
    implements ListSubmissionEventCounterOffered {
  const _$ListSubmissionEventCounterOfferedImpl({
    required this.submissionId,
    required this.buyerId,
    required this.amount,
    required this.timestamp,
    final String? $type,
  }) : $type = $type ?? 'counterOffered';

  factory _$ListSubmissionEventCounterOfferedImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionEventCounterOfferedImplFromJson(json);

  @override
  final String submissionId;
  @override
  final String buyerId;
  @override
  final double amount;
  @override
  final DateTime timestamp;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionEvent.counterOffered(submissionId: $submissionId, buyerId: $buyerId, amount: $amount, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionEventCounterOfferedImpl &&
            (identical(other.submissionId, submissionId) ||
                other.submissionId == submissionId) &&
            (identical(other.buyerId, buyerId) || other.buyerId == buyerId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, submissionId, buyerId, amount, timestamp);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionEventCounterOfferedImplCopyWith<
    _$ListSubmissionEventCounterOfferedImpl
  >
  get copyWith =>
      __$$ListSubmissionEventCounterOfferedImplCopyWithImpl<
        _$ListSubmissionEventCounterOfferedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )
    submitted,
    required TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )
    quoted,
    required TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )
    counterOffered,
    required TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )
    accepted,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    rejected,
    required TResult Function(String submissionId, DateTime timestamp) expired,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    cancelled,
  }) {
    return counterOffered(submissionId, buyerId, amount, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult? Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult? Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult? Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult? Function(String submissionId, DateTime timestamp)? expired,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
  }) {
    return counterOffered?.call(submissionId, buyerId, amount, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult Function(String submissionId, DateTime timestamp)? expired,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
    required TResult orElse(),
  }) {
    if (counterOffered != null) {
      return counterOffered(submissionId, buyerId, amount, timestamp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionEventSubmitted value) submitted,
    required TResult Function(ListSubmissionEventQuoted value) quoted,
    required TResult Function(ListSubmissionEventCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionEventAccepted value) accepted,
    required TResult Function(ListSubmissionEventRejected value) rejected,
    required TResult Function(ListSubmissionEventExpired value) expired,
    required TResult Function(ListSubmissionEventCancelled value) cancelled,
  }) {
    return counterOffered(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionEventSubmitted value)? submitted,
    TResult? Function(ListSubmissionEventQuoted value)? quoted,
    TResult? Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionEventAccepted value)? accepted,
    TResult? Function(ListSubmissionEventRejected value)? rejected,
    TResult? Function(ListSubmissionEventExpired value)? expired,
    TResult? Function(ListSubmissionEventCancelled value)? cancelled,
  }) {
    return counterOffered?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionEventSubmitted value)? submitted,
    TResult Function(ListSubmissionEventQuoted value)? quoted,
    TResult Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionEventAccepted value)? accepted,
    TResult Function(ListSubmissionEventRejected value)? rejected,
    TResult Function(ListSubmissionEventExpired value)? expired,
    TResult Function(ListSubmissionEventCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (counterOffered != null) {
      return counterOffered(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionEventCounterOfferedImplToJson(this);
  }
}

abstract class ListSubmissionEventCounterOffered
    implements ListSubmissionEvent {
  const factory ListSubmissionEventCounterOffered({
    required final String submissionId,
    required final String buyerId,
    required final double amount,
    required final DateTime timestamp,
  }) = _$ListSubmissionEventCounterOfferedImpl;

  factory ListSubmissionEventCounterOffered.fromJson(
    Map<String, dynamic> json,
  ) = _$ListSubmissionEventCounterOfferedImpl.fromJson;

  @override
  String get submissionId;
  String get buyerId;
  double get amount;
  @override
  DateTime get timestamp;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionEventCounterOfferedImplCopyWith<
    _$ListSubmissionEventCounterOfferedImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionEventAcceptedImplCopyWith<$Res>
    implements $ListSubmissionEventCopyWith<$Res> {
  factory _$$ListSubmissionEventAcceptedImplCopyWith(
    _$ListSubmissionEventAcceptedImpl value,
    $Res Function(_$ListSubmissionEventAcceptedImpl) then,
  ) = __$$ListSubmissionEventAcceptedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String submissionId,
    String actorId,
    double amount,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$ListSubmissionEventAcceptedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionEventCopyWithImpl<
          $Res,
          _$ListSubmissionEventAcceptedImpl
        >
    implements _$$ListSubmissionEventAcceptedImplCopyWith<$Res> {
  __$$ListSubmissionEventAcceptedImplCopyWithImpl(
    _$ListSubmissionEventAcceptedImpl _value,
    $Res Function(_$ListSubmissionEventAcceptedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? submissionId = null,
    Object? actorId = null,
    Object? amount = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$ListSubmissionEventAcceptedImpl(
        submissionId: null == submissionId
            ? _value.submissionId
            : submissionId // ignore: cast_nullable_to_non_nullable
                  as String,
        actorId: null == actorId
            ? _value.actorId
            : actorId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionEventAcceptedImpl implements ListSubmissionEventAccepted {
  const _$ListSubmissionEventAcceptedImpl({
    required this.submissionId,
    required this.actorId,
    required this.amount,
    required this.timestamp,
    final String? $type,
  }) : $type = $type ?? 'accepted';

  factory _$ListSubmissionEventAcceptedImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionEventAcceptedImplFromJson(json);

  @override
  final String submissionId;
  @override
  final String actorId;
  @override
  final double amount;
  @override
  final DateTime timestamp;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionEvent.accepted(submissionId: $submissionId, actorId: $actorId, amount: $amount, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionEventAcceptedImpl &&
            (identical(other.submissionId, submissionId) ||
                other.submissionId == submissionId) &&
            (identical(other.actorId, actorId) || other.actorId == actorId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, submissionId, actorId, amount, timestamp);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionEventAcceptedImplCopyWith<_$ListSubmissionEventAcceptedImpl>
  get copyWith =>
      __$$ListSubmissionEventAcceptedImplCopyWithImpl<
        _$ListSubmissionEventAcceptedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )
    submitted,
    required TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )
    quoted,
    required TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )
    counterOffered,
    required TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )
    accepted,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    rejected,
    required TResult Function(String submissionId, DateTime timestamp) expired,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    cancelled,
  }) {
    return accepted(submissionId, actorId, amount, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult? Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult? Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult? Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult? Function(String submissionId, DateTime timestamp)? expired,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
  }) {
    return accepted?.call(submissionId, actorId, amount, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult Function(String submissionId, DateTime timestamp)? expired,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
    required TResult orElse(),
  }) {
    if (accepted != null) {
      return accepted(submissionId, actorId, amount, timestamp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionEventSubmitted value) submitted,
    required TResult Function(ListSubmissionEventQuoted value) quoted,
    required TResult Function(ListSubmissionEventCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionEventAccepted value) accepted,
    required TResult Function(ListSubmissionEventRejected value) rejected,
    required TResult Function(ListSubmissionEventExpired value) expired,
    required TResult Function(ListSubmissionEventCancelled value) cancelled,
  }) {
    return accepted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionEventSubmitted value)? submitted,
    TResult? Function(ListSubmissionEventQuoted value)? quoted,
    TResult? Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionEventAccepted value)? accepted,
    TResult? Function(ListSubmissionEventRejected value)? rejected,
    TResult? Function(ListSubmissionEventExpired value)? expired,
    TResult? Function(ListSubmissionEventCancelled value)? cancelled,
  }) {
    return accepted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionEventSubmitted value)? submitted,
    TResult Function(ListSubmissionEventQuoted value)? quoted,
    TResult Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionEventAccepted value)? accepted,
    TResult Function(ListSubmissionEventRejected value)? rejected,
    TResult Function(ListSubmissionEventExpired value)? expired,
    TResult Function(ListSubmissionEventCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (accepted != null) {
      return accepted(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionEventAcceptedImplToJson(this);
  }
}

abstract class ListSubmissionEventAccepted implements ListSubmissionEvent {
  const factory ListSubmissionEventAccepted({
    required final String submissionId,
    required final String actorId,
    required final double amount,
    required final DateTime timestamp,
  }) = _$ListSubmissionEventAcceptedImpl;

  factory ListSubmissionEventAccepted.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionEventAcceptedImpl.fromJson;

  @override
  String get submissionId;
  String get actorId;
  double get amount;
  @override
  DateTime get timestamp;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionEventAcceptedImplCopyWith<_$ListSubmissionEventAcceptedImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionEventRejectedImplCopyWith<$Res>
    implements $ListSubmissionEventCopyWith<$Res> {
  factory _$$ListSubmissionEventRejectedImplCopyWith(
    _$ListSubmissionEventRejectedImpl value,
    $Res Function(_$ListSubmissionEventRejectedImpl) then,
  ) = __$$ListSubmissionEventRejectedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String submissionId,
    String actorId,
    String reason,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$ListSubmissionEventRejectedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionEventCopyWithImpl<
          $Res,
          _$ListSubmissionEventRejectedImpl
        >
    implements _$$ListSubmissionEventRejectedImplCopyWith<$Res> {
  __$$ListSubmissionEventRejectedImplCopyWithImpl(
    _$ListSubmissionEventRejectedImpl _value,
    $Res Function(_$ListSubmissionEventRejectedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? submissionId = null,
    Object? actorId = null,
    Object? reason = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$ListSubmissionEventRejectedImpl(
        submissionId: null == submissionId
            ? _value.submissionId
            : submissionId // ignore: cast_nullable_to_non_nullable
                  as String,
        actorId: null == actorId
            ? _value.actorId
            : actorId // ignore: cast_nullable_to_non_nullable
                  as String,
        reason: null == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionEventRejectedImpl implements ListSubmissionEventRejected {
  const _$ListSubmissionEventRejectedImpl({
    required this.submissionId,
    required this.actorId,
    required this.reason,
    required this.timestamp,
    final String? $type,
  }) : $type = $type ?? 'rejected';

  factory _$ListSubmissionEventRejectedImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionEventRejectedImplFromJson(json);

  @override
  final String submissionId;
  @override
  final String actorId;
  @override
  final String reason;
  @override
  final DateTime timestamp;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionEvent.rejected(submissionId: $submissionId, actorId: $actorId, reason: $reason, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionEventRejectedImpl &&
            (identical(other.submissionId, submissionId) ||
                other.submissionId == submissionId) &&
            (identical(other.actorId, actorId) || other.actorId == actorId) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, submissionId, actorId, reason, timestamp);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionEventRejectedImplCopyWith<_$ListSubmissionEventRejectedImpl>
  get copyWith =>
      __$$ListSubmissionEventRejectedImplCopyWithImpl<
        _$ListSubmissionEventRejectedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )
    submitted,
    required TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )
    quoted,
    required TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )
    counterOffered,
    required TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )
    accepted,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    rejected,
    required TResult Function(String submissionId, DateTime timestamp) expired,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    cancelled,
  }) {
    return rejected(submissionId, actorId, reason, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult? Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult? Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult? Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult? Function(String submissionId, DateTime timestamp)? expired,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
  }) {
    return rejected?.call(submissionId, actorId, reason, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult Function(String submissionId, DateTime timestamp)? expired,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
    required TResult orElse(),
  }) {
    if (rejected != null) {
      return rejected(submissionId, actorId, reason, timestamp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionEventSubmitted value) submitted,
    required TResult Function(ListSubmissionEventQuoted value) quoted,
    required TResult Function(ListSubmissionEventCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionEventAccepted value) accepted,
    required TResult Function(ListSubmissionEventRejected value) rejected,
    required TResult Function(ListSubmissionEventExpired value) expired,
    required TResult Function(ListSubmissionEventCancelled value) cancelled,
  }) {
    return rejected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionEventSubmitted value)? submitted,
    TResult? Function(ListSubmissionEventQuoted value)? quoted,
    TResult? Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionEventAccepted value)? accepted,
    TResult? Function(ListSubmissionEventRejected value)? rejected,
    TResult? Function(ListSubmissionEventExpired value)? expired,
    TResult? Function(ListSubmissionEventCancelled value)? cancelled,
  }) {
    return rejected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionEventSubmitted value)? submitted,
    TResult Function(ListSubmissionEventQuoted value)? quoted,
    TResult Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionEventAccepted value)? accepted,
    TResult Function(ListSubmissionEventRejected value)? rejected,
    TResult Function(ListSubmissionEventExpired value)? expired,
    TResult Function(ListSubmissionEventCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (rejected != null) {
      return rejected(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionEventRejectedImplToJson(this);
  }
}

abstract class ListSubmissionEventRejected implements ListSubmissionEvent {
  const factory ListSubmissionEventRejected({
    required final String submissionId,
    required final String actorId,
    required final String reason,
    required final DateTime timestamp,
  }) = _$ListSubmissionEventRejectedImpl;

  factory ListSubmissionEventRejected.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionEventRejectedImpl.fromJson;

  @override
  String get submissionId;
  String get actorId;
  String get reason;
  @override
  DateTime get timestamp;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionEventRejectedImplCopyWith<_$ListSubmissionEventRejectedImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionEventExpiredImplCopyWith<$Res>
    implements $ListSubmissionEventCopyWith<$Res> {
  factory _$$ListSubmissionEventExpiredImplCopyWith(
    _$ListSubmissionEventExpiredImpl value,
    $Res Function(_$ListSubmissionEventExpiredImpl) then,
  ) = __$$ListSubmissionEventExpiredImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String submissionId, DateTime timestamp});
}

/// @nodoc
class __$$ListSubmissionEventExpiredImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionEventCopyWithImpl<
          $Res,
          _$ListSubmissionEventExpiredImpl
        >
    implements _$$ListSubmissionEventExpiredImplCopyWith<$Res> {
  __$$ListSubmissionEventExpiredImplCopyWithImpl(
    _$ListSubmissionEventExpiredImpl _value,
    $Res Function(_$ListSubmissionEventExpiredImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? submissionId = null, Object? timestamp = null}) {
    return _then(
      _$ListSubmissionEventExpiredImpl(
        submissionId: null == submissionId
            ? _value.submissionId
            : submissionId // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionEventExpiredImpl implements ListSubmissionEventExpired {
  const _$ListSubmissionEventExpiredImpl({
    required this.submissionId,
    required this.timestamp,
    final String? $type,
  }) : $type = $type ?? 'expired';

  factory _$ListSubmissionEventExpiredImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionEventExpiredImplFromJson(json);

  @override
  final String submissionId;
  @override
  final DateTime timestamp;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionEvent.expired(submissionId: $submissionId, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionEventExpiredImpl &&
            (identical(other.submissionId, submissionId) ||
                other.submissionId == submissionId) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, submissionId, timestamp);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionEventExpiredImplCopyWith<_$ListSubmissionEventExpiredImpl>
  get copyWith =>
      __$$ListSubmissionEventExpiredImplCopyWithImpl<
        _$ListSubmissionEventExpiredImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )
    submitted,
    required TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )
    quoted,
    required TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )
    counterOffered,
    required TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )
    accepted,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    rejected,
    required TResult Function(String submissionId, DateTime timestamp) expired,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    cancelled,
  }) {
    return expired(submissionId, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult? Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult? Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult? Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult? Function(String submissionId, DateTime timestamp)? expired,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
  }) {
    return expired?.call(submissionId, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult Function(String submissionId, DateTime timestamp)? expired,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
    required TResult orElse(),
  }) {
    if (expired != null) {
      return expired(submissionId, timestamp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionEventSubmitted value) submitted,
    required TResult Function(ListSubmissionEventQuoted value) quoted,
    required TResult Function(ListSubmissionEventCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionEventAccepted value) accepted,
    required TResult Function(ListSubmissionEventRejected value) rejected,
    required TResult Function(ListSubmissionEventExpired value) expired,
    required TResult Function(ListSubmissionEventCancelled value) cancelled,
  }) {
    return expired(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionEventSubmitted value)? submitted,
    TResult? Function(ListSubmissionEventQuoted value)? quoted,
    TResult? Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionEventAccepted value)? accepted,
    TResult? Function(ListSubmissionEventRejected value)? rejected,
    TResult? Function(ListSubmissionEventExpired value)? expired,
    TResult? Function(ListSubmissionEventCancelled value)? cancelled,
  }) {
    return expired?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionEventSubmitted value)? submitted,
    TResult Function(ListSubmissionEventQuoted value)? quoted,
    TResult Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionEventAccepted value)? accepted,
    TResult Function(ListSubmissionEventRejected value)? rejected,
    TResult Function(ListSubmissionEventExpired value)? expired,
    TResult Function(ListSubmissionEventCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (expired != null) {
      return expired(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionEventExpiredImplToJson(this);
  }
}

abstract class ListSubmissionEventExpired implements ListSubmissionEvent {
  const factory ListSubmissionEventExpired({
    required final String submissionId,
    required final DateTime timestamp,
  }) = _$ListSubmissionEventExpiredImpl;

  factory ListSubmissionEventExpired.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionEventExpiredImpl.fromJson;

  @override
  String get submissionId;
  @override
  DateTime get timestamp;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionEventExpiredImplCopyWith<_$ListSubmissionEventExpiredImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionEventCancelledImplCopyWith<$Res>
    implements $ListSubmissionEventCopyWith<$Res> {
  factory _$$ListSubmissionEventCancelledImplCopyWith(
    _$ListSubmissionEventCancelledImpl value,
    $Res Function(_$ListSubmissionEventCancelledImpl) then,
  ) = __$$ListSubmissionEventCancelledImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String submissionId,
    String actorId,
    String reason,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$ListSubmissionEventCancelledImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionEventCopyWithImpl<
          $Res,
          _$ListSubmissionEventCancelledImpl
        >
    implements _$$ListSubmissionEventCancelledImplCopyWith<$Res> {
  __$$ListSubmissionEventCancelledImplCopyWithImpl(
    _$ListSubmissionEventCancelledImpl _value,
    $Res Function(_$ListSubmissionEventCancelledImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? submissionId = null,
    Object? actorId = null,
    Object? reason = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$ListSubmissionEventCancelledImpl(
        submissionId: null == submissionId
            ? _value.submissionId
            : submissionId // ignore: cast_nullable_to_non_nullable
                  as String,
        actorId: null == actorId
            ? _value.actorId
            : actorId // ignore: cast_nullable_to_non_nullable
                  as String,
        reason: null == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionEventCancelledImpl
    implements ListSubmissionEventCancelled {
  const _$ListSubmissionEventCancelledImpl({
    required this.submissionId,
    required this.actorId,
    required this.reason,
    required this.timestamp,
    final String? $type,
  }) : $type = $type ?? 'cancelled';

  factory _$ListSubmissionEventCancelledImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionEventCancelledImplFromJson(json);

  @override
  final String submissionId;
  @override
  final String actorId;
  @override
  final String reason;
  @override
  final DateTime timestamp;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionEvent.cancelled(submissionId: $submissionId, actorId: $actorId, reason: $reason, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionEventCancelledImpl &&
            (identical(other.submissionId, submissionId) ||
                other.submissionId == submissionId) &&
            (identical(other.actorId, actorId) || other.actorId == actorId) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, submissionId, actorId, reason, timestamp);

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionEventCancelledImplCopyWith<
    _$ListSubmissionEventCancelledImpl
  >
  get copyWith =>
      __$$ListSubmissionEventCancelledImplCopyWithImpl<
        _$ListSubmissionEventCancelledImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )
    submitted,
    required TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )
    quoted,
    required TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )
    counterOffered,
    required TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )
    accepted,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    rejected,
    required TResult Function(String submissionId, DateTime timestamp) expired,
    required TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )
    cancelled,
  }) {
    return cancelled(submissionId, actorId, reason, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult? Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult? Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult? Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult? Function(String submissionId, DateTime timestamp)? expired,
    TResult? Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
  }) {
    return cancelled?.call(submissionId, actorId, reason, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      String submissionId,
      String buyerId,
      String sellerId,
      DateTime timestamp,
    )?
    submitted,
    TResult Function(
      String submissionId,
      String sellerId,
      double amount,
      DateTime timestamp,
    )?
    quoted,
    TResult Function(
      String submissionId,
      String buyerId,
      double amount,
      DateTime timestamp,
    )?
    counterOffered,
    TResult Function(
      String submissionId,
      String actorId,
      double amount,
      DateTime timestamp,
    )?
    accepted,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    rejected,
    TResult Function(String submissionId, DateTime timestamp)? expired,
    TResult Function(
      String submissionId,
      String actorId,
      String reason,
      DateTime timestamp,
    )?
    cancelled,
    required TResult orElse(),
  }) {
    if (cancelled != null) {
      return cancelled(submissionId, actorId, reason, timestamp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionEventSubmitted value) submitted,
    required TResult Function(ListSubmissionEventQuoted value) quoted,
    required TResult Function(ListSubmissionEventCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionEventAccepted value) accepted,
    required TResult Function(ListSubmissionEventRejected value) rejected,
    required TResult Function(ListSubmissionEventExpired value) expired,
    required TResult Function(ListSubmissionEventCancelled value) cancelled,
  }) {
    return cancelled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionEventSubmitted value)? submitted,
    TResult? Function(ListSubmissionEventQuoted value)? quoted,
    TResult? Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionEventAccepted value)? accepted,
    TResult? Function(ListSubmissionEventRejected value)? rejected,
    TResult? Function(ListSubmissionEventExpired value)? expired,
    TResult? Function(ListSubmissionEventCancelled value)? cancelled,
  }) {
    return cancelled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionEventSubmitted value)? submitted,
    TResult Function(ListSubmissionEventQuoted value)? quoted,
    TResult Function(ListSubmissionEventCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionEventAccepted value)? accepted,
    TResult Function(ListSubmissionEventRejected value)? rejected,
    TResult Function(ListSubmissionEventExpired value)? expired,
    TResult Function(ListSubmissionEventCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (cancelled != null) {
      return cancelled(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionEventCancelledImplToJson(this);
  }
}

abstract class ListSubmissionEventCancelled implements ListSubmissionEvent {
  const factory ListSubmissionEventCancelled({
    required final String submissionId,
    required final String actorId,
    required final String reason,
    required final DateTime timestamp,
  }) = _$ListSubmissionEventCancelledImpl;

  factory ListSubmissionEventCancelled.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionEventCancelledImpl.fromJson;

  @override
  String get submissionId;
  String get actorId;
  String get reason;
  @override
  DateTime get timestamp;

  /// Create a copy of ListSubmissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionEventCancelledImplCopyWith<
    _$ListSubmissionEventCancelledImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

ListSubmissionStatus _$ListSubmissionStatusFromJson(Map<String, dynamic> json) {
  switch (json['runtimeType']) {
    case 'pending':
      return ListSubmissionStatusPending.fromJson(json);
    case 'quoted':
      return ListSubmissionStatusQuoted.fromJson(json);
    case 'counterOffered':
      return ListSubmissionStatusCounterOffered.fromJson(json);
    case 'accepted':
      return ListSubmissionStatusAccepted.fromJson(json);
    case 'rejected':
      return ListSubmissionStatusRejected.fromJson(json);
    case 'expired':
      return ListSubmissionStatusExpired.fromJson(json);
    case 'cancelled':
      return ListSubmissionStatusCancelled.fromJson(json);

    default:
      throw CheckedFromJsonException(
        json,
        'runtimeType',
        'ListSubmissionStatus',
        'Invalid union type "${json['runtimeType']}"!',
      );
  }
}

/// @nodoc
mixin _$ListSubmissionStatus {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime submittedAt) pending,
    required TResult Function(DateTime quotedAt, double amount) quoted,
    required TResult Function(DateTime counterOfferedAt, double amount)
    counterOffered,
    required TResult Function(DateTime acceptedAt, double amount) accepted,
    required TResult Function(DateTime rejectedAt, String reason) rejected,
    required TResult Function(DateTime expiredAt) expired,
    required TResult Function(DateTime cancelledAt, String reason) cancelled,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime submittedAt)? pending,
    TResult? Function(DateTime quotedAt, double amount)? quoted,
    TResult? Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult? Function(DateTime acceptedAt, double amount)? accepted,
    TResult? Function(DateTime rejectedAt, String reason)? rejected,
    TResult? Function(DateTime expiredAt)? expired,
    TResult? Function(DateTime cancelledAt, String reason)? cancelled,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime submittedAt)? pending,
    TResult Function(DateTime quotedAt, double amount)? quoted,
    TResult Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult Function(DateTime acceptedAt, double amount)? accepted,
    TResult Function(DateTime rejectedAt, String reason)? rejected,
    TResult Function(DateTime expiredAt)? expired,
    TResult Function(DateTime cancelledAt, String reason)? cancelled,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionStatusPending value) pending,
    required TResult Function(ListSubmissionStatusQuoted value) quoted,
    required TResult Function(ListSubmissionStatusCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionStatusAccepted value) accepted,
    required TResult Function(ListSubmissionStatusRejected value) rejected,
    required TResult Function(ListSubmissionStatusExpired value) expired,
    required TResult Function(ListSubmissionStatusCancelled value) cancelled,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionStatusPending value)? pending,
    TResult? Function(ListSubmissionStatusQuoted value)? quoted,
    TResult? Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionStatusAccepted value)? accepted,
    TResult? Function(ListSubmissionStatusRejected value)? rejected,
    TResult? Function(ListSubmissionStatusExpired value)? expired,
    TResult? Function(ListSubmissionStatusCancelled value)? cancelled,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionStatusPending value)? pending,
    TResult Function(ListSubmissionStatusQuoted value)? quoted,
    TResult Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionStatusAccepted value)? accepted,
    TResult Function(ListSubmissionStatusRejected value)? rejected,
    TResult Function(ListSubmissionStatusExpired value)? expired,
    TResult Function(ListSubmissionStatusCancelled value)? cancelled,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this ListSubmissionStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListSubmissionStatusCopyWith<$Res> {
  factory $ListSubmissionStatusCopyWith(
    ListSubmissionStatus value,
    $Res Function(ListSubmissionStatus) then,
  ) = _$ListSubmissionStatusCopyWithImpl<$Res, ListSubmissionStatus>;
}

/// @nodoc
class _$ListSubmissionStatusCopyWithImpl<
  $Res,
  $Val extends ListSubmissionStatus
>
    implements $ListSubmissionStatusCopyWith<$Res> {
  _$ListSubmissionStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ListSubmissionStatusPendingImplCopyWith<$Res> {
  factory _$$ListSubmissionStatusPendingImplCopyWith(
    _$ListSubmissionStatusPendingImpl value,
    $Res Function(_$ListSubmissionStatusPendingImpl) then,
  ) = __$$ListSubmissionStatusPendingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime submittedAt});
}

/// @nodoc
class __$$ListSubmissionStatusPendingImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionStatusCopyWithImpl<
          $Res,
          _$ListSubmissionStatusPendingImpl
        >
    implements _$$ListSubmissionStatusPendingImplCopyWith<$Res> {
  __$$ListSubmissionStatusPendingImplCopyWithImpl(
    _$ListSubmissionStatusPendingImpl _value,
    $Res Function(_$ListSubmissionStatusPendingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? submittedAt = null}) {
    return _then(
      _$ListSubmissionStatusPendingImpl(
        submittedAt: null == submittedAt
            ? _value.submittedAt
            : submittedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionStatusPendingImpl implements ListSubmissionStatusPending {
  const _$ListSubmissionStatusPendingImpl({
    required this.submittedAt,
    final String? $type,
  }) : $type = $type ?? 'pending';

  factory _$ListSubmissionStatusPendingImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionStatusPendingImplFromJson(json);

  @override
  final DateTime submittedAt;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionStatus.pending(submittedAt: $submittedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionStatusPendingImpl &&
            (identical(other.submittedAt, submittedAt) ||
                other.submittedAt == submittedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, submittedAt);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionStatusPendingImplCopyWith<_$ListSubmissionStatusPendingImpl>
  get copyWith =>
      __$$ListSubmissionStatusPendingImplCopyWithImpl<
        _$ListSubmissionStatusPendingImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime submittedAt) pending,
    required TResult Function(DateTime quotedAt, double amount) quoted,
    required TResult Function(DateTime counterOfferedAt, double amount)
    counterOffered,
    required TResult Function(DateTime acceptedAt, double amount) accepted,
    required TResult Function(DateTime rejectedAt, String reason) rejected,
    required TResult Function(DateTime expiredAt) expired,
    required TResult Function(DateTime cancelledAt, String reason) cancelled,
  }) {
    return pending(submittedAt);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime submittedAt)? pending,
    TResult? Function(DateTime quotedAt, double amount)? quoted,
    TResult? Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult? Function(DateTime acceptedAt, double amount)? accepted,
    TResult? Function(DateTime rejectedAt, String reason)? rejected,
    TResult? Function(DateTime expiredAt)? expired,
    TResult? Function(DateTime cancelledAt, String reason)? cancelled,
  }) {
    return pending?.call(submittedAt);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime submittedAt)? pending,
    TResult Function(DateTime quotedAt, double amount)? quoted,
    TResult Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult Function(DateTime acceptedAt, double amount)? accepted,
    TResult Function(DateTime rejectedAt, String reason)? rejected,
    TResult Function(DateTime expiredAt)? expired,
    TResult Function(DateTime cancelledAt, String reason)? cancelled,
    required TResult orElse(),
  }) {
    if (pending != null) {
      return pending(submittedAt);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionStatusPending value) pending,
    required TResult Function(ListSubmissionStatusQuoted value) quoted,
    required TResult Function(ListSubmissionStatusCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionStatusAccepted value) accepted,
    required TResult Function(ListSubmissionStatusRejected value) rejected,
    required TResult Function(ListSubmissionStatusExpired value) expired,
    required TResult Function(ListSubmissionStatusCancelled value) cancelled,
  }) {
    return pending(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionStatusPending value)? pending,
    TResult? Function(ListSubmissionStatusQuoted value)? quoted,
    TResult? Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionStatusAccepted value)? accepted,
    TResult? Function(ListSubmissionStatusRejected value)? rejected,
    TResult? Function(ListSubmissionStatusExpired value)? expired,
    TResult? Function(ListSubmissionStatusCancelled value)? cancelled,
  }) {
    return pending?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionStatusPending value)? pending,
    TResult Function(ListSubmissionStatusQuoted value)? quoted,
    TResult Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionStatusAccepted value)? accepted,
    TResult Function(ListSubmissionStatusRejected value)? rejected,
    TResult Function(ListSubmissionStatusExpired value)? expired,
    TResult Function(ListSubmissionStatusCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (pending != null) {
      return pending(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionStatusPendingImplToJson(this);
  }
}

abstract class ListSubmissionStatusPending implements ListSubmissionStatus {
  const factory ListSubmissionStatusPending({
    required final DateTime submittedAt,
  }) = _$ListSubmissionStatusPendingImpl;

  factory ListSubmissionStatusPending.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionStatusPendingImpl.fromJson;

  DateTime get submittedAt;

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionStatusPendingImplCopyWith<_$ListSubmissionStatusPendingImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionStatusQuotedImplCopyWith<$Res> {
  factory _$$ListSubmissionStatusQuotedImplCopyWith(
    _$ListSubmissionStatusQuotedImpl value,
    $Res Function(_$ListSubmissionStatusQuotedImpl) then,
  ) = __$$ListSubmissionStatusQuotedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime quotedAt, double amount});
}

/// @nodoc
class __$$ListSubmissionStatusQuotedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionStatusCopyWithImpl<
          $Res,
          _$ListSubmissionStatusQuotedImpl
        >
    implements _$$ListSubmissionStatusQuotedImplCopyWith<$Res> {
  __$$ListSubmissionStatusQuotedImplCopyWithImpl(
    _$ListSubmissionStatusQuotedImpl _value,
    $Res Function(_$ListSubmissionStatusQuotedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? quotedAt = null, Object? amount = null}) {
    return _then(
      _$ListSubmissionStatusQuotedImpl(
        quotedAt: null == quotedAt
            ? _value.quotedAt
            : quotedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionStatusQuotedImpl implements ListSubmissionStatusQuoted {
  const _$ListSubmissionStatusQuotedImpl({
    required this.quotedAt,
    required this.amount,
    final String? $type,
  }) : $type = $type ?? 'quoted';

  factory _$ListSubmissionStatusQuotedImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionStatusQuotedImplFromJson(json);

  @override
  final DateTime quotedAt;
  @override
  final double amount;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionStatus.quoted(quotedAt: $quotedAt, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionStatusQuotedImpl &&
            (identical(other.quotedAt, quotedAt) ||
                other.quotedAt == quotedAt) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, quotedAt, amount);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionStatusQuotedImplCopyWith<_$ListSubmissionStatusQuotedImpl>
  get copyWith =>
      __$$ListSubmissionStatusQuotedImplCopyWithImpl<
        _$ListSubmissionStatusQuotedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime submittedAt) pending,
    required TResult Function(DateTime quotedAt, double amount) quoted,
    required TResult Function(DateTime counterOfferedAt, double amount)
    counterOffered,
    required TResult Function(DateTime acceptedAt, double amount) accepted,
    required TResult Function(DateTime rejectedAt, String reason) rejected,
    required TResult Function(DateTime expiredAt) expired,
    required TResult Function(DateTime cancelledAt, String reason) cancelled,
  }) {
    return quoted(quotedAt, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime submittedAt)? pending,
    TResult? Function(DateTime quotedAt, double amount)? quoted,
    TResult? Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult? Function(DateTime acceptedAt, double amount)? accepted,
    TResult? Function(DateTime rejectedAt, String reason)? rejected,
    TResult? Function(DateTime expiredAt)? expired,
    TResult? Function(DateTime cancelledAt, String reason)? cancelled,
  }) {
    return quoted?.call(quotedAt, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime submittedAt)? pending,
    TResult Function(DateTime quotedAt, double amount)? quoted,
    TResult Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult Function(DateTime acceptedAt, double amount)? accepted,
    TResult Function(DateTime rejectedAt, String reason)? rejected,
    TResult Function(DateTime expiredAt)? expired,
    TResult Function(DateTime cancelledAt, String reason)? cancelled,
    required TResult orElse(),
  }) {
    if (quoted != null) {
      return quoted(quotedAt, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionStatusPending value) pending,
    required TResult Function(ListSubmissionStatusQuoted value) quoted,
    required TResult Function(ListSubmissionStatusCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionStatusAccepted value) accepted,
    required TResult Function(ListSubmissionStatusRejected value) rejected,
    required TResult Function(ListSubmissionStatusExpired value) expired,
    required TResult Function(ListSubmissionStatusCancelled value) cancelled,
  }) {
    return quoted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionStatusPending value)? pending,
    TResult? Function(ListSubmissionStatusQuoted value)? quoted,
    TResult? Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionStatusAccepted value)? accepted,
    TResult? Function(ListSubmissionStatusRejected value)? rejected,
    TResult? Function(ListSubmissionStatusExpired value)? expired,
    TResult? Function(ListSubmissionStatusCancelled value)? cancelled,
  }) {
    return quoted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionStatusPending value)? pending,
    TResult Function(ListSubmissionStatusQuoted value)? quoted,
    TResult Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionStatusAccepted value)? accepted,
    TResult Function(ListSubmissionStatusRejected value)? rejected,
    TResult Function(ListSubmissionStatusExpired value)? expired,
    TResult Function(ListSubmissionStatusCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (quoted != null) {
      return quoted(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionStatusQuotedImplToJson(this);
  }
}

abstract class ListSubmissionStatusQuoted implements ListSubmissionStatus {
  const factory ListSubmissionStatusQuoted({
    required final DateTime quotedAt,
    required final double amount,
  }) = _$ListSubmissionStatusQuotedImpl;

  factory ListSubmissionStatusQuoted.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionStatusQuotedImpl.fromJson;

  DateTime get quotedAt;
  double get amount;

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionStatusQuotedImplCopyWith<_$ListSubmissionStatusQuotedImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionStatusCounterOfferedImplCopyWith<$Res> {
  factory _$$ListSubmissionStatusCounterOfferedImplCopyWith(
    _$ListSubmissionStatusCounterOfferedImpl value,
    $Res Function(_$ListSubmissionStatusCounterOfferedImpl) then,
  ) = __$$ListSubmissionStatusCounterOfferedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime counterOfferedAt, double amount});
}

/// @nodoc
class __$$ListSubmissionStatusCounterOfferedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionStatusCopyWithImpl<
          $Res,
          _$ListSubmissionStatusCounterOfferedImpl
        >
    implements _$$ListSubmissionStatusCounterOfferedImplCopyWith<$Res> {
  __$$ListSubmissionStatusCounterOfferedImplCopyWithImpl(
    _$ListSubmissionStatusCounterOfferedImpl _value,
    $Res Function(_$ListSubmissionStatusCounterOfferedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? counterOfferedAt = null, Object? amount = null}) {
    return _then(
      _$ListSubmissionStatusCounterOfferedImpl(
        counterOfferedAt: null == counterOfferedAt
            ? _value.counterOfferedAt
            : counterOfferedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionStatusCounterOfferedImpl
    implements ListSubmissionStatusCounterOffered {
  const _$ListSubmissionStatusCounterOfferedImpl({
    required this.counterOfferedAt,
    required this.amount,
    final String? $type,
  }) : $type = $type ?? 'counterOffered';

  factory _$ListSubmissionStatusCounterOfferedImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionStatusCounterOfferedImplFromJson(json);

  @override
  final DateTime counterOfferedAt;
  @override
  final double amount;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionStatus.counterOffered(counterOfferedAt: $counterOfferedAt, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionStatusCounterOfferedImpl &&
            (identical(other.counterOfferedAt, counterOfferedAt) ||
                other.counterOfferedAt == counterOfferedAt) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, counterOfferedAt, amount);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionStatusCounterOfferedImplCopyWith<
    _$ListSubmissionStatusCounterOfferedImpl
  >
  get copyWith =>
      __$$ListSubmissionStatusCounterOfferedImplCopyWithImpl<
        _$ListSubmissionStatusCounterOfferedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime submittedAt) pending,
    required TResult Function(DateTime quotedAt, double amount) quoted,
    required TResult Function(DateTime counterOfferedAt, double amount)
    counterOffered,
    required TResult Function(DateTime acceptedAt, double amount) accepted,
    required TResult Function(DateTime rejectedAt, String reason) rejected,
    required TResult Function(DateTime expiredAt) expired,
    required TResult Function(DateTime cancelledAt, String reason) cancelled,
  }) {
    return counterOffered(counterOfferedAt, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime submittedAt)? pending,
    TResult? Function(DateTime quotedAt, double amount)? quoted,
    TResult? Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult? Function(DateTime acceptedAt, double amount)? accepted,
    TResult? Function(DateTime rejectedAt, String reason)? rejected,
    TResult? Function(DateTime expiredAt)? expired,
    TResult? Function(DateTime cancelledAt, String reason)? cancelled,
  }) {
    return counterOffered?.call(counterOfferedAt, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime submittedAt)? pending,
    TResult Function(DateTime quotedAt, double amount)? quoted,
    TResult Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult Function(DateTime acceptedAt, double amount)? accepted,
    TResult Function(DateTime rejectedAt, String reason)? rejected,
    TResult Function(DateTime expiredAt)? expired,
    TResult Function(DateTime cancelledAt, String reason)? cancelled,
    required TResult orElse(),
  }) {
    if (counterOffered != null) {
      return counterOffered(counterOfferedAt, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionStatusPending value) pending,
    required TResult Function(ListSubmissionStatusQuoted value) quoted,
    required TResult Function(ListSubmissionStatusCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionStatusAccepted value) accepted,
    required TResult Function(ListSubmissionStatusRejected value) rejected,
    required TResult Function(ListSubmissionStatusExpired value) expired,
    required TResult Function(ListSubmissionStatusCancelled value) cancelled,
  }) {
    return counterOffered(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionStatusPending value)? pending,
    TResult? Function(ListSubmissionStatusQuoted value)? quoted,
    TResult? Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionStatusAccepted value)? accepted,
    TResult? Function(ListSubmissionStatusRejected value)? rejected,
    TResult? Function(ListSubmissionStatusExpired value)? expired,
    TResult? Function(ListSubmissionStatusCancelled value)? cancelled,
  }) {
    return counterOffered?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionStatusPending value)? pending,
    TResult Function(ListSubmissionStatusQuoted value)? quoted,
    TResult Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionStatusAccepted value)? accepted,
    TResult Function(ListSubmissionStatusRejected value)? rejected,
    TResult Function(ListSubmissionStatusExpired value)? expired,
    TResult Function(ListSubmissionStatusCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (counterOffered != null) {
      return counterOffered(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionStatusCounterOfferedImplToJson(this);
  }
}

abstract class ListSubmissionStatusCounterOffered
    implements ListSubmissionStatus {
  const factory ListSubmissionStatusCounterOffered({
    required final DateTime counterOfferedAt,
    required final double amount,
  }) = _$ListSubmissionStatusCounterOfferedImpl;

  factory ListSubmissionStatusCounterOffered.fromJson(
    Map<String, dynamic> json,
  ) = _$ListSubmissionStatusCounterOfferedImpl.fromJson;

  DateTime get counterOfferedAt;
  double get amount;

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionStatusCounterOfferedImplCopyWith<
    _$ListSubmissionStatusCounterOfferedImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionStatusAcceptedImplCopyWith<$Res> {
  factory _$$ListSubmissionStatusAcceptedImplCopyWith(
    _$ListSubmissionStatusAcceptedImpl value,
    $Res Function(_$ListSubmissionStatusAcceptedImpl) then,
  ) = __$$ListSubmissionStatusAcceptedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime acceptedAt, double amount});
}

/// @nodoc
class __$$ListSubmissionStatusAcceptedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionStatusCopyWithImpl<
          $Res,
          _$ListSubmissionStatusAcceptedImpl
        >
    implements _$$ListSubmissionStatusAcceptedImplCopyWith<$Res> {
  __$$ListSubmissionStatusAcceptedImplCopyWithImpl(
    _$ListSubmissionStatusAcceptedImpl _value,
    $Res Function(_$ListSubmissionStatusAcceptedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? acceptedAt = null, Object? amount = null}) {
    return _then(
      _$ListSubmissionStatusAcceptedImpl(
        acceptedAt: null == acceptedAt
            ? _value.acceptedAt
            : acceptedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionStatusAcceptedImpl
    implements ListSubmissionStatusAccepted {
  const _$ListSubmissionStatusAcceptedImpl({
    required this.acceptedAt,
    required this.amount,
    final String? $type,
  }) : $type = $type ?? 'accepted';

  factory _$ListSubmissionStatusAcceptedImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionStatusAcceptedImplFromJson(json);

  @override
  final DateTime acceptedAt;
  @override
  final double amount;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionStatus.accepted(acceptedAt: $acceptedAt, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionStatusAcceptedImpl &&
            (identical(other.acceptedAt, acceptedAt) ||
                other.acceptedAt == acceptedAt) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, acceptedAt, amount);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionStatusAcceptedImplCopyWith<
    _$ListSubmissionStatusAcceptedImpl
  >
  get copyWith =>
      __$$ListSubmissionStatusAcceptedImplCopyWithImpl<
        _$ListSubmissionStatusAcceptedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime submittedAt) pending,
    required TResult Function(DateTime quotedAt, double amount) quoted,
    required TResult Function(DateTime counterOfferedAt, double amount)
    counterOffered,
    required TResult Function(DateTime acceptedAt, double amount) accepted,
    required TResult Function(DateTime rejectedAt, String reason) rejected,
    required TResult Function(DateTime expiredAt) expired,
    required TResult Function(DateTime cancelledAt, String reason) cancelled,
  }) {
    return accepted(acceptedAt, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime submittedAt)? pending,
    TResult? Function(DateTime quotedAt, double amount)? quoted,
    TResult? Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult? Function(DateTime acceptedAt, double amount)? accepted,
    TResult? Function(DateTime rejectedAt, String reason)? rejected,
    TResult? Function(DateTime expiredAt)? expired,
    TResult? Function(DateTime cancelledAt, String reason)? cancelled,
  }) {
    return accepted?.call(acceptedAt, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime submittedAt)? pending,
    TResult Function(DateTime quotedAt, double amount)? quoted,
    TResult Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult Function(DateTime acceptedAt, double amount)? accepted,
    TResult Function(DateTime rejectedAt, String reason)? rejected,
    TResult Function(DateTime expiredAt)? expired,
    TResult Function(DateTime cancelledAt, String reason)? cancelled,
    required TResult orElse(),
  }) {
    if (accepted != null) {
      return accepted(acceptedAt, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionStatusPending value) pending,
    required TResult Function(ListSubmissionStatusQuoted value) quoted,
    required TResult Function(ListSubmissionStatusCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionStatusAccepted value) accepted,
    required TResult Function(ListSubmissionStatusRejected value) rejected,
    required TResult Function(ListSubmissionStatusExpired value) expired,
    required TResult Function(ListSubmissionStatusCancelled value) cancelled,
  }) {
    return accepted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionStatusPending value)? pending,
    TResult? Function(ListSubmissionStatusQuoted value)? quoted,
    TResult? Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionStatusAccepted value)? accepted,
    TResult? Function(ListSubmissionStatusRejected value)? rejected,
    TResult? Function(ListSubmissionStatusExpired value)? expired,
    TResult? Function(ListSubmissionStatusCancelled value)? cancelled,
  }) {
    return accepted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionStatusPending value)? pending,
    TResult Function(ListSubmissionStatusQuoted value)? quoted,
    TResult Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionStatusAccepted value)? accepted,
    TResult Function(ListSubmissionStatusRejected value)? rejected,
    TResult Function(ListSubmissionStatusExpired value)? expired,
    TResult Function(ListSubmissionStatusCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (accepted != null) {
      return accepted(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionStatusAcceptedImplToJson(this);
  }
}

abstract class ListSubmissionStatusAccepted implements ListSubmissionStatus {
  const factory ListSubmissionStatusAccepted({
    required final DateTime acceptedAt,
    required final double amount,
  }) = _$ListSubmissionStatusAcceptedImpl;

  factory ListSubmissionStatusAccepted.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionStatusAcceptedImpl.fromJson;

  DateTime get acceptedAt;
  double get amount;

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionStatusAcceptedImplCopyWith<
    _$ListSubmissionStatusAcceptedImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionStatusRejectedImplCopyWith<$Res> {
  factory _$$ListSubmissionStatusRejectedImplCopyWith(
    _$ListSubmissionStatusRejectedImpl value,
    $Res Function(_$ListSubmissionStatusRejectedImpl) then,
  ) = __$$ListSubmissionStatusRejectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime rejectedAt, String reason});
}

/// @nodoc
class __$$ListSubmissionStatusRejectedImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionStatusCopyWithImpl<
          $Res,
          _$ListSubmissionStatusRejectedImpl
        >
    implements _$$ListSubmissionStatusRejectedImplCopyWith<$Res> {
  __$$ListSubmissionStatusRejectedImplCopyWithImpl(
    _$ListSubmissionStatusRejectedImpl _value,
    $Res Function(_$ListSubmissionStatusRejectedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? rejectedAt = null, Object? reason = null}) {
    return _then(
      _$ListSubmissionStatusRejectedImpl(
        rejectedAt: null == rejectedAt
            ? _value.rejectedAt
            : rejectedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        reason: null == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionStatusRejectedImpl
    implements ListSubmissionStatusRejected {
  const _$ListSubmissionStatusRejectedImpl({
    required this.rejectedAt,
    required this.reason,
    final String? $type,
  }) : $type = $type ?? 'rejected';

  factory _$ListSubmissionStatusRejectedImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionStatusRejectedImplFromJson(json);

  @override
  final DateTime rejectedAt;
  @override
  final String reason;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionStatus.rejected(rejectedAt: $rejectedAt, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionStatusRejectedImpl &&
            (identical(other.rejectedAt, rejectedAt) ||
                other.rejectedAt == rejectedAt) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, rejectedAt, reason);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionStatusRejectedImplCopyWith<
    _$ListSubmissionStatusRejectedImpl
  >
  get copyWith =>
      __$$ListSubmissionStatusRejectedImplCopyWithImpl<
        _$ListSubmissionStatusRejectedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime submittedAt) pending,
    required TResult Function(DateTime quotedAt, double amount) quoted,
    required TResult Function(DateTime counterOfferedAt, double amount)
    counterOffered,
    required TResult Function(DateTime acceptedAt, double amount) accepted,
    required TResult Function(DateTime rejectedAt, String reason) rejected,
    required TResult Function(DateTime expiredAt) expired,
    required TResult Function(DateTime cancelledAt, String reason) cancelled,
  }) {
    return rejected(rejectedAt, reason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime submittedAt)? pending,
    TResult? Function(DateTime quotedAt, double amount)? quoted,
    TResult? Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult? Function(DateTime acceptedAt, double amount)? accepted,
    TResult? Function(DateTime rejectedAt, String reason)? rejected,
    TResult? Function(DateTime expiredAt)? expired,
    TResult? Function(DateTime cancelledAt, String reason)? cancelled,
  }) {
    return rejected?.call(rejectedAt, reason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime submittedAt)? pending,
    TResult Function(DateTime quotedAt, double amount)? quoted,
    TResult Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult Function(DateTime acceptedAt, double amount)? accepted,
    TResult Function(DateTime rejectedAt, String reason)? rejected,
    TResult Function(DateTime expiredAt)? expired,
    TResult Function(DateTime cancelledAt, String reason)? cancelled,
    required TResult orElse(),
  }) {
    if (rejected != null) {
      return rejected(rejectedAt, reason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionStatusPending value) pending,
    required TResult Function(ListSubmissionStatusQuoted value) quoted,
    required TResult Function(ListSubmissionStatusCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionStatusAccepted value) accepted,
    required TResult Function(ListSubmissionStatusRejected value) rejected,
    required TResult Function(ListSubmissionStatusExpired value) expired,
    required TResult Function(ListSubmissionStatusCancelled value) cancelled,
  }) {
    return rejected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionStatusPending value)? pending,
    TResult? Function(ListSubmissionStatusQuoted value)? quoted,
    TResult? Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionStatusAccepted value)? accepted,
    TResult? Function(ListSubmissionStatusRejected value)? rejected,
    TResult? Function(ListSubmissionStatusExpired value)? expired,
    TResult? Function(ListSubmissionStatusCancelled value)? cancelled,
  }) {
    return rejected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionStatusPending value)? pending,
    TResult Function(ListSubmissionStatusQuoted value)? quoted,
    TResult Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionStatusAccepted value)? accepted,
    TResult Function(ListSubmissionStatusRejected value)? rejected,
    TResult Function(ListSubmissionStatusExpired value)? expired,
    TResult Function(ListSubmissionStatusCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (rejected != null) {
      return rejected(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionStatusRejectedImplToJson(this);
  }
}

abstract class ListSubmissionStatusRejected implements ListSubmissionStatus {
  const factory ListSubmissionStatusRejected({
    required final DateTime rejectedAt,
    required final String reason,
  }) = _$ListSubmissionStatusRejectedImpl;

  factory ListSubmissionStatusRejected.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionStatusRejectedImpl.fromJson;

  DateTime get rejectedAt;
  String get reason;

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionStatusRejectedImplCopyWith<
    _$ListSubmissionStatusRejectedImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionStatusExpiredImplCopyWith<$Res> {
  factory _$$ListSubmissionStatusExpiredImplCopyWith(
    _$ListSubmissionStatusExpiredImpl value,
    $Res Function(_$ListSubmissionStatusExpiredImpl) then,
  ) = __$$ListSubmissionStatusExpiredImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime expiredAt});
}

/// @nodoc
class __$$ListSubmissionStatusExpiredImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionStatusCopyWithImpl<
          $Res,
          _$ListSubmissionStatusExpiredImpl
        >
    implements _$$ListSubmissionStatusExpiredImplCopyWith<$Res> {
  __$$ListSubmissionStatusExpiredImplCopyWithImpl(
    _$ListSubmissionStatusExpiredImpl _value,
    $Res Function(_$ListSubmissionStatusExpiredImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? expiredAt = null}) {
    return _then(
      _$ListSubmissionStatusExpiredImpl(
        expiredAt: null == expiredAt
            ? _value.expiredAt
            : expiredAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionStatusExpiredImpl implements ListSubmissionStatusExpired {
  const _$ListSubmissionStatusExpiredImpl({
    required this.expiredAt,
    final String? $type,
  }) : $type = $type ?? 'expired';

  factory _$ListSubmissionStatusExpiredImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionStatusExpiredImplFromJson(json);

  @override
  final DateTime expiredAt;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionStatus.expired(expiredAt: $expiredAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionStatusExpiredImpl &&
            (identical(other.expiredAt, expiredAt) ||
                other.expiredAt == expiredAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, expiredAt);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionStatusExpiredImplCopyWith<_$ListSubmissionStatusExpiredImpl>
  get copyWith =>
      __$$ListSubmissionStatusExpiredImplCopyWithImpl<
        _$ListSubmissionStatusExpiredImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime submittedAt) pending,
    required TResult Function(DateTime quotedAt, double amount) quoted,
    required TResult Function(DateTime counterOfferedAt, double amount)
    counterOffered,
    required TResult Function(DateTime acceptedAt, double amount) accepted,
    required TResult Function(DateTime rejectedAt, String reason) rejected,
    required TResult Function(DateTime expiredAt) expired,
    required TResult Function(DateTime cancelledAt, String reason) cancelled,
  }) {
    return expired(expiredAt);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime submittedAt)? pending,
    TResult? Function(DateTime quotedAt, double amount)? quoted,
    TResult? Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult? Function(DateTime acceptedAt, double amount)? accepted,
    TResult? Function(DateTime rejectedAt, String reason)? rejected,
    TResult? Function(DateTime expiredAt)? expired,
    TResult? Function(DateTime cancelledAt, String reason)? cancelled,
  }) {
    return expired?.call(expiredAt);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime submittedAt)? pending,
    TResult Function(DateTime quotedAt, double amount)? quoted,
    TResult Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult Function(DateTime acceptedAt, double amount)? accepted,
    TResult Function(DateTime rejectedAt, String reason)? rejected,
    TResult Function(DateTime expiredAt)? expired,
    TResult Function(DateTime cancelledAt, String reason)? cancelled,
    required TResult orElse(),
  }) {
    if (expired != null) {
      return expired(expiredAt);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionStatusPending value) pending,
    required TResult Function(ListSubmissionStatusQuoted value) quoted,
    required TResult Function(ListSubmissionStatusCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionStatusAccepted value) accepted,
    required TResult Function(ListSubmissionStatusRejected value) rejected,
    required TResult Function(ListSubmissionStatusExpired value) expired,
    required TResult Function(ListSubmissionStatusCancelled value) cancelled,
  }) {
    return expired(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionStatusPending value)? pending,
    TResult? Function(ListSubmissionStatusQuoted value)? quoted,
    TResult? Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionStatusAccepted value)? accepted,
    TResult? Function(ListSubmissionStatusRejected value)? rejected,
    TResult? Function(ListSubmissionStatusExpired value)? expired,
    TResult? Function(ListSubmissionStatusCancelled value)? cancelled,
  }) {
    return expired?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionStatusPending value)? pending,
    TResult Function(ListSubmissionStatusQuoted value)? quoted,
    TResult Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionStatusAccepted value)? accepted,
    TResult Function(ListSubmissionStatusRejected value)? rejected,
    TResult Function(ListSubmissionStatusExpired value)? expired,
    TResult Function(ListSubmissionStatusCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (expired != null) {
      return expired(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionStatusExpiredImplToJson(this);
  }
}

abstract class ListSubmissionStatusExpired implements ListSubmissionStatus {
  const factory ListSubmissionStatusExpired({
    required final DateTime expiredAt,
  }) = _$ListSubmissionStatusExpiredImpl;

  factory ListSubmissionStatusExpired.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionStatusExpiredImpl.fromJson;

  DateTime get expiredAt;

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionStatusExpiredImplCopyWith<_$ListSubmissionStatusExpiredImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListSubmissionStatusCancelledImplCopyWith<$Res> {
  factory _$$ListSubmissionStatusCancelledImplCopyWith(
    _$ListSubmissionStatusCancelledImpl value,
    $Res Function(_$ListSubmissionStatusCancelledImpl) then,
  ) = __$$ListSubmissionStatusCancelledImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime cancelledAt, String reason});
}

/// @nodoc
class __$$ListSubmissionStatusCancelledImplCopyWithImpl<$Res>
    extends
        _$ListSubmissionStatusCopyWithImpl<
          $Res,
          _$ListSubmissionStatusCancelledImpl
        >
    implements _$$ListSubmissionStatusCancelledImplCopyWith<$Res> {
  __$$ListSubmissionStatusCancelledImplCopyWithImpl(
    _$ListSubmissionStatusCancelledImpl _value,
    $Res Function(_$ListSubmissionStatusCancelledImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? cancelledAt = null, Object? reason = null}) {
    return _then(
      _$ListSubmissionStatusCancelledImpl(
        cancelledAt: null == cancelledAt
            ? _value.cancelledAt
            : cancelledAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        reason: null == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionStatusCancelledImpl
    implements ListSubmissionStatusCancelled {
  const _$ListSubmissionStatusCancelledImpl({
    required this.cancelledAt,
    required this.reason,
    final String? $type,
  }) : $type = $type ?? 'cancelled';

  factory _$ListSubmissionStatusCancelledImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$ListSubmissionStatusCancelledImplFromJson(json);

  @override
  final DateTime cancelledAt;
  @override
  final String reason;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListSubmissionStatus.cancelled(cancelledAt: $cancelledAt, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionStatusCancelledImpl &&
            (identical(other.cancelledAt, cancelledAt) ||
                other.cancelledAt == cancelledAt) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, cancelledAt, reason);

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionStatusCancelledImplCopyWith<
    _$ListSubmissionStatusCancelledImpl
  >
  get copyWith =>
      __$$ListSubmissionStatusCancelledImplCopyWithImpl<
        _$ListSubmissionStatusCancelledImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime submittedAt) pending,
    required TResult Function(DateTime quotedAt, double amount) quoted,
    required TResult Function(DateTime counterOfferedAt, double amount)
    counterOffered,
    required TResult Function(DateTime acceptedAt, double amount) accepted,
    required TResult Function(DateTime rejectedAt, String reason) rejected,
    required TResult Function(DateTime expiredAt) expired,
    required TResult Function(DateTime cancelledAt, String reason) cancelled,
  }) {
    return cancelled(cancelledAt, reason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime submittedAt)? pending,
    TResult? Function(DateTime quotedAt, double amount)? quoted,
    TResult? Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult? Function(DateTime acceptedAt, double amount)? accepted,
    TResult? Function(DateTime rejectedAt, String reason)? rejected,
    TResult? Function(DateTime expiredAt)? expired,
    TResult? Function(DateTime cancelledAt, String reason)? cancelled,
  }) {
    return cancelled?.call(cancelledAt, reason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime submittedAt)? pending,
    TResult Function(DateTime quotedAt, double amount)? quoted,
    TResult Function(DateTime counterOfferedAt, double amount)? counterOffered,
    TResult Function(DateTime acceptedAt, double amount)? accepted,
    TResult Function(DateTime rejectedAt, String reason)? rejected,
    TResult Function(DateTime expiredAt)? expired,
    TResult Function(DateTime cancelledAt, String reason)? cancelled,
    required TResult orElse(),
  }) {
    if (cancelled != null) {
      return cancelled(cancelledAt, reason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ListSubmissionStatusPending value) pending,
    required TResult Function(ListSubmissionStatusQuoted value) quoted,
    required TResult Function(ListSubmissionStatusCounterOffered value)
    counterOffered,
    required TResult Function(ListSubmissionStatusAccepted value) accepted,
    required TResult Function(ListSubmissionStatusRejected value) rejected,
    required TResult Function(ListSubmissionStatusExpired value) expired,
    required TResult Function(ListSubmissionStatusCancelled value) cancelled,
  }) {
    return cancelled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ListSubmissionStatusPending value)? pending,
    TResult? Function(ListSubmissionStatusQuoted value)? quoted,
    TResult? Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult? Function(ListSubmissionStatusAccepted value)? accepted,
    TResult? Function(ListSubmissionStatusRejected value)? rejected,
    TResult? Function(ListSubmissionStatusExpired value)? expired,
    TResult? Function(ListSubmissionStatusCancelled value)? cancelled,
  }) {
    return cancelled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ListSubmissionStatusPending value)? pending,
    TResult Function(ListSubmissionStatusQuoted value)? quoted,
    TResult Function(ListSubmissionStatusCounterOffered value)? counterOffered,
    TResult Function(ListSubmissionStatusAccepted value)? accepted,
    TResult Function(ListSubmissionStatusRejected value)? rejected,
    TResult Function(ListSubmissionStatusExpired value)? expired,
    TResult Function(ListSubmissionStatusCancelled value)? cancelled,
    required TResult orElse(),
  }) {
    if (cancelled != null) {
      return cancelled(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionStatusCancelledImplToJson(this);
  }
}

abstract class ListSubmissionStatusCancelled implements ListSubmissionStatus {
  const factory ListSubmissionStatusCancelled({
    required final DateTime cancelledAt,
    required final String reason,
  }) = _$ListSubmissionStatusCancelledImpl;

  factory ListSubmissionStatusCancelled.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionStatusCancelledImpl.fromJson;

  DateTime get cancelledAt;
  String get reason;

  /// Create a copy of ListSubmissionStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionStatusCancelledImplCopyWith<
    _$ListSubmissionStatusCancelledImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}
