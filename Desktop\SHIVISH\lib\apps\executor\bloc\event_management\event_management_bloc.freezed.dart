// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'event_management_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$EventManagementEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPublicEvents,
    required TResult Function(String eventId) loadEventDetails,
    required TResult Function(String eventId, String executorId) approveEvent,
    required TResult Function(String eventId, String executorId, String reason)
    rejectEvent,
    required TResult Function(EventModel event) createPublicEvent,
    required TResult Function(EventModel event) updatePublicEvent,
    required TResult Function(String eventId) deletePublicEvent,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPublicEvents,
    TResult? Function(String eventId)? loadEventDetails,
    TResult? Function(String eventId, String executorId)? approveEvent,
    TResult? Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult? Function(EventModel event)? createPublicEvent,
    TResult? Function(EventModel event)? updatePublicEvent,
    TResult? Function(String eventId)? deletePublicEvent,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPublicEvents,
    TResult Function(String eventId)? loadEventDetails,
    TResult Function(String eventId, String executorId)? approveEvent,
    TResult Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult Function(EventModel event)? createPublicEvent,
    TResult Function(EventModel event)? updatePublicEvent,
    TResult Function(String eventId)? deletePublicEvent,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPublicEvents value) loadPublicEvents,
    required TResult Function(LoadEventDetails value) loadEventDetails,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreatePublicEvent value) createPublicEvent,
    required TResult Function(UpdatePublicEvent value) updatePublicEvent,
    required TResult Function(DeletePublicEvent value) deletePublicEvent,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPublicEvents value)? loadPublicEvents,
    TResult? Function(LoadEventDetails value)? loadEventDetails,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreatePublicEvent value)? createPublicEvent,
    TResult? Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult? Function(DeletePublicEvent value)? deletePublicEvent,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPublicEvents value)? loadPublicEvents,
    TResult Function(LoadEventDetails value)? loadEventDetails,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreatePublicEvent value)? createPublicEvent,
    TResult Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult Function(DeletePublicEvent value)? deletePublicEvent,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EventManagementEventCopyWith<$Res> {
  factory $EventManagementEventCopyWith(
    EventManagementEvent value,
    $Res Function(EventManagementEvent) then,
  ) = _$EventManagementEventCopyWithImpl<$Res, EventManagementEvent>;
}

/// @nodoc
class _$EventManagementEventCopyWithImpl<
  $Res,
  $Val extends EventManagementEvent
>
    implements $EventManagementEventCopyWith<$Res> {
  _$EventManagementEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadPublicEventsImplCopyWith<$Res> {
  factory _$$LoadPublicEventsImplCopyWith(
    _$LoadPublicEventsImpl value,
    $Res Function(_$LoadPublicEventsImpl) then,
  ) = __$$LoadPublicEventsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadPublicEventsImplCopyWithImpl<$Res>
    extends _$EventManagementEventCopyWithImpl<$Res, _$LoadPublicEventsImpl>
    implements _$$LoadPublicEventsImplCopyWith<$Res> {
  __$$LoadPublicEventsImplCopyWithImpl(
    _$LoadPublicEventsImpl _value,
    $Res Function(_$LoadPublicEventsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadPublicEventsImpl implements LoadPublicEvents {
  const _$LoadPublicEventsImpl();

  @override
  String toString() {
    return 'EventManagementEvent.loadPublicEvents()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadPublicEventsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPublicEvents,
    required TResult Function(String eventId) loadEventDetails,
    required TResult Function(String eventId, String executorId) approveEvent,
    required TResult Function(String eventId, String executorId, String reason)
    rejectEvent,
    required TResult Function(EventModel event) createPublicEvent,
    required TResult Function(EventModel event) updatePublicEvent,
    required TResult Function(String eventId) deletePublicEvent,
  }) {
    return loadPublicEvents();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPublicEvents,
    TResult? Function(String eventId)? loadEventDetails,
    TResult? Function(String eventId, String executorId)? approveEvent,
    TResult? Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult? Function(EventModel event)? createPublicEvent,
    TResult? Function(EventModel event)? updatePublicEvent,
    TResult? Function(String eventId)? deletePublicEvent,
  }) {
    return loadPublicEvents?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPublicEvents,
    TResult Function(String eventId)? loadEventDetails,
    TResult Function(String eventId, String executorId)? approveEvent,
    TResult Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult Function(EventModel event)? createPublicEvent,
    TResult Function(EventModel event)? updatePublicEvent,
    TResult Function(String eventId)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (loadPublicEvents != null) {
      return loadPublicEvents();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPublicEvents value) loadPublicEvents,
    required TResult Function(LoadEventDetails value) loadEventDetails,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreatePublicEvent value) createPublicEvent,
    required TResult Function(UpdatePublicEvent value) updatePublicEvent,
    required TResult Function(DeletePublicEvent value) deletePublicEvent,
  }) {
    return loadPublicEvents(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPublicEvents value)? loadPublicEvents,
    TResult? Function(LoadEventDetails value)? loadEventDetails,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreatePublicEvent value)? createPublicEvent,
    TResult? Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult? Function(DeletePublicEvent value)? deletePublicEvent,
  }) {
    return loadPublicEvents?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPublicEvents value)? loadPublicEvents,
    TResult Function(LoadEventDetails value)? loadEventDetails,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreatePublicEvent value)? createPublicEvent,
    TResult Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult Function(DeletePublicEvent value)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (loadPublicEvents != null) {
      return loadPublicEvents(this);
    }
    return orElse();
  }
}

abstract class LoadPublicEvents implements EventManagementEvent {
  const factory LoadPublicEvents() = _$LoadPublicEventsImpl;
}

/// @nodoc
abstract class _$$LoadEventDetailsImplCopyWith<$Res> {
  factory _$$LoadEventDetailsImplCopyWith(
    _$LoadEventDetailsImpl value,
    $Res Function(_$LoadEventDetailsImpl) then,
  ) = __$$LoadEventDetailsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String eventId});
}

/// @nodoc
class __$$LoadEventDetailsImplCopyWithImpl<$Res>
    extends _$EventManagementEventCopyWithImpl<$Res, _$LoadEventDetailsImpl>
    implements _$$LoadEventDetailsImplCopyWith<$Res> {
  __$$LoadEventDetailsImplCopyWithImpl(
    _$LoadEventDetailsImpl _value,
    $Res Function(_$LoadEventDetailsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? eventId = null}) {
    return _then(
      _$LoadEventDetailsImpl(
        eventId: null == eventId
            ? _value.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$LoadEventDetailsImpl implements LoadEventDetails {
  const _$LoadEventDetailsImpl({required this.eventId});

  @override
  final String eventId;

  @override
  String toString() {
    return 'EventManagementEvent.loadEventDetails(eventId: $eventId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadEventDetailsImpl &&
            (identical(other.eventId, eventId) || other.eventId == eventId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, eventId);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadEventDetailsImplCopyWith<_$LoadEventDetailsImpl> get copyWith =>
      __$$LoadEventDetailsImplCopyWithImpl<_$LoadEventDetailsImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPublicEvents,
    required TResult Function(String eventId) loadEventDetails,
    required TResult Function(String eventId, String executorId) approveEvent,
    required TResult Function(String eventId, String executorId, String reason)
    rejectEvent,
    required TResult Function(EventModel event) createPublicEvent,
    required TResult Function(EventModel event) updatePublicEvent,
    required TResult Function(String eventId) deletePublicEvent,
  }) {
    return loadEventDetails(eventId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPublicEvents,
    TResult? Function(String eventId)? loadEventDetails,
    TResult? Function(String eventId, String executorId)? approveEvent,
    TResult? Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult? Function(EventModel event)? createPublicEvent,
    TResult? Function(EventModel event)? updatePublicEvent,
    TResult? Function(String eventId)? deletePublicEvent,
  }) {
    return loadEventDetails?.call(eventId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPublicEvents,
    TResult Function(String eventId)? loadEventDetails,
    TResult Function(String eventId, String executorId)? approveEvent,
    TResult Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult Function(EventModel event)? createPublicEvent,
    TResult Function(EventModel event)? updatePublicEvent,
    TResult Function(String eventId)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (loadEventDetails != null) {
      return loadEventDetails(eventId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPublicEvents value) loadPublicEvents,
    required TResult Function(LoadEventDetails value) loadEventDetails,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreatePublicEvent value) createPublicEvent,
    required TResult Function(UpdatePublicEvent value) updatePublicEvent,
    required TResult Function(DeletePublicEvent value) deletePublicEvent,
  }) {
    return loadEventDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPublicEvents value)? loadPublicEvents,
    TResult? Function(LoadEventDetails value)? loadEventDetails,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreatePublicEvent value)? createPublicEvent,
    TResult? Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult? Function(DeletePublicEvent value)? deletePublicEvent,
  }) {
    return loadEventDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPublicEvents value)? loadPublicEvents,
    TResult Function(LoadEventDetails value)? loadEventDetails,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreatePublicEvent value)? createPublicEvent,
    TResult Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult Function(DeletePublicEvent value)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (loadEventDetails != null) {
      return loadEventDetails(this);
    }
    return orElse();
  }
}

abstract class LoadEventDetails implements EventManagementEvent {
  const factory LoadEventDetails({required final String eventId}) =
      _$LoadEventDetailsImpl;

  String get eventId;

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadEventDetailsImplCopyWith<_$LoadEventDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ApproveEventImplCopyWith<$Res> {
  factory _$$ApproveEventImplCopyWith(
    _$ApproveEventImpl value,
    $Res Function(_$ApproveEventImpl) then,
  ) = __$$ApproveEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String eventId, String executorId});
}

/// @nodoc
class __$$ApproveEventImplCopyWithImpl<$Res>
    extends _$EventManagementEventCopyWithImpl<$Res, _$ApproveEventImpl>
    implements _$$ApproveEventImplCopyWith<$Res> {
  __$$ApproveEventImplCopyWithImpl(
    _$ApproveEventImpl _value,
    $Res Function(_$ApproveEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? eventId = null, Object? executorId = null}) {
    return _then(
      _$ApproveEventImpl(
        eventId: null == eventId
            ? _value.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
        executorId: null == executorId
            ? _value.executorId
            : executorId // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$ApproveEventImpl implements ApproveEvent {
  const _$ApproveEventImpl({required this.eventId, required this.executorId});

  @override
  final String eventId;
  @override
  final String executorId;

  @override
  String toString() {
    return 'EventManagementEvent.approveEvent(eventId: $eventId, executorId: $executorId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApproveEventImpl &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.executorId, executorId) ||
                other.executorId == executorId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, eventId, executorId);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApproveEventImplCopyWith<_$ApproveEventImpl> get copyWith =>
      __$$ApproveEventImplCopyWithImpl<_$ApproveEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPublicEvents,
    required TResult Function(String eventId) loadEventDetails,
    required TResult Function(String eventId, String executorId) approveEvent,
    required TResult Function(String eventId, String executorId, String reason)
    rejectEvent,
    required TResult Function(EventModel event) createPublicEvent,
    required TResult Function(EventModel event) updatePublicEvent,
    required TResult Function(String eventId) deletePublicEvent,
  }) {
    return approveEvent(eventId, executorId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPublicEvents,
    TResult? Function(String eventId)? loadEventDetails,
    TResult? Function(String eventId, String executorId)? approveEvent,
    TResult? Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult? Function(EventModel event)? createPublicEvent,
    TResult? Function(EventModel event)? updatePublicEvent,
    TResult? Function(String eventId)? deletePublicEvent,
  }) {
    return approveEvent?.call(eventId, executorId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPublicEvents,
    TResult Function(String eventId)? loadEventDetails,
    TResult Function(String eventId, String executorId)? approveEvent,
    TResult Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult Function(EventModel event)? createPublicEvent,
    TResult Function(EventModel event)? updatePublicEvent,
    TResult Function(String eventId)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (approveEvent != null) {
      return approveEvent(eventId, executorId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPublicEvents value) loadPublicEvents,
    required TResult Function(LoadEventDetails value) loadEventDetails,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreatePublicEvent value) createPublicEvent,
    required TResult Function(UpdatePublicEvent value) updatePublicEvent,
    required TResult Function(DeletePublicEvent value) deletePublicEvent,
  }) {
    return approveEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPublicEvents value)? loadPublicEvents,
    TResult? Function(LoadEventDetails value)? loadEventDetails,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreatePublicEvent value)? createPublicEvent,
    TResult? Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult? Function(DeletePublicEvent value)? deletePublicEvent,
  }) {
    return approveEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPublicEvents value)? loadPublicEvents,
    TResult Function(LoadEventDetails value)? loadEventDetails,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreatePublicEvent value)? createPublicEvent,
    TResult Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult Function(DeletePublicEvent value)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (approveEvent != null) {
      return approveEvent(this);
    }
    return orElse();
  }
}

abstract class ApproveEvent implements EventManagementEvent {
  const factory ApproveEvent({
    required final String eventId,
    required final String executorId,
  }) = _$ApproveEventImpl;

  String get eventId;
  String get executorId;

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApproveEventImplCopyWith<_$ApproveEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RejectEventImplCopyWith<$Res> {
  factory _$$RejectEventImplCopyWith(
    _$RejectEventImpl value,
    $Res Function(_$RejectEventImpl) then,
  ) = __$$RejectEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String eventId, String executorId, String reason});
}

/// @nodoc
class __$$RejectEventImplCopyWithImpl<$Res>
    extends _$EventManagementEventCopyWithImpl<$Res, _$RejectEventImpl>
    implements _$$RejectEventImplCopyWith<$Res> {
  __$$RejectEventImplCopyWithImpl(
    _$RejectEventImpl _value,
    $Res Function(_$RejectEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eventId = null,
    Object? executorId = null,
    Object? reason = null,
  }) {
    return _then(
      _$RejectEventImpl(
        eventId: null == eventId
            ? _value.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
        executorId: null == executorId
            ? _value.executorId
            : executorId // ignore: cast_nullable_to_non_nullable
                  as String,
        reason: null == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$RejectEventImpl implements RejectEvent {
  const _$RejectEventImpl({
    required this.eventId,
    required this.executorId,
    required this.reason,
  });

  @override
  final String eventId;
  @override
  final String executorId;
  @override
  final String reason;

  @override
  String toString() {
    return 'EventManagementEvent.rejectEvent(eventId: $eventId, executorId: $executorId, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RejectEventImpl &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.executorId, executorId) ||
                other.executorId == executorId) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, eventId, executorId, reason);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RejectEventImplCopyWith<_$RejectEventImpl> get copyWith =>
      __$$RejectEventImplCopyWithImpl<_$RejectEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPublicEvents,
    required TResult Function(String eventId) loadEventDetails,
    required TResult Function(String eventId, String executorId) approveEvent,
    required TResult Function(String eventId, String executorId, String reason)
    rejectEvent,
    required TResult Function(EventModel event) createPublicEvent,
    required TResult Function(EventModel event) updatePublicEvent,
    required TResult Function(String eventId) deletePublicEvent,
  }) {
    return rejectEvent(eventId, executorId, reason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPublicEvents,
    TResult? Function(String eventId)? loadEventDetails,
    TResult? Function(String eventId, String executorId)? approveEvent,
    TResult? Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult? Function(EventModel event)? createPublicEvent,
    TResult? Function(EventModel event)? updatePublicEvent,
    TResult? Function(String eventId)? deletePublicEvent,
  }) {
    return rejectEvent?.call(eventId, executorId, reason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPublicEvents,
    TResult Function(String eventId)? loadEventDetails,
    TResult Function(String eventId, String executorId)? approveEvent,
    TResult Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult Function(EventModel event)? createPublicEvent,
    TResult Function(EventModel event)? updatePublicEvent,
    TResult Function(String eventId)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (rejectEvent != null) {
      return rejectEvent(eventId, executorId, reason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPublicEvents value) loadPublicEvents,
    required TResult Function(LoadEventDetails value) loadEventDetails,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreatePublicEvent value) createPublicEvent,
    required TResult Function(UpdatePublicEvent value) updatePublicEvent,
    required TResult Function(DeletePublicEvent value) deletePublicEvent,
  }) {
    return rejectEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPublicEvents value)? loadPublicEvents,
    TResult? Function(LoadEventDetails value)? loadEventDetails,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreatePublicEvent value)? createPublicEvent,
    TResult? Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult? Function(DeletePublicEvent value)? deletePublicEvent,
  }) {
    return rejectEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPublicEvents value)? loadPublicEvents,
    TResult Function(LoadEventDetails value)? loadEventDetails,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreatePublicEvent value)? createPublicEvent,
    TResult Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult Function(DeletePublicEvent value)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (rejectEvent != null) {
      return rejectEvent(this);
    }
    return orElse();
  }
}

abstract class RejectEvent implements EventManagementEvent {
  const factory RejectEvent({
    required final String eventId,
    required final String executorId,
    required final String reason,
  }) = _$RejectEventImpl;

  String get eventId;
  String get executorId;
  String get reason;

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RejectEventImplCopyWith<_$RejectEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreatePublicEventImplCopyWith<$Res> {
  factory _$$CreatePublicEventImplCopyWith(
    _$CreatePublicEventImpl value,
    $Res Function(_$CreatePublicEventImpl) then,
  ) = __$$CreatePublicEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({EventModel event});

  $EventModelCopyWith<$Res> get event;
}

/// @nodoc
class __$$CreatePublicEventImplCopyWithImpl<$Res>
    extends _$EventManagementEventCopyWithImpl<$Res, _$CreatePublicEventImpl>
    implements _$$CreatePublicEventImplCopyWith<$Res> {
  __$$CreatePublicEventImplCopyWithImpl(
    _$CreatePublicEventImpl _value,
    $Res Function(_$CreatePublicEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? event = null}) {
    return _then(
      _$CreatePublicEventImpl(
        event: null == event
            ? _value.event
            : event // ignore: cast_nullable_to_non_nullable
                  as EventModel,
      ),
    );
  }

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EventModelCopyWith<$Res> get event {
    return $EventModelCopyWith<$Res>(_value.event, (value) {
      return _then(_value.copyWith(event: value));
    });
  }
}

/// @nodoc

class _$CreatePublicEventImpl implements CreatePublicEvent {
  const _$CreatePublicEventImpl({required this.event});

  @override
  final EventModel event;

  @override
  String toString() {
    return 'EventManagementEvent.createPublicEvent(event: $event)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreatePublicEventImpl &&
            (identical(other.event, event) || other.event == event));
  }

  @override
  int get hashCode => Object.hash(runtimeType, event);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreatePublicEventImplCopyWith<_$CreatePublicEventImpl> get copyWith =>
      __$$CreatePublicEventImplCopyWithImpl<_$CreatePublicEventImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPublicEvents,
    required TResult Function(String eventId) loadEventDetails,
    required TResult Function(String eventId, String executorId) approveEvent,
    required TResult Function(String eventId, String executorId, String reason)
    rejectEvent,
    required TResult Function(EventModel event) createPublicEvent,
    required TResult Function(EventModel event) updatePublicEvent,
    required TResult Function(String eventId) deletePublicEvent,
  }) {
    return createPublicEvent(event);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPublicEvents,
    TResult? Function(String eventId)? loadEventDetails,
    TResult? Function(String eventId, String executorId)? approveEvent,
    TResult? Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult? Function(EventModel event)? createPublicEvent,
    TResult? Function(EventModel event)? updatePublicEvent,
    TResult? Function(String eventId)? deletePublicEvent,
  }) {
    return createPublicEvent?.call(event);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPublicEvents,
    TResult Function(String eventId)? loadEventDetails,
    TResult Function(String eventId, String executorId)? approveEvent,
    TResult Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult Function(EventModel event)? createPublicEvent,
    TResult Function(EventModel event)? updatePublicEvent,
    TResult Function(String eventId)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (createPublicEvent != null) {
      return createPublicEvent(event);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPublicEvents value) loadPublicEvents,
    required TResult Function(LoadEventDetails value) loadEventDetails,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreatePublicEvent value) createPublicEvent,
    required TResult Function(UpdatePublicEvent value) updatePublicEvent,
    required TResult Function(DeletePublicEvent value) deletePublicEvent,
  }) {
    return createPublicEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPublicEvents value)? loadPublicEvents,
    TResult? Function(LoadEventDetails value)? loadEventDetails,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreatePublicEvent value)? createPublicEvent,
    TResult? Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult? Function(DeletePublicEvent value)? deletePublicEvent,
  }) {
    return createPublicEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPublicEvents value)? loadPublicEvents,
    TResult Function(LoadEventDetails value)? loadEventDetails,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreatePublicEvent value)? createPublicEvent,
    TResult Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult Function(DeletePublicEvent value)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (createPublicEvent != null) {
      return createPublicEvent(this);
    }
    return orElse();
  }
}

abstract class CreatePublicEvent implements EventManagementEvent {
  const factory CreatePublicEvent({required final EventModel event}) =
      _$CreatePublicEventImpl;

  EventModel get event;

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreatePublicEventImplCopyWith<_$CreatePublicEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatePublicEventImplCopyWith<$Res> {
  factory _$$UpdatePublicEventImplCopyWith(
    _$UpdatePublicEventImpl value,
    $Res Function(_$UpdatePublicEventImpl) then,
  ) = __$$UpdatePublicEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({EventModel event});

  $EventModelCopyWith<$Res> get event;
}

/// @nodoc
class __$$UpdatePublicEventImplCopyWithImpl<$Res>
    extends _$EventManagementEventCopyWithImpl<$Res, _$UpdatePublicEventImpl>
    implements _$$UpdatePublicEventImplCopyWith<$Res> {
  __$$UpdatePublicEventImplCopyWithImpl(
    _$UpdatePublicEventImpl _value,
    $Res Function(_$UpdatePublicEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? event = null}) {
    return _then(
      _$UpdatePublicEventImpl(
        event: null == event
            ? _value.event
            : event // ignore: cast_nullable_to_non_nullable
                  as EventModel,
      ),
    );
  }

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EventModelCopyWith<$Res> get event {
    return $EventModelCopyWith<$Res>(_value.event, (value) {
      return _then(_value.copyWith(event: value));
    });
  }
}

/// @nodoc

class _$UpdatePublicEventImpl implements UpdatePublicEvent {
  const _$UpdatePublicEventImpl({required this.event});

  @override
  final EventModel event;

  @override
  String toString() {
    return 'EventManagementEvent.updatePublicEvent(event: $event)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatePublicEventImpl &&
            (identical(other.event, event) || other.event == event));
  }

  @override
  int get hashCode => Object.hash(runtimeType, event);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatePublicEventImplCopyWith<_$UpdatePublicEventImpl> get copyWith =>
      __$$UpdatePublicEventImplCopyWithImpl<_$UpdatePublicEventImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPublicEvents,
    required TResult Function(String eventId) loadEventDetails,
    required TResult Function(String eventId, String executorId) approveEvent,
    required TResult Function(String eventId, String executorId, String reason)
    rejectEvent,
    required TResult Function(EventModel event) createPublicEvent,
    required TResult Function(EventModel event) updatePublicEvent,
    required TResult Function(String eventId) deletePublicEvent,
  }) {
    return updatePublicEvent(event);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPublicEvents,
    TResult? Function(String eventId)? loadEventDetails,
    TResult? Function(String eventId, String executorId)? approveEvent,
    TResult? Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult? Function(EventModel event)? createPublicEvent,
    TResult? Function(EventModel event)? updatePublicEvent,
    TResult? Function(String eventId)? deletePublicEvent,
  }) {
    return updatePublicEvent?.call(event);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPublicEvents,
    TResult Function(String eventId)? loadEventDetails,
    TResult Function(String eventId, String executorId)? approveEvent,
    TResult Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult Function(EventModel event)? createPublicEvent,
    TResult Function(EventModel event)? updatePublicEvent,
    TResult Function(String eventId)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (updatePublicEvent != null) {
      return updatePublicEvent(event);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPublicEvents value) loadPublicEvents,
    required TResult Function(LoadEventDetails value) loadEventDetails,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreatePublicEvent value) createPublicEvent,
    required TResult Function(UpdatePublicEvent value) updatePublicEvent,
    required TResult Function(DeletePublicEvent value) deletePublicEvent,
  }) {
    return updatePublicEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPublicEvents value)? loadPublicEvents,
    TResult? Function(LoadEventDetails value)? loadEventDetails,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreatePublicEvent value)? createPublicEvent,
    TResult? Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult? Function(DeletePublicEvent value)? deletePublicEvent,
  }) {
    return updatePublicEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPublicEvents value)? loadPublicEvents,
    TResult Function(LoadEventDetails value)? loadEventDetails,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreatePublicEvent value)? createPublicEvent,
    TResult Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult Function(DeletePublicEvent value)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (updatePublicEvent != null) {
      return updatePublicEvent(this);
    }
    return orElse();
  }
}

abstract class UpdatePublicEvent implements EventManagementEvent {
  const factory UpdatePublicEvent({required final EventModel event}) =
      _$UpdatePublicEventImpl;

  EventModel get event;

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatePublicEventImplCopyWith<_$UpdatePublicEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeletePublicEventImplCopyWith<$Res> {
  factory _$$DeletePublicEventImplCopyWith(
    _$DeletePublicEventImpl value,
    $Res Function(_$DeletePublicEventImpl) then,
  ) = __$$DeletePublicEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String eventId});
}

/// @nodoc
class __$$DeletePublicEventImplCopyWithImpl<$Res>
    extends _$EventManagementEventCopyWithImpl<$Res, _$DeletePublicEventImpl>
    implements _$$DeletePublicEventImplCopyWith<$Res> {
  __$$DeletePublicEventImplCopyWithImpl(
    _$DeletePublicEventImpl _value,
    $Res Function(_$DeletePublicEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? eventId = null}) {
    return _then(
      _$DeletePublicEventImpl(
        eventId: null == eventId
            ? _value.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$DeletePublicEventImpl implements DeletePublicEvent {
  const _$DeletePublicEventImpl({required this.eventId});

  @override
  final String eventId;

  @override
  String toString() {
    return 'EventManagementEvent.deletePublicEvent(eventId: $eventId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeletePublicEventImpl &&
            (identical(other.eventId, eventId) || other.eventId == eventId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, eventId);

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeletePublicEventImplCopyWith<_$DeletePublicEventImpl> get copyWith =>
      __$$DeletePublicEventImplCopyWithImpl<_$DeletePublicEventImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPublicEvents,
    required TResult Function(String eventId) loadEventDetails,
    required TResult Function(String eventId, String executorId) approveEvent,
    required TResult Function(String eventId, String executorId, String reason)
    rejectEvent,
    required TResult Function(EventModel event) createPublicEvent,
    required TResult Function(EventModel event) updatePublicEvent,
    required TResult Function(String eventId) deletePublicEvent,
  }) {
    return deletePublicEvent(eventId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPublicEvents,
    TResult? Function(String eventId)? loadEventDetails,
    TResult? Function(String eventId, String executorId)? approveEvent,
    TResult? Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult? Function(EventModel event)? createPublicEvent,
    TResult? Function(EventModel event)? updatePublicEvent,
    TResult? Function(String eventId)? deletePublicEvent,
  }) {
    return deletePublicEvent?.call(eventId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPublicEvents,
    TResult Function(String eventId)? loadEventDetails,
    TResult Function(String eventId, String executorId)? approveEvent,
    TResult Function(String eventId, String executorId, String reason)?
    rejectEvent,
    TResult Function(EventModel event)? createPublicEvent,
    TResult Function(EventModel event)? updatePublicEvent,
    TResult Function(String eventId)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (deletePublicEvent != null) {
      return deletePublicEvent(eventId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPublicEvents value) loadPublicEvents,
    required TResult Function(LoadEventDetails value) loadEventDetails,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreatePublicEvent value) createPublicEvent,
    required TResult Function(UpdatePublicEvent value) updatePublicEvent,
    required TResult Function(DeletePublicEvent value) deletePublicEvent,
  }) {
    return deletePublicEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPublicEvents value)? loadPublicEvents,
    TResult? Function(LoadEventDetails value)? loadEventDetails,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreatePublicEvent value)? createPublicEvent,
    TResult? Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult? Function(DeletePublicEvent value)? deletePublicEvent,
  }) {
    return deletePublicEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPublicEvents value)? loadPublicEvents,
    TResult Function(LoadEventDetails value)? loadEventDetails,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreatePublicEvent value)? createPublicEvent,
    TResult Function(UpdatePublicEvent value)? updatePublicEvent,
    TResult Function(DeletePublicEvent value)? deletePublicEvent,
    required TResult orElse(),
  }) {
    if (deletePublicEvent != null) {
      return deletePublicEvent(this);
    }
    return orElse();
  }
}

abstract class DeletePublicEvent implements EventManagementEvent {
  const factory DeletePublicEvent({required final String eventId}) =
      _$DeletePublicEventImpl;

  String get eventId;

  /// Create a copy of EventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeletePublicEventImplCopyWith<_$DeletePublicEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$EventManagementState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EventManagementStateCopyWith<$Res> {
  factory $EventManagementStateCopyWith(
    EventManagementState value,
    $Res Function(EventManagementState) then,
  ) = _$EventManagementStateCopyWithImpl<$Res, EventManagementState>;
}

/// @nodoc
class _$EventManagementStateCopyWithImpl<
  $Res,
  $Val extends EventManagementState
>
    implements $EventManagementStateCopyWith<$Res> {
  _$EventManagementStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
    _$InitialImpl value,
    $Res Function(_$InitialImpl) then,
  ) = __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
    _$InitialImpl _value,
    $Res Function(_$InitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'EventManagementState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements EventManagementState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
    _$LoadingImpl value,
    $Res Function(_$LoadingImpl) then,
  ) = __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
    _$LoadingImpl _value,
    $Res Function(_$LoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'EventManagementState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements EventManagementState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
    _$ErrorImpl value,
    $Res Function(_$ErrorImpl) then,
  ) = __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
    _$ErrorImpl _value,
    $Res Function(_$ErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$ErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'EventManagementState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements EventManagementState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedPublicEventsImplCopyWith<$Res> {
  factory _$$LoadedPublicEventsImplCopyWith(
    _$LoadedPublicEventsImpl value,
    $Res Function(_$LoadedPublicEventsImpl) then,
  ) = __$$LoadedPublicEventsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<EventModel> events});
}

/// @nodoc
class __$$LoadedPublicEventsImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$LoadedPublicEventsImpl>
    implements _$$LoadedPublicEventsImplCopyWith<$Res> {
  __$$LoadedPublicEventsImplCopyWithImpl(
    _$LoadedPublicEventsImpl _value,
    $Res Function(_$LoadedPublicEventsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? events = null}) {
    return _then(
      _$LoadedPublicEventsImpl(
        events: null == events
            ? _value._events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<EventModel>,
      ),
    );
  }
}

/// @nodoc

class _$LoadedPublicEventsImpl implements _LoadedPublicEvents {
  const _$LoadedPublicEventsImpl({required final List<EventModel> events})
    : _events = events;

  final List<EventModel> _events;
  @override
  List<EventModel> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  @override
  String toString() {
    return 'EventManagementState.loadedPublicEvents(events: $events)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedPublicEventsImpl &&
            const DeepCollectionEquality().equals(other._events, _events));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_events));

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedPublicEventsImplCopyWith<_$LoadedPublicEventsImpl> get copyWith =>
      __$$LoadedPublicEventsImplCopyWithImpl<_$LoadedPublicEventsImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return loadedPublicEvents(events);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return loadedPublicEvents?.call(events);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (loadedPublicEvents != null) {
      return loadedPublicEvents(events);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return loadedPublicEvents(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return loadedPublicEvents?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (loadedPublicEvents != null) {
      return loadedPublicEvents(this);
    }
    return orElse();
  }
}

abstract class _LoadedPublicEvents implements EventManagementState {
  const factory _LoadedPublicEvents({required final List<EventModel> events}) =
      _$LoadedPublicEventsImpl;

  List<EventModel> get events;

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedPublicEventsImplCopyWith<_$LoadedPublicEventsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedEventDetailsImplCopyWith<$Res> {
  factory _$$LoadedEventDetailsImplCopyWith(
    _$LoadedEventDetailsImpl value,
    $Res Function(_$LoadedEventDetailsImpl) then,
  ) = __$$LoadedEventDetailsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({EventModel event});

  $EventModelCopyWith<$Res> get event;
}

/// @nodoc
class __$$LoadedEventDetailsImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$LoadedEventDetailsImpl>
    implements _$$LoadedEventDetailsImplCopyWith<$Res> {
  __$$LoadedEventDetailsImplCopyWithImpl(
    _$LoadedEventDetailsImpl _value,
    $Res Function(_$LoadedEventDetailsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? event = null}) {
    return _then(
      _$LoadedEventDetailsImpl(
        event: null == event
            ? _value.event
            : event // ignore: cast_nullable_to_non_nullable
                  as EventModel,
      ),
    );
  }

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EventModelCopyWith<$Res> get event {
    return $EventModelCopyWith<$Res>(_value.event, (value) {
      return _then(_value.copyWith(event: value));
    });
  }
}

/// @nodoc

class _$LoadedEventDetailsImpl implements _LoadedEventDetails {
  const _$LoadedEventDetailsImpl({required this.event});

  @override
  final EventModel event;

  @override
  String toString() {
    return 'EventManagementState.loadedEventDetails(event: $event)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedEventDetailsImpl &&
            (identical(other.event, event) || other.event == event));
  }

  @override
  int get hashCode => Object.hash(runtimeType, event);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedEventDetailsImplCopyWith<_$LoadedEventDetailsImpl> get copyWith =>
      __$$LoadedEventDetailsImplCopyWithImpl<_$LoadedEventDetailsImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return loadedEventDetails(event);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return loadedEventDetails?.call(event);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (loadedEventDetails != null) {
      return loadedEventDetails(event);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return loadedEventDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return loadedEventDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (loadedEventDetails != null) {
      return loadedEventDetails(this);
    }
    return orElse();
  }
}

abstract class _LoadedEventDetails implements EventManagementState {
  const factory _LoadedEventDetails({required final EventModel event}) =
      _$LoadedEventDetailsImpl;

  EventModel get event;

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedEventDetailsImplCopyWith<_$LoadedEventDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EventApprovedImplCopyWith<$Res> {
  factory _$$EventApprovedImplCopyWith(
    _$EventApprovedImpl value,
    $Res Function(_$EventApprovedImpl) then,
  ) = __$$EventApprovedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EventApprovedImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$EventApprovedImpl>
    implements _$$EventApprovedImplCopyWith<$Res> {
  __$$EventApprovedImplCopyWithImpl(
    _$EventApprovedImpl _value,
    $Res Function(_$EventApprovedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EventApprovedImpl implements _EventApproved {
  const _$EventApprovedImpl();

  @override
  String toString() {
    return 'EventManagementState.eventApproved()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EventApprovedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return eventApproved();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return eventApproved?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventApproved != null) {
      return eventApproved();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return eventApproved(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return eventApproved?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventApproved != null) {
      return eventApproved(this);
    }
    return orElse();
  }
}

abstract class _EventApproved implements EventManagementState {
  const factory _EventApproved() = _$EventApprovedImpl;
}

/// @nodoc
abstract class _$$EventRejectedImplCopyWith<$Res> {
  factory _$$EventRejectedImplCopyWith(
    _$EventRejectedImpl value,
    $Res Function(_$EventRejectedImpl) then,
  ) = __$$EventRejectedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EventRejectedImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$EventRejectedImpl>
    implements _$$EventRejectedImplCopyWith<$Res> {
  __$$EventRejectedImplCopyWithImpl(
    _$EventRejectedImpl _value,
    $Res Function(_$EventRejectedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EventRejectedImpl implements _EventRejected {
  const _$EventRejectedImpl();

  @override
  String toString() {
    return 'EventManagementState.eventRejected()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EventRejectedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return eventRejected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return eventRejected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventRejected != null) {
      return eventRejected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return eventRejected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return eventRejected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventRejected != null) {
      return eventRejected(this);
    }
    return orElse();
  }
}

abstract class _EventRejected implements EventManagementState {
  const factory _EventRejected() = _$EventRejectedImpl;
}

/// @nodoc
abstract class _$$EventCreatedImplCopyWith<$Res> {
  factory _$$EventCreatedImplCopyWith(
    _$EventCreatedImpl value,
    $Res Function(_$EventCreatedImpl) then,
  ) = __$$EventCreatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EventCreatedImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$EventCreatedImpl>
    implements _$$EventCreatedImplCopyWith<$Res> {
  __$$EventCreatedImplCopyWithImpl(
    _$EventCreatedImpl _value,
    $Res Function(_$EventCreatedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EventCreatedImpl implements _EventCreated {
  const _$EventCreatedImpl();

  @override
  String toString() {
    return 'EventManagementState.eventCreated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EventCreatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return eventCreated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return eventCreated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventCreated != null) {
      return eventCreated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return eventCreated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return eventCreated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventCreated != null) {
      return eventCreated(this);
    }
    return orElse();
  }
}

abstract class _EventCreated implements EventManagementState {
  const factory _EventCreated() = _$EventCreatedImpl;
}

/// @nodoc
abstract class _$$EventUpdatedImplCopyWith<$Res> {
  factory _$$EventUpdatedImplCopyWith(
    _$EventUpdatedImpl value,
    $Res Function(_$EventUpdatedImpl) then,
  ) = __$$EventUpdatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EventUpdatedImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$EventUpdatedImpl>
    implements _$$EventUpdatedImplCopyWith<$Res> {
  __$$EventUpdatedImplCopyWithImpl(
    _$EventUpdatedImpl _value,
    $Res Function(_$EventUpdatedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EventUpdatedImpl implements _EventUpdated {
  const _$EventUpdatedImpl();

  @override
  String toString() {
    return 'EventManagementState.eventUpdated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EventUpdatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return eventUpdated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return eventUpdated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventUpdated != null) {
      return eventUpdated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return eventUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return eventUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventUpdated != null) {
      return eventUpdated(this);
    }
    return orElse();
  }
}

abstract class _EventUpdated implements EventManagementState {
  const factory _EventUpdated() = _$EventUpdatedImpl;
}

/// @nodoc
abstract class _$$EventDeletedImplCopyWith<$Res> {
  factory _$$EventDeletedImplCopyWith(
    _$EventDeletedImpl value,
    $Res Function(_$EventDeletedImpl) then,
  ) = __$$EventDeletedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EventDeletedImplCopyWithImpl<$Res>
    extends _$EventManagementStateCopyWithImpl<$Res, _$EventDeletedImpl>
    implements _$$EventDeletedImplCopyWith<$Res> {
  __$$EventDeletedImplCopyWithImpl(
    _$EventDeletedImpl _value,
    $Res Function(_$EventDeletedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EventDeletedImpl implements _EventDeleted {
  const _$EventDeletedImpl();

  @override
  String toString() {
    return 'EventManagementState.eventDeleted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EventDeletedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPublicEvents,
    required TResult Function(EventModel event) loadedEventDetails,
    required TResult Function() eventApproved,
    required TResult Function() eventRejected,
    required TResult Function() eventCreated,
    required TResult Function() eventUpdated,
    required TResult Function() eventDeleted,
  }) {
    return eventDeleted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPublicEvents,
    TResult? Function(EventModel event)? loadedEventDetails,
    TResult? Function()? eventApproved,
    TResult? Function()? eventRejected,
    TResult? Function()? eventCreated,
    TResult? Function()? eventUpdated,
    TResult? Function()? eventDeleted,
  }) {
    return eventDeleted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPublicEvents,
    TResult Function(EventModel event)? loadedEventDetails,
    TResult Function()? eventApproved,
    TResult Function()? eventRejected,
    TResult Function()? eventCreated,
    TResult Function()? eventUpdated,
    TResult Function()? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventDeleted != null) {
      return eventDeleted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPublicEvents value) loadedPublicEvents,
    required TResult Function(_LoadedEventDetails value) loadedEventDetails,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
    required TResult Function(_EventDeleted value) eventDeleted,
  }) {
    return eventDeleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult? Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
    TResult? Function(_EventDeleted value)? eventDeleted,
  }) {
    return eventDeleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPublicEvents value)? loadedPublicEvents,
    TResult Function(_LoadedEventDetails value)? loadedEventDetails,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    TResult Function(_EventDeleted value)? eventDeleted,
    required TResult orElse(),
  }) {
    if (eventDeleted != null) {
      return eventDeleted(this);
    }
    return orElse();
  }
}

abstract class _EventDeleted implements EventManagementState {
  const factory _EventDeleted() = _$EventDeletedImpl;
}
