// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$toneServiceHash() => r'4f173fbf6b5fca58cd99619270dfd21407a8a7e2';

/// See also [toneService].
@ProviderFor(toneService)
final toneServiceProvider = AutoDisposeProvider<ToneService>.internal(
  toneService,
  name: r'toneServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$toneServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ToneServiceRef = AutoDisposeProviderRef<ToneService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
