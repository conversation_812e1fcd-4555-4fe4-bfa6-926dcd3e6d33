// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MarketingStateImpl _$$MarketingStateImplFromJson(Map<String, dynamic> json) =>
    _$MarketingStateImpl(
      promotions:
          (json['promotions'] as List<dynamic>?)
              ?.map((e) => PromotionModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      campaigns:
          (json['campaigns'] as List<dynamic>?)
              ?.map((e) => CampaignModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$$MarketingStateImplToJson(
  _$MarketingStateImpl instance,
) => <String, dynamic>{
  'promotions': instance.promotions.map((e) => e.toJson()).toList(),
  'campaigns': instance.campaigns.map((e) => e.toJson()).toList(),
  'isLoading': instance.isLoading,
  'error': instance.error,
};
