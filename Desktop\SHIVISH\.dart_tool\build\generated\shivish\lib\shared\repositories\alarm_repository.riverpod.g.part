// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$alarmRepositoryHash() => r'f12773ecc11e25bc0aab11e0f541c938e88b370c';

/// See also [alarmRepository].
@ProviderFor(alarmRepository)
final alarmRepositoryProvider = AutoDisposeProvider<AlarmRepository>.internal(
  alarmRepository,
  name: r'alarmRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$alarmRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AlarmRepositoryRef = AutoDisposeProviderRef<AlarmRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
