// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CalendarEventReminderImpl _$$CalendarEventReminderImplFromJson(
  Map<String, dynamic> json,
) => _$CalendarEventReminderImpl(
  type: $enumDecode(_$ReminderTypeEnumMap, json['type']),
  timeBeforeEvent: (json['timeBeforeEvent'] as num).toInt(),
  isEnabled: json['isEnabled'] as bool? ?? true,
  customMessage: json['customMessage'] as String?,
);

Map<String, dynamic> _$$CalendarEventReminderImplToJson(
  _$CalendarEventReminderImpl instance,
) => <String, dynamic>{
  'type': _$ReminderTypeEnumMap[instance.type]!,
  'timeBeforeEvent': instance.timeBeforeEvent,
  'isEnabled': instance.isEnabled,
  'customMessage': instance.customMessage,
};

const _$ReminderTypeEnumMap = {
  ReminderType.notification: 'notification',
  ReminderType.email: 'email',
  ReminderType.sms: 'sms',
  ReminderType.whatsapp: 'whatsapp',
};
