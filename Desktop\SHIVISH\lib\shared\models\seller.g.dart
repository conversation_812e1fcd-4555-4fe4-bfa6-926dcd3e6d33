// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SellerImpl _$$SellerImplFromJson(Map<String, dynamic> json) => _$SellerImpl(
  id: json['id'] as String,
  businessName: json['businessName'] as String,
  email: json['email'] as String,
  phoneNumber: json['phoneNumber'] as String?,
  profileImage: json['profileImage'] as String?,
  businessLogo: json['businessLogo'] as String?,
  businessDescription: json['businessDescription'] as String?,
  businessAddress:
      (json['businessAddress'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  businessDocuments:
      (json['businessDocuments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  products:
      (json['products'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  orders:
      (json['orders'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  category:
      $enumDecodeNullable(_$SellerCategoryEnumMap, json['category']) ??
      SellerCategory.other,
  isEmailVerified: json['isEmailVerified'] as bool? ?? false,
  isPhoneVerified: json['isPhoneVerified'] as bool? ?? false,
  isProfileComplete: json['isProfileComplete'] as bool? ?? false,
  isApproved: json['isApproved'] as bool? ?? false,
  isActive: json['isActive'] as bool? ?? false,
  isSuspended: json['isSuspended'] as bool? ?? false,
  isDeleted: json['isDeleted'] as bool? ?? false,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  lastLoginAt: json['lastLoginAt'] == null
      ? null
      : DateTime.parse(json['lastLoginAt'] as String),
  approvedAt: json['approvedAt'] == null
      ? null
      : DateTime.parse(json['approvedAt'] as String),
  approvedBy: json['approvedBy'] as String?,
  rejectionReason: json['rejectionReason'] as String?,
  rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
  totalReviews: (json['totalReviews'] as num?)?.toInt() ?? 0,
  totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
  totalProducts: (json['totalProducts'] as num?)?.toInt() ?? 0,
  totalRevenue: (json['totalRevenue'] as num?)?.toDouble() ?? 0.0,
  businessHours: json['businessHours'] as Map<String, dynamic>? ?? const {},
  paymentSettings: json['paymentSettings'] as Map<String, dynamic>? ?? const {},
  shippingSettings:
      json['shippingSettings'] as Map<String, dynamic>? ?? const {},
  notificationSettings:
      json['notificationSettings'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$SellerImplToJson(_$SellerImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'businessName': instance.businessName,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'profileImage': instance.profileImage,
      'businessLogo': instance.businessLogo,
      'businessDescription': instance.businessDescription,
      'businessAddress': instance.businessAddress,
      'businessDocuments': instance.businessDocuments,
      'products': instance.products,
      'orders': instance.orders,
      'category': _$SellerCategoryEnumMap[instance.category]!,
      'isEmailVerified': instance.isEmailVerified,
      'isPhoneVerified': instance.isPhoneVerified,
      'isProfileComplete': instance.isProfileComplete,
      'isApproved': instance.isApproved,
      'isActive': instance.isActive,
      'isSuspended': instance.isSuspended,
      'isDeleted': instance.isDeleted,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'approvedAt': instance.approvedAt?.toIso8601String(),
      'approvedBy': instance.approvedBy,
      'rejectionReason': instance.rejectionReason,
      'rating': instance.rating,
      'totalReviews': instance.totalReviews,
      'totalOrders': instance.totalOrders,
      'totalProducts': instance.totalProducts,
      'totalRevenue': instance.totalRevenue,
      'businessHours': instance.businessHours,
      'paymentSettings': instance.paymentSettings,
      'shippingSettings': instance.shippingSettings,
      'notificationSettings': instance.notificationSettings,
    };

const _$SellerCategoryEnumMap = {
  SellerCategory.farmer: 'farmer',
  SellerCategory.wholesaler: 'wholesaler',
  SellerCategory.retailer: 'retailer',
  SellerCategory.manufacturer: 'manufacturer',
  SellerCategory.distributor: 'distributor',
  SellerCategory.importer: 'importer',
  SellerCategory.exporter: 'exporter',
  SellerCategory.trader: 'trader',
  SellerCategory.other: 'other',
};
