// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentMethodImpl _$$PaymentMethodImplFromJson(Map<String, dynamic> json) =>
    _$PaymentMethodImpl(
      id: json['id'] as String,
      type: $enumDecode(_$PaymentMethodTypeEnumMap, json['type']),
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      cardNumber: json['cardNumber'] as String?,
      cardBrand: json['cardBrand'] as String?,
      expiryMonth: json['expiryMonth'] as String?,
      expiryYear: json['expiryYear'] as String?,
      upiId: json['upiId'] as String?,
      bankName: json['bankName'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$PaymentMethodImplToJson(_$PaymentMethodImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$PaymentMethodTypeEnumMap[instance.type]!,
      'title': instance.title,
      'subtitle': instance.subtitle,
      'cardNumber': instance.cardNumber,
      'cardBrand': instance.cardBrand,
      'expiryMonth': instance.expiryMonth,
      'expiryYear': instance.expiryYear,
      'upiId': instance.upiId,
      'bankName': instance.bankName,
      'metadata': instance.metadata,
    };

const _$PaymentMethodTypeEnumMap = {
  PaymentMethodType.card: 'card',
  PaymentMethodType.upi: 'upi',
  PaymentMethodType.netbanking: 'netbanking',
};
