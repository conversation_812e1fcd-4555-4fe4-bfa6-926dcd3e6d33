// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hospital_user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

HospitalUserModel _$HospitalUserModelFromJson(Map<String, dynamic> json) {
  return _HospitalUserModel.fromJson(json);
}

/// @nodoc
mixin _$HospitalUserModel {
  String get id => throw _privateConstructorUsedError;
  String get hospitalId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  HospitalUserRole get role => throw _privateConstructorUsedError;
  bool get isApproved => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isFirstLogin => throw _privateConstructorUsedError;
  String? get profileImage => throw _privateConstructorUsedError;
  String? get accessCode => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this HospitalUserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HospitalUserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HospitalUserModelCopyWith<HospitalUserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HospitalUserModelCopyWith<$Res> {
  factory $HospitalUserModelCopyWith(
    HospitalUserModel value,
    $Res Function(HospitalUserModel) then,
  ) = _$HospitalUserModelCopyWithImpl<$Res, HospitalUserModel>;
  @useResult
  $Res call({
    String id,
    String hospitalId,
    String name,
    String email,
    String phone,
    HospitalUserRole role,
    bool isApproved,
    bool isActive,
    bool isFirstLogin,
    String? profileImage,
    String? accessCode,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class _$HospitalUserModelCopyWithImpl<$Res, $Val extends HospitalUserModel>
    implements $HospitalUserModelCopyWith<$Res> {
  _$HospitalUserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HospitalUserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? hospitalId = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? role = null,
    Object? isApproved = null,
    Object? isActive = null,
    Object? isFirstLogin = null,
    Object? profileImage = freezed,
    Object? accessCode = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            hospitalId: null == hospitalId
                ? _value.hospitalId
                : hospitalId // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            role: null == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as HospitalUserRole,
            isApproved: null == isApproved
                ? _value.isApproved
                : isApproved // ignore: cast_nullable_to_non_nullable
                      as bool,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            isFirstLogin: null == isFirstLogin
                ? _value.isFirstLogin
                : isFirstLogin // ignore: cast_nullable_to_non_nullable
                      as bool,
            profileImage: freezed == profileImage
                ? _value.profileImage
                : profileImage // ignore: cast_nullable_to_non_nullable
                      as String?,
            accessCode: freezed == accessCode
                ? _value.accessCode
                : accessCode // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$HospitalUserModelImplCopyWith<$Res>
    implements $HospitalUserModelCopyWith<$Res> {
  factory _$$HospitalUserModelImplCopyWith(
    _$HospitalUserModelImpl value,
    $Res Function(_$HospitalUserModelImpl) then,
  ) = __$$HospitalUserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String hospitalId,
    String name,
    String email,
    String phone,
    HospitalUserRole role,
    bool isApproved,
    bool isActive,
    bool isFirstLogin,
    String? profileImage,
    String? accessCode,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
  });
}

/// @nodoc
class __$$HospitalUserModelImplCopyWithImpl<$Res>
    extends _$HospitalUserModelCopyWithImpl<$Res, _$HospitalUserModelImpl>
    implements _$$HospitalUserModelImplCopyWith<$Res> {
  __$$HospitalUserModelImplCopyWithImpl(
    _$HospitalUserModelImpl _value,
    $Res Function(_$HospitalUserModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of HospitalUserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? hospitalId = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? role = null,
    Object? isApproved = null,
    Object? isActive = null,
    Object? isFirstLogin = null,
    Object? profileImage = freezed,
    Object? accessCode = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
  }) {
    return _then(
      _$HospitalUserModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        hospitalId: null == hospitalId
            ? _value.hospitalId
            : hospitalId // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        role: null == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as HospitalUserRole,
        isApproved: null == isApproved
            ? _value.isApproved
            : isApproved // ignore: cast_nullable_to_non_nullable
                  as bool,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        isFirstLogin: null == isFirstLogin
            ? _value.isFirstLogin
            : isFirstLogin // ignore: cast_nullable_to_non_nullable
                  as bool,
        profileImage: freezed == profileImage
            ? _value.profileImage
            : profileImage // ignore: cast_nullable_to_non_nullable
                  as String?,
        accessCode: freezed == accessCode
            ? _value.accessCode
            : accessCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$HospitalUserModelImpl implements _HospitalUserModel {
  const _$HospitalUserModelImpl({
    required this.id,
    required this.hospitalId,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.isApproved,
    required this.isActive,
    required this.isFirstLogin,
    this.profileImage,
    this.accessCode,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  factory _$HospitalUserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$HospitalUserModelImplFromJson(json);

  @override
  final String id;
  @override
  final String hospitalId;
  @override
  final String name;
  @override
  final String email;
  @override
  final String phone;
  @override
  final HospitalUserRole role;
  @override
  final bool isApproved;
  @override
  final bool isActive;
  @override
  final bool isFirstLogin;
  @override
  final String? profileImage;
  @override
  final String? accessCode;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'HospitalUserModel(id: $id, hospitalId: $hospitalId, name: $name, email: $email, phone: $phone, role: $role, isApproved: $isApproved, isActive: $isActive, isFirstLogin: $isFirstLogin, profileImage: $profileImage, accessCode: $accessCode, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HospitalUserModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.hospitalId, hospitalId) ||
                other.hospitalId == hospitalId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.isApproved, isApproved) ||
                other.isApproved == isApproved) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isFirstLogin, isFirstLogin) ||
                other.isFirstLogin == isFirstLogin) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.accessCode, accessCode) ||
                other.accessCode == accessCode) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    hospitalId,
    name,
    email,
    phone,
    role,
    isApproved,
    isActive,
    isFirstLogin,
    profileImage,
    accessCode,
    createdAt,
    updatedAt,
    isDeleted,
  );

  /// Create a copy of HospitalUserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HospitalUserModelImplCopyWith<_$HospitalUserModelImpl> get copyWith =>
      __$$HospitalUserModelImplCopyWithImpl<_$HospitalUserModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$HospitalUserModelImplToJson(this);
  }
}

abstract class _HospitalUserModel implements HospitalUserModel {
  const factory _HospitalUserModel({
    required final String id,
    required final String hospitalId,
    required final String name,
    required final String email,
    required final String phone,
    required final HospitalUserRole role,
    required final bool isApproved,
    required final bool isActive,
    required final bool isFirstLogin,
    final String? profileImage,
    final String? accessCode,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
  }) = _$HospitalUserModelImpl;

  factory _HospitalUserModel.fromJson(Map<String, dynamic> json) =
      _$HospitalUserModelImpl.fromJson;

  @override
  String get id;
  @override
  String get hospitalId;
  @override
  String get name;
  @override
  String get email;
  @override
  String get phone;
  @override
  HospitalUserRole get role;
  @override
  bool get isApproved;
  @override
  bool get isActive;
  @override
  bool get isFirstLogin;
  @override
  String? get profileImage;
  @override
  String? get accessCode;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of HospitalUserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HospitalUserModelImplCopyWith<_$HospitalUserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
