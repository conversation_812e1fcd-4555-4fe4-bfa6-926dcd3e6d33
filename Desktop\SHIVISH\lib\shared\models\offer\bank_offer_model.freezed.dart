// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_offer_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

BankOfferModel _$BankOfferModelFromJson(Map<String, dynamic> json) {
  return _BankOfferModel.fromJson(json);
}

/// @nodoc
mixin _$BankOfferModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get bankName => throw _privateConstructorUsedError;
  String? get bankLogoUrl => throw _privateConstructorUsedError;
  BankOfferType get offerType => throw _privateConstructorUsedError;
  double get value => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime get endDate => throw _privateConstructorUsedError;
  List<String> get applicableProductIds => throw _privateConstructorUsedError;
  List<String> get applicableCategories => throw _privateConstructorUsedError;
  double get minimumPurchaseAmount => throw _privateConstructorUsedError;
  double? get maximumDiscountAmount => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  String? get termsAndConditions => throw _privateConstructorUsedError;

  /// Serializes this BankOfferModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BankOfferModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BankOfferModelCopyWith<BankOfferModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankOfferModelCopyWith<$Res> {
  factory $BankOfferModelCopyWith(
    BankOfferModel value,
    $Res Function(BankOfferModel) then,
  ) = _$BankOfferModelCopyWithImpl<$Res, BankOfferModel>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String bankName,
    String? bankLogoUrl,
    BankOfferType offerType,
    double value,
    String? code,
    DateTime startDate,
    DateTime endDate,
    List<String> applicableProductIds,
    List<String> applicableCategories,
    double minimumPurchaseAmount,
    double? maximumDiscountAmount,
    bool isActive,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    String? termsAndConditions,
  });
}

/// @nodoc
class _$BankOfferModelCopyWithImpl<$Res, $Val extends BankOfferModel>
    implements $BankOfferModelCopyWith<$Res> {
  _$BankOfferModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankOfferModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? bankName = null,
    Object? bankLogoUrl = freezed,
    Object? offerType = null,
    Object? value = null,
    Object? code = freezed,
    Object? startDate = null,
    Object? endDate = null,
    Object? applicableProductIds = null,
    Object? applicableCategories = null,
    Object? minimumPurchaseAmount = null,
    Object? maximumDiscountAmount = freezed,
    Object? isActive = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? termsAndConditions = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            bankName: null == bankName
                ? _value.bankName
                : bankName // ignore: cast_nullable_to_non_nullable
                      as String,
            bankLogoUrl: freezed == bankLogoUrl
                ? _value.bankLogoUrl
                : bankLogoUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            offerType: null == offerType
                ? _value.offerType
                : offerType // ignore: cast_nullable_to_non_nullable
                      as BankOfferType,
            value: null == value
                ? _value.value
                : value // ignore: cast_nullable_to_non_nullable
                      as double,
            code: freezed == code
                ? _value.code
                : code // ignore: cast_nullable_to_non_nullable
                      as String?,
            startDate: null == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            endDate: null == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            applicableProductIds: null == applicableProductIds
                ? _value.applicableProductIds
                : applicableProductIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            applicableCategories: null == applicableCategories
                ? _value.applicableCategories
                : applicableCategories // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            minimumPurchaseAmount: null == minimumPurchaseAmount
                ? _value.minimumPurchaseAmount
                : minimumPurchaseAmount // ignore: cast_nullable_to_non_nullable
                      as double,
            maximumDiscountAmount: freezed == maximumDiscountAmount
                ? _value.maximumDiscountAmount
                : maximumDiscountAmount // ignore: cast_nullable_to_non_nullable
                      as double?,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            termsAndConditions: freezed == termsAndConditions
                ? _value.termsAndConditions
                : termsAndConditions // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BankOfferModelImplCopyWith<$Res>
    implements $BankOfferModelCopyWith<$Res> {
  factory _$$BankOfferModelImplCopyWith(
    _$BankOfferModelImpl value,
    $Res Function(_$BankOfferModelImpl) then,
  ) = __$$BankOfferModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String bankName,
    String? bankLogoUrl,
    BankOfferType offerType,
    double value,
    String? code,
    DateTime startDate,
    DateTime endDate,
    List<String> applicableProductIds,
    List<String> applicableCategories,
    double minimumPurchaseAmount,
    double? maximumDiscountAmount,
    bool isActive,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    String? termsAndConditions,
  });
}

/// @nodoc
class __$$BankOfferModelImplCopyWithImpl<$Res>
    extends _$BankOfferModelCopyWithImpl<$Res, _$BankOfferModelImpl>
    implements _$$BankOfferModelImplCopyWith<$Res> {
  __$$BankOfferModelImplCopyWithImpl(
    _$BankOfferModelImpl _value,
    $Res Function(_$BankOfferModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BankOfferModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? bankName = null,
    Object? bankLogoUrl = freezed,
    Object? offerType = null,
    Object? value = null,
    Object? code = freezed,
    Object? startDate = null,
    Object? endDate = null,
    Object? applicableProductIds = null,
    Object? applicableCategories = null,
    Object? minimumPurchaseAmount = null,
    Object? maximumDiscountAmount = freezed,
    Object? isActive = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? termsAndConditions = freezed,
  }) {
    return _then(
      _$BankOfferModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        bankName: null == bankName
            ? _value.bankName
            : bankName // ignore: cast_nullable_to_non_nullable
                  as String,
        bankLogoUrl: freezed == bankLogoUrl
            ? _value.bankLogoUrl
            : bankLogoUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        offerType: null == offerType
            ? _value.offerType
            : offerType // ignore: cast_nullable_to_non_nullable
                  as BankOfferType,
        value: null == value
            ? _value.value
            : value // ignore: cast_nullable_to_non_nullable
                  as double,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
        startDate: null == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        endDate: null == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        applicableProductIds: null == applicableProductIds
            ? _value._applicableProductIds
            : applicableProductIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        applicableCategories: null == applicableCategories
            ? _value._applicableCategories
            : applicableCategories // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        minimumPurchaseAmount: null == minimumPurchaseAmount
            ? _value.minimumPurchaseAmount
            : minimumPurchaseAmount // ignore: cast_nullable_to_non_nullable
                  as double,
        maximumDiscountAmount: freezed == maximumDiscountAmount
            ? _value.maximumDiscountAmount
            : maximumDiscountAmount // ignore: cast_nullable_to_non_nullable
                  as double?,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        termsAndConditions: freezed == termsAndConditions
            ? _value.termsAndConditions
            : termsAndConditions // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BankOfferModelImpl implements _BankOfferModel {
  const _$BankOfferModelImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.bankName,
    this.bankLogoUrl,
    required this.offerType,
    required this.value,
    this.code,
    required this.startDate,
    required this.endDate,
    required final List<String> applicableProductIds,
    required final List<String> applicableCategories,
    required this.minimumPurchaseAmount,
    this.maximumDiscountAmount,
    this.isActive = false,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    this.termsAndConditions,
  }) : _applicableProductIds = applicableProductIds,
       _applicableCategories = applicableCategories;

  factory _$BankOfferModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BankOfferModelImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String bankName;
  @override
  final String? bankLogoUrl;
  @override
  final BankOfferType offerType;
  @override
  final double value;
  @override
  final String? code;
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  final List<String> _applicableProductIds;
  @override
  List<String> get applicableProductIds {
    if (_applicableProductIds is EqualUnmodifiableListView)
      return _applicableProductIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicableProductIds);
  }

  final List<String> _applicableCategories;
  @override
  List<String> get applicableCategories {
    if (_applicableCategories is EqualUnmodifiableListView)
      return _applicableCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicableCategories);
  }

  @override
  final double minimumPurchaseAmount;
  @override
  final double? maximumDiscountAmount;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  final String? termsAndConditions;

  @override
  String toString() {
    return 'BankOfferModel(id: $id, title: $title, description: $description, bankName: $bankName, bankLogoUrl: $bankLogoUrl, offerType: $offerType, value: $value, code: $code, startDate: $startDate, endDate: $endDate, applicableProductIds: $applicableProductIds, applicableCategories: $applicableCategories, minimumPurchaseAmount: $minimumPurchaseAmount, maximumDiscountAmount: $maximumDiscountAmount, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, termsAndConditions: $termsAndConditions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankOfferModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.bankLogoUrl, bankLogoUrl) ||
                other.bankLogoUrl == bankLogoUrl) &&
            (identical(other.offerType, offerType) ||
                other.offerType == offerType) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            const DeepCollectionEquality().equals(
              other._applicableProductIds,
              _applicableProductIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._applicableCategories,
              _applicableCategories,
            ) &&
            (identical(other.minimumPurchaseAmount, minimumPurchaseAmount) ||
                other.minimumPurchaseAmount == minimumPurchaseAmount) &&
            (identical(other.maximumDiscountAmount, maximumDiscountAmount) ||
                other.maximumDiscountAmount == maximumDiscountAmount) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.termsAndConditions, termsAndConditions) ||
                other.termsAndConditions == termsAndConditions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    title,
    description,
    bankName,
    bankLogoUrl,
    offerType,
    value,
    code,
    startDate,
    endDate,
    const DeepCollectionEquality().hash(_applicableProductIds),
    const DeepCollectionEquality().hash(_applicableCategories),
    minimumPurchaseAmount,
    maximumDiscountAmount,
    isActive,
    createdAt,
    updatedAt,
    isDeleted,
    termsAndConditions,
  ]);

  /// Create a copy of BankOfferModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankOfferModelImplCopyWith<_$BankOfferModelImpl> get copyWith =>
      __$$BankOfferModelImplCopyWithImpl<_$BankOfferModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$BankOfferModelImplToJson(this);
  }
}

abstract class _BankOfferModel implements BankOfferModel {
  const factory _BankOfferModel({
    required final String id,
    required final String title,
    required final String description,
    required final String bankName,
    final String? bankLogoUrl,
    required final BankOfferType offerType,
    required final double value,
    final String? code,
    required final DateTime startDate,
    required final DateTime endDate,
    required final List<String> applicableProductIds,
    required final List<String> applicableCategories,
    required final double minimumPurchaseAmount,
    final double? maximumDiscountAmount,
    final bool isActive,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final bool isDeleted,
    final String? termsAndConditions,
  }) = _$BankOfferModelImpl;

  factory _BankOfferModel.fromJson(Map<String, dynamic> json) =
      _$BankOfferModelImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get bankName;
  @override
  String? get bankLogoUrl;
  @override
  BankOfferType get offerType;
  @override
  double get value;
  @override
  String? get code;
  @override
  DateTime get startDate;
  @override
  DateTime get endDate;
  @override
  List<String> get applicableProductIds;
  @override
  List<String> get applicableCategories;
  @override
  double get minimumPurchaseAmount;
  @override
  double? get maximumDiscountAmount;
  @override
  bool get isActive;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  String? get termsAndConditions;

  /// Create a copy of BankOfferModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankOfferModelImplCopyWith<_$BankOfferModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
