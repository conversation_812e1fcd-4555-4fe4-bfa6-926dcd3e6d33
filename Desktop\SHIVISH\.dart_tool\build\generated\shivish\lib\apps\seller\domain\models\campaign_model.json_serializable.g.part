// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignModelImpl _$$CampaignModelImplFromJson(Map<String, dynamic> json) =>
    _$CampaignModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$CampaignTypeEnumMap, json['type']),
      status: $enumDecode(_$CampaignStatusEnumMap, json['status']),
      budget: (json['budget'] as num).toDouble(),
      spent: (json['spent'] as num).toDouble(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      impressions: (json['impressions'] as num).toInt(),
      clicks: (json['clicks'] as num).toInt(),
      conversions: (json['conversions'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$CampaignModelImplToJson(_$CampaignModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$CampaignTypeEnumMap[instance.type]!,
      'status': _$CampaignStatusEnumMap[instance.status]!,
      'budget': instance.budget,
      'spent': instance.spent,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'impressions': instance.impressions,
      'clicks': instance.clicks,
      'conversions': instance.conversions,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

const _$CampaignTypeEnumMap = {
  CampaignType.productPromotion: 'productPromotion',
  CampaignType.storePromotion: 'storePromotion',
  CampaignType.seasonalSale: 'seasonalSale',
  CampaignType.flashSale: 'flashSale',
};

const _$CampaignStatusEnumMap = {
  CampaignStatus.draft: 'draft',
  CampaignStatus.scheduled: 'scheduled',
  CampaignStatus.active: 'active',
  CampaignStatus.paused: 'paused',
  CampaignStatus.completed: 'completed',
  CampaignStatus.cancelled: 'cancelled',
};
