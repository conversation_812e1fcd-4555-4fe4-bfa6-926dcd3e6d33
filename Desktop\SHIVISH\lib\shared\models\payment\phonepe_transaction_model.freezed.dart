// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'phonepe_transaction_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PhonePeTransactionModel _$PhonePeTransactionModelFromJson(
  Map<String, dynamic> json,
) {
  return _PhonePeTransactionModel.fromJson(json);
}

/// @nodoc
mixin _$PhonePeTransactionModel {
  /// Unique transaction ID
  String get transactionId => throw _privateConstructorUsedError;

  /// Merchant transaction ID
  String get merchantTransactionId => throw _privateConstructorUsedError;

  /// Order ID
  String? get orderId => throw _privateConstructorUsedError;

  /// Amount in paise
  int get amount => throw _privateConstructorUsedError;

  /// Transaction status
  PhonePeTransactionStatus get status => throw _privateConstructorUsedError;

  /// Response code
  String? get responseCode => throw _privateConstructorUsedError;

  /// Response message
  String? get responseMessage => throw _privateConstructorUsedError;

  /// Payment instrument type (UPI, CARD, etc.)
  String? get paymentInstrumentType => throw _privateConstructorUsedError;

  /// Payment instrument details
  Map<String, dynamic>? get paymentInstrumentDetails =>
      throw _privateConstructorUsedError;

  /// Customer ID
  String? get customerId => throw _privateConstructorUsedError;

  /// Customer phone number
  String? get customerPhone => throw _privateConstructorUsedError;

  /// Customer email
  String? get customerEmail => throw _privateConstructorUsedError;

  /// Created timestamp
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Updated timestamp
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Raw response from PhonePe
  Map<String, dynamic>? get rawResponse => throw _privateConstructorUsedError;

  /// Serializes this PhonePeTransactionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PhonePeTransactionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhonePeTransactionModelCopyWith<PhonePeTransactionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhonePeTransactionModelCopyWith<$Res> {
  factory $PhonePeTransactionModelCopyWith(
    PhonePeTransactionModel value,
    $Res Function(PhonePeTransactionModel) then,
  ) = _$PhonePeTransactionModelCopyWithImpl<$Res, PhonePeTransactionModel>;
  @useResult
  $Res call({
    String transactionId,
    String merchantTransactionId,
    String? orderId,
    int amount,
    PhonePeTransactionStatus status,
    String? responseCode,
    String? responseMessage,
    String? paymentInstrumentType,
    Map<String, dynamic>? paymentInstrumentDetails,
    String? customerId,
    String? customerPhone,
    String? customerEmail,
    DateTime createdAt,
    DateTime updatedAt,
    Map<String, dynamic>? rawResponse,
  });
}

/// @nodoc
class _$PhonePeTransactionModelCopyWithImpl<
  $Res,
  $Val extends PhonePeTransactionModel
>
    implements $PhonePeTransactionModelCopyWith<$Res> {
  _$PhonePeTransactionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhonePeTransactionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionId = null,
    Object? merchantTransactionId = null,
    Object? orderId = freezed,
    Object? amount = null,
    Object? status = null,
    Object? responseCode = freezed,
    Object? responseMessage = freezed,
    Object? paymentInstrumentType = freezed,
    Object? paymentInstrumentDetails = freezed,
    Object? customerId = freezed,
    Object? customerPhone = freezed,
    Object? customerEmail = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? rawResponse = freezed,
  }) {
    return _then(
      _value.copyWith(
            transactionId: null == transactionId
                ? _value.transactionId
                : transactionId // ignore: cast_nullable_to_non_nullable
                      as String,
            merchantTransactionId: null == merchantTransactionId
                ? _value.merchantTransactionId
                : merchantTransactionId // ignore: cast_nullable_to_non_nullable
                      as String,
            orderId: freezed == orderId
                ? _value.orderId
                : orderId // ignore: cast_nullable_to_non_nullable
                      as String?,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as int,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as PhonePeTransactionStatus,
            responseCode: freezed == responseCode
                ? _value.responseCode
                : responseCode // ignore: cast_nullable_to_non_nullable
                      as String?,
            responseMessage: freezed == responseMessage
                ? _value.responseMessage
                : responseMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            paymentInstrumentType: freezed == paymentInstrumentType
                ? _value.paymentInstrumentType
                : paymentInstrumentType // ignore: cast_nullable_to_non_nullable
                      as String?,
            paymentInstrumentDetails: freezed == paymentInstrumentDetails
                ? _value.paymentInstrumentDetails
                : paymentInstrumentDetails // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            customerId: freezed == customerId
                ? _value.customerId
                : customerId // ignore: cast_nullable_to_non_nullable
                      as String?,
            customerPhone: freezed == customerPhone
                ? _value.customerPhone
                : customerPhone // ignore: cast_nullable_to_non_nullable
                      as String?,
            customerEmail: freezed == customerEmail
                ? _value.customerEmail
                : customerEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            rawResponse: freezed == rawResponse
                ? _value.rawResponse
                : rawResponse // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PhonePeTransactionModelImplCopyWith<$Res>
    implements $PhonePeTransactionModelCopyWith<$Res> {
  factory _$$PhonePeTransactionModelImplCopyWith(
    _$PhonePeTransactionModelImpl value,
    $Res Function(_$PhonePeTransactionModelImpl) then,
  ) = __$$PhonePeTransactionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String transactionId,
    String merchantTransactionId,
    String? orderId,
    int amount,
    PhonePeTransactionStatus status,
    String? responseCode,
    String? responseMessage,
    String? paymentInstrumentType,
    Map<String, dynamic>? paymentInstrumentDetails,
    String? customerId,
    String? customerPhone,
    String? customerEmail,
    DateTime createdAt,
    DateTime updatedAt,
    Map<String, dynamic>? rawResponse,
  });
}

/// @nodoc
class __$$PhonePeTransactionModelImplCopyWithImpl<$Res>
    extends
        _$PhonePeTransactionModelCopyWithImpl<
          $Res,
          _$PhonePeTransactionModelImpl
        >
    implements _$$PhonePeTransactionModelImplCopyWith<$Res> {
  __$$PhonePeTransactionModelImplCopyWithImpl(
    _$PhonePeTransactionModelImpl _value,
    $Res Function(_$PhonePeTransactionModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PhonePeTransactionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionId = null,
    Object? merchantTransactionId = null,
    Object? orderId = freezed,
    Object? amount = null,
    Object? status = null,
    Object? responseCode = freezed,
    Object? responseMessage = freezed,
    Object? paymentInstrumentType = freezed,
    Object? paymentInstrumentDetails = freezed,
    Object? customerId = freezed,
    Object? customerPhone = freezed,
    Object? customerEmail = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? rawResponse = freezed,
  }) {
    return _then(
      _$PhonePeTransactionModelImpl(
        transactionId: null == transactionId
            ? _value.transactionId
            : transactionId // ignore: cast_nullable_to_non_nullable
                  as String,
        merchantTransactionId: null == merchantTransactionId
            ? _value.merchantTransactionId
            : merchantTransactionId // ignore: cast_nullable_to_non_nullable
                  as String,
        orderId: freezed == orderId
            ? _value.orderId
            : orderId // ignore: cast_nullable_to_non_nullable
                  as String?,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as int,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as PhonePeTransactionStatus,
        responseCode: freezed == responseCode
            ? _value.responseCode
            : responseCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        responseMessage: freezed == responseMessage
            ? _value.responseMessage
            : responseMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        paymentInstrumentType: freezed == paymentInstrumentType
            ? _value.paymentInstrumentType
            : paymentInstrumentType // ignore: cast_nullable_to_non_nullable
                  as String?,
        paymentInstrumentDetails: freezed == paymentInstrumentDetails
            ? _value._paymentInstrumentDetails
            : paymentInstrumentDetails // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        customerId: freezed == customerId
            ? _value.customerId
            : customerId // ignore: cast_nullable_to_non_nullable
                  as String?,
        customerPhone: freezed == customerPhone
            ? _value.customerPhone
            : customerPhone // ignore: cast_nullable_to_non_nullable
                  as String?,
        customerEmail: freezed == customerEmail
            ? _value.customerEmail
            : customerEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        rawResponse: freezed == rawResponse
            ? _value._rawResponse
            : rawResponse // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PhonePeTransactionModelImpl implements _PhonePeTransactionModel {
  const _$PhonePeTransactionModelImpl({
    required this.transactionId,
    required this.merchantTransactionId,
    this.orderId,
    required this.amount,
    this.status = PhonePeTransactionStatus.pending,
    this.responseCode,
    this.responseMessage,
    this.paymentInstrumentType,
    final Map<String, dynamic>? paymentInstrumentDetails,
    this.customerId,
    this.customerPhone,
    this.customerEmail,
    required this.createdAt,
    required this.updatedAt,
    final Map<String, dynamic>? rawResponse,
  }) : _paymentInstrumentDetails = paymentInstrumentDetails,
       _rawResponse = rawResponse;

  factory _$PhonePeTransactionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PhonePeTransactionModelImplFromJson(json);

  /// Unique transaction ID
  @override
  final String transactionId;

  /// Merchant transaction ID
  @override
  final String merchantTransactionId;

  /// Order ID
  @override
  final String? orderId;

  /// Amount in paise
  @override
  final int amount;

  /// Transaction status
  @override
  @JsonKey()
  final PhonePeTransactionStatus status;

  /// Response code
  @override
  final String? responseCode;

  /// Response message
  @override
  final String? responseMessage;

  /// Payment instrument type (UPI, CARD, etc.)
  @override
  final String? paymentInstrumentType;

  /// Payment instrument details
  final Map<String, dynamic>? _paymentInstrumentDetails;

  /// Payment instrument details
  @override
  Map<String, dynamic>? get paymentInstrumentDetails {
    final value = _paymentInstrumentDetails;
    if (value == null) return null;
    if (_paymentInstrumentDetails is EqualUnmodifiableMapView)
      return _paymentInstrumentDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Customer ID
  @override
  final String? customerId;

  /// Customer phone number
  @override
  final String? customerPhone;

  /// Customer email
  @override
  final String? customerEmail;

  /// Created timestamp
  @override
  final DateTime createdAt;

  /// Updated timestamp
  @override
  final DateTime updatedAt;

  /// Raw response from PhonePe
  final Map<String, dynamic>? _rawResponse;

  /// Raw response from PhonePe
  @override
  Map<String, dynamic>? get rawResponse {
    final value = _rawResponse;
    if (value == null) return null;
    if (_rawResponse is EqualUnmodifiableMapView) return _rawResponse;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PhonePeTransactionModel(transactionId: $transactionId, merchantTransactionId: $merchantTransactionId, orderId: $orderId, amount: $amount, status: $status, responseCode: $responseCode, responseMessage: $responseMessage, paymentInstrumentType: $paymentInstrumentType, paymentInstrumentDetails: $paymentInstrumentDetails, customerId: $customerId, customerPhone: $customerPhone, customerEmail: $customerEmail, createdAt: $createdAt, updatedAt: $updatedAt, rawResponse: $rawResponse)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhonePeTransactionModelImpl &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.merchantTransactionId, merchantTransactionId) ||
                other.merchantTransactionId == merchantTransactionId) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.responseCode, responseCode) ||
                other.responseCode == responseCode) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            (identical(other.paymentInstrumentType, paymentInstrumentType) ||
                other.paymentInstrumentType == paymentInstrumentType) &&
            const DeepCollectionEquality().equals(
              other._paymentInstrumentDetails,
              _paymentInstrumentDetails,
            ) &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.customerPhone, customerPhone) ||
                other.customerPhone == customerPhone) &&
            (identical(other.customerEmail, customerEmail) ||
                other.customerEmail == customerEmail) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(
              other._rawResponse,
              _rawResponse,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    transactionId,
    merchantTransactionId,
    orderId,
    amount,
    status,
    responseCode,
    responseMessage,
    paymentInstrumentType,
    const DeepCollectionEquality().hash(_paymentInstrumentDetails),
    customerId,
    customerPhone,
    customerEmail,
    createdAt,
    updatedAt,
    const DeepCollectionEquality().hash(_rawResponse),
  );

  /// Create a copy of PhonePeTransactionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhonePeTransactionModelImplCopyWith<_$PhonePeTransactionModelImpl>
  get copyWith =>
      __$$PhonePeTransactionModelImplCopyWithImpl<
        _$PhonePeTransactionModelImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PhonePeTransactionModelImplToJson(this);
  }
}

abstract class _PhonePeTransactionModel implements PhonePeTransactionModel {
  const factory _PhonePeTransactionModel({
    required final String transactionId,
    required final String merchantTransactionId,
    final String? orderId,
    required final int amount,
    final PhonePeTransactionStatus status,
    final String? responseCode,
    final String? responseMessage,
    final String? paymentInstrumentType,
    final Map<String, dynamic>? paymentInstrumentDetails,
    final String? customerId,
    final String? customerPhone,
    final String? customerEmail,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final Map<String, dynamic>? rawResponse,
  }) = _$PhonePeTransactionModelImpl;

  factory _PhonePeTransactionModel.fromJson(Map<String, dynamic> json) =
      _$PhonePeTransactionModelImpl.fromJson;

  /// Unique transaction ID
  @override
  String get transactionId;

  /// Merchant transaction ID
  @override
  String get merchantTransactionId;

  /// Order ID
  @override
  String? get orderId;

  /// Amount in paise
  @override
  int get amount;

  /// Transaction status
  @override
  PhonePeTransactionStatus get status;

  /// Response code
  @override
  String? get responseCode;

  /// Response message
  @override
  String? get responseMessage;

  /// Payment instrument type (UPI, CARD, etc.)
  @override
  String? get paymentInstrumentType;

  /// Payment instrument details
  @override
  Map<String, dynamic>? get paymentInstrumentDetails;

  /// Customer ID
  @override
  String? get customerId;

  /// Customer phone number
  @override
  String? get customerPhone;

  /// Customer email
  @override
  String? get customerEmail;

  /// Created timestamp
  @override
  DateTime get createdAt;

  /// Updated timestamp
  @override
  DateTime get updatedAt;

  /// Raw response from PhonePe
  @override
  Map<String, dynamic>? get rawResponse;

  /// Create a copy of PhonePeTransactionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhonePeTransactionModelImplCopyWith<_$PhonePeTransactionModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
