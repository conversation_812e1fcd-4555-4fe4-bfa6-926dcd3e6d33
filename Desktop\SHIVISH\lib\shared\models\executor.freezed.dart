// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'executor.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Executor _$ExecutorFromJson(Map<String, dynamic> json) {
  return _Executor.fromJson(json);
}

/// @nodoc
mixin _$Executor {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String get role => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  String? get profileImage => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  Map<String, dynamic>? get performanceMetrics =>
      throw _privateConstructorUsedError;
  List<String>? get assignedRegions => throw _privateConstructorUsedError;
  List<String>? get permissions => throw _privateConstructorUsedError;

  /// Serializes this Executor to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Executor
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ExecutorCopyWith<Executor> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExecutorCopyWith<$Res> {
  factory $ExecutorCopyWith(Executor value, $Res Function(Executor) then) =
      _$ExecutorCopyWithImpl<$Res, Executor>;
  @useResult
  $Res call({
    String id,
    String name,
    String email,
    String phone,
    String role,
    String status,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    bool isActive,
    String? profileImage,
    String? address,
    String? notes,
    Map<String, dynamic>? performanceMetrics,
    List<String>? assignedRegions,
    List<String>? permissions,
  });
}

/// @nodoc
class _$ExecutorCopyWithImpl<$Res, $Val extends Executor>
    implements $ExecutorCopyWith<$Res> {
  _$ExecutorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Executor
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? role = null,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? isActive = null,
    Object? profileImage = freezed,
    Object? address = freezed,
    Object? notes = freezed,
    Object? performanceMetrics = freezed,
    Object? assignedRegions = freezed,
    Object? permissions = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            role: null == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            profileImage: freezed == profileImage
                ? _value.profileImage
                : profileImage // ignore: cast_nullable_to_non_nullable
                      as String?,
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            performanceMetrics: freezed == performanceMetrics
                ? _value.performanceMetrics
                : performanceMetrics // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            assignedRegions: freezed == assignedRegions
                ? _value.assignedRegions
                : assignedRegions // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            permissions: freezed == permissions
                ? _value.permissions
                : permissions // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ExecutorImplCopyWith<$Res>
    implements $ExecutorCopyWith<$Res> {
  factory _$$ExecutorImplCopyWith(
    _$ExecutorImpl value,
    $Res Function(_$ExecutorImpl) then,
  ) = __$$ExecutorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String email,
    String phone,
    String role,
    String status,
    DateTime createdAt,
    DateTime updatedAt,
    bool isDeleted,
    bool isActive,
    String? profileImage,
    String? address,
    String? notes,
    Map<String, dynamic>? performanceMetrics,
    List<String>? assignedRegions,
    List<String>? permissions,
  });
}

/// @nodoc
class __$$ExecutorImplCopyWithImpl<$Res>
    extends _$ExecutorCopyWithImpl<$Res, _$ExecutorImpl>
    implements _$$ExecutorImplCopyWith<$Res> {
  __$$ExecutorImplCopyWithImpl(
    _$ExecutorImpl _value,
    $Res Function(_$ExecutorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Executor
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? phone = null,
    Object? role = null,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isDeleted = null,
    Object? isActive = null,
    Object? profileImage = freezed,
    Object? address = freezed,
    Object? notes = freezed,
    Object? performanceMetrics = freezed,
    Object? assignedRegions = freezed,
    Object? permissions = freezed,
  }) {
    return _then(
      _$ExecutorImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        role: null == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        profileImage: freezed == profileImage
            ? _value.profileImage
            : profileImage // ignore: cast_nullable_to_non_nullable
                  as String?,
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        performanceMetrics: freezed == performanceMetrics
            ? _value._performanceMetrics
            : performanceMetrics // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        assignedRegions: freezed == assignedRegions
            ? _value._assignedRegions
            : assignedRegions // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        permissions: freezed == permissions
            ? _value._permissions
            : permissions // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ExecutorImpl implements _Executor {
  const _$ExecutorImpl({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.isDeleted,
    this.isActive = false,
    this.profileImage,
    this.address,
    this.notes,
    final Map<String, dynamic>? performanceMetrics,
    final List<String>? assignedRegions,
    final List<String>? permissions,
  }) : _performanceMetrics = performanceMetrics,
       _assignedRegions = assignedRegions,
       _permissions = permissions;

  factory _$ExecutorImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExecutorImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String email;
  @override
  final String phone;
  @override
  final String role;
  @override
  final String status;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final bool isDeleted;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final String? profileImage;
  @override
  final String? address;
  @override
  final String? notes;
  final Map<String, dynamic>? _performanceMetrics;
  @override
  Map<String, dynamic>? get performanceMetrics {
    final value = _performanceMetrics;
    if (value == null) return null;
    if (_performanceMetrics is EqualUnmodifiableMapView)
      return _performanceMetrics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _assignedRegions;
  @override
  List<String>? get assignedRegions {
    final value = _assignedRegions;
    if (value == null) return null;
    if (_assignedRegions is EqualUnmodifiableListView) return _assignedRegions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _permissions;
  @override
  List<String>? get permissions {
    final value = _permissions;
    if (value == null) return null;
    if (_permissions is EqualUnmodifiableListView) return _permissions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Executor(id: $id, name: $name, email: $email, phone: $phone, role: $role, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, isActive: $isActive, profileImage: $profileImage, address: $address, notes: $notes, performanceMetrics: $performanceMetrics, assignedRegions: $assignedRegions, permissions: $permissions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExecutorImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            const DeepCollectionEquality().equals(
              other._performanceMetrics,
              _performanceMetrics,
            ) &&
            const DeepCollectionEquality().equals(
              other._assignedRegions,
              _assignedRegions,
            ) &&
            const DeepCollectionEquality().equals(
              other._permissions,
              _permissions,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    email,
    phone,
    role,
    status,
    createdAt,
    updatedAt,
    isDeleted,
    isActive,
    profileImage,
    address,
    notes,
    const DeepCollectionEquality().hash(_performanceMetrics),
    const DeepCollectionEquality().hash(_assignedRegions),
    const DeepCollectionEquality().hash(_permissions),
  );

  /// Create a copy of Executor
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExecutorImplCopyWith<_$ExecutorImpl> get copyWith =>
      __$$ExecutorImplCopyWithImpl<_$ExecutorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExecutorImplToJson(this);
  }
}

abstract class _Executor implements Executor {
  const factory _Executor({
    required final String id,
    required final String name,
    required final String email,
    required final String phone,
    required final String role,
    required final String status,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    required final bool isDeleted,
    final bool isActive,
    final String? profileImage,
    final String? address,
    final String? notes,
    final Map<String, dynamic>? performanceMetrics,
    final List<String>? assignedRegions,
    final List<String>? permissions,
  }) = _$ExecutorImpl;

  factory _Executor.fromJson(Map<String, dynamic> json) =
      _$ExecutorImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get email;
  @override
  String get phone;
  @override
  String get role;
  @override
  String get status;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isDeleted;
  @override
  bool get isActive;
  @override
  String? get profileImage;
  @override
  String? get address;
  @override
  String? get notes;
  @override
  Map<String, dynamic>? get performanceMetrics;
  @override
  List<String>? get assignedRegions;
  @override
  List<String>? get permissions;

  /// Create a copy of Executor
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExecutorImplCopyWith<_$ExecutorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
