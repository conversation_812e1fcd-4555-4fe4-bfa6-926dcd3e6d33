// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'doctor_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DoctorModelImpl _$$DoctorModelImplFromJson(Map<String, dynamic> json) =>
    _$DoctorModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      hospitalId: json['hospitalId'] as String,
      departmentId: json['departmentId'] as String,
      designation: json['designation'] as String,
      qualification: json['qualification'] as String,
      description: json['description'] as String,
      specialties: (json['specialties'] as List<dynamic>)
          .map((e) => $enumDecode(_$DoctorSpecialtyEnumMap, e))
          .toList(),
      experienceYears: (json['experienceYears'] as num).toInt(),
      registrationNumber: json['registrationNumber'] as String,
      profileImage: json['profileImage'] as String,
      isAvailable: json['isAvailable'] as bool,
      isVerified: json['isVerified'] as bool,
      rating: (json['rating'] as num).toDouble(),
      totalReviews: (json['totalReviews'] as num).toInt(),
      consultationFee: (json['consultationFee'] as num).toDouble(),
      consultationDuration: (json['consultationDuration'] as num).toInt(),
      schedule: json['schedule'] == null
          ? null
          : DoctorScheduleModel.fromJson(
              json['schedule'] as Map<String, dynamic>,
            ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
    );

Map<String, dynamic> _$$DoctorModelImplToJson(_$DoctorModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'hospitalId': instance.hospitalId,
      'departmentId': instance.departmentId,
      'designation': instance.designation,
      'qualification': instance.qualification,
      'description': instance.description,
      'specialties': instance.specialties
          .map((e) => _$DoctorSpecialtyEnumMap[e]!)
          .toList(),
      'experienceYears': instance.experienceYears,
      'registrationNumber': instance.registrationNumber,
      'profileImage': instance.profileImage,
      'isAvailable': instance.isAvailable,
      'isVerified': instance.isVerified,
      'rating': instance.rating,
      'totalReviews': instance.totalReviews,
      'consultationFee': instance.consultationFee,
      'consultationDuration': instance.consultationDuration,
      'schedule': instance.schedule?.toJson(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'deletedAt': instance.deletedAt?.toIso8601String(),
    };

const _$DoctorSpecialtyEnumMap = {
  DoctorSpecialty.generalPhysician: 'general_physician',
  DoctorSpecialty.cardiologist: 'cardiologist',
  DoctorSpecialty.dermatologist: 'dermatologist',
  DoctorSpecialty.neurologist: 'neurologist',
  DoctorSpecialty.orthopedic: 'orthopedic',
  DoctorSpecialty.pediatrician: 'pediatrician',
  DoctorSpecialty.childrenSpecialist: 'children_specialist',
  DoctorSpecialty.gynecologist: 'gynecologist',
  DoctorSpecialty.ophthalmologist: 'ophthalmologist',
  DoctorSpecialty.entSpecialist: 'ent_specialist',
  DoctorSpecialty.psychiatrist: 'psychiatrist',
  DoctorSpecialty.dentist: 'dentist',
  DoctorSpecialty.urologist: 'urologist',
  DoctorSpecialty.gastroenterologist: 'gastroenterologist',
  DoctorSpecialty.endocrinologist: 'endocrinologist',
  DoctorSpecialty.pulmonologist: 'pulmonologist',
  DoctorSpecialty.oncologist: 'oncologist',
  DoctorSpecialty.rheumatologist: 'rheumatologist',
  DoctorSpecialty.nephrologist: 'nephrologist',
  DoctorSpecialty.neonatologist: 'neonatologist',
  DoctorSpecialty.pediatricSurgeon: 'pediatric_surgeon',
  DoctorSpecialty.pediatricNeurologist: 'pediatric_neurologist',
  DoctorSpecialty.pediatricCardiologist: 'pediatric_cardiologist',
  DoctorSpecialty.pediatricEndocrinologist: 'pediatric_endocrinologist',
  DoctorSpecialty.pediatricGastroenterologist: 'pediatric_gastroenterologist',
  DoctorSpecialty.pediatricHematologist: 'pediatric_hematologist',
  DoctorSpecialty.pediatricOncologist: 'pediatric_oncologist',
  DoctorSpecialty.pediatricPulmonologist: 'pediatric_pulmonologist',
  DoctorSpecialty.pediatricRheumatologist: 'pediatric_rheumatologist',
  DoctorSpecialty.pediatricNephrologist: 'pediatric_nephrologist',
  DoctorSpecialty.pediatricDermatologist: 'pediatric_dermatologist',
  DoctorSpecialty.neurosurgeon: 'neurosurgeon',
  DoctorSpecialty.cardiothoracicSurgeon: 'cardiothoracic_surgeon',
  DoctorSpecialty.vascularSurgeon: 'vascular_surgeon',
  DoctorSpecialty.plasticSurgeon: 'plastic_surgeon',
  DoctorSpecialty.generalSurgeon: 'general_surgeon',
  DoctorSpecialty.allergist: 'allergist',
  DoctorSpecialty.immunologist: 'immunologist',
  DoctorSpecialty.anesthesiologist: 'anesthesiologist',
  DoctorSpecialty.radiologist: 'radiologist',
  DoctorSpecialty.pathologist: 'pathologist',
  DoctorSpecialty.emergencyMedicine: 'emergency_medicine',
  DoctorSpecialty.familyMedicine: 'family_medicine',
  DoctorSpecialty.internalMedicine: 'internal_medicine',
  DoctorSpecialty.infectiousDisease: 'infectious_disease',
  DoctorSpecialty.geriatrician: 'geriatrician',
  DoctorSpecialty.sportsMedicine: 'sports_medicine',
  DoctorSpecialty.painManagement: 'pain_management',
  DoctorSpecialty.physicalMedicine: 'physical_medicine',
  DoctorSpecialty.hematologist: 'hematologist',
  DoctorSpecialty.nutritionist: 'nutritionist',
  DoctorSpecialty.diabetologist: 'diabetologist',
  DoctorSpecialty.audiologist: 'audiologist',
  DoctorSpecialty.physiotherapist: 'physiotherapist',
  DoctorSpecialty.obstetrician: 'obstetrician',
  DoctorSpecialty.hepatologist: 'hepatologist',
  DoctorSpecialty.podiatrist: 'podiatrist',
  DoctorSpecialty.geneticist: 'geneticist',
  DoctorSpecialty.homeopathic: 'homeopathic',
  DoctorSpecialty.other: 'other',
};
