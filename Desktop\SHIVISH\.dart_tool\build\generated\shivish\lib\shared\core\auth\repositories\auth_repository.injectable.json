[{"type": {"import": "package:shivish/shared/core/auth/repositories/auth_repository.dart", "name": "AuthRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/shared/core/auth/repositories/auth_repository.dart", "name": "AuthRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 1, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "", "orderPosition": 0}]