// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReviewImpl _$$ReviewImplFromJson(Map<String, dynamic> json) => _$ReviewImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  userName: json['userName'] as String?,
  userImage: json['userImage'] as String?,
  rating: (json['rating'] as num?)?.toDouble(),
  comment: json['comment'] as String?,
  isVerified: json['isVerified'] as bool?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
  helpfulVotes: (json['helpfulVotes'] as num?)?.toInt() ?? 0,
  notHelpfulVotes: (json['notHelpfulVotes'] as num?)?.toInt() ?? 0,
  productId: json['productId'] as String?,
  orderId: json['orderId'] as String?,
  photoUrls:
      (json['photoUrls'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  isVerifiedPurchase: json['isVerifiedPurchase'] as bool? ?? false,
);

Map<String, dynamic> _$$ReviewImplToJson(_$ReviewImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'userImage': instance.userImage,
      'rating': instance.rating,
      'comment': instance.comment,
      'isVerified': instance.isVerified,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'helpfulVotes': instance.helpfulVotes,
      'notHelpfulVotes': instance.notHelpfulVotes,
      'productId': instance.productId,
      'orderId': instance.orderId,
      'photoUrls': instance.photoUrls,
      'isVerifiedPurchase': instance.isVerifiedPurchase,
    };
