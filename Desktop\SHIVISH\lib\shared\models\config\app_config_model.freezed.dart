// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

AppConfigModel _$AppConfigModelFromJson(Map<String, dynamic> json) {
  return _AppConfigModel.fromJson(json);
}

/// @nodoc
mixin _$AppConfigModel {
  String get supportEmail => throw _privateConstructorUsedError;
  String get placeholderImageUrl => throw _privateConstructorUsedError;
  String get apiBaseUrl => throw _privateConstructorUsedError;
  String get chatUrl => throw _privateConstructorUsedError;
  String get paymentApiUrl => throw _privateConstructorUsedError;
  String? get razorpayKeyId => throw _privateConstructorUsedError;
  String? get razorpayKeySecret => throw _privateConstructorUsedError;

  /// Serializes this AppConfigModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppConfigModelCopyWith<AppConfigModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppConfigModelCopyWith<$Res> {
  factory $AppConfigModelCopyWith(
    AppConfigModel value,
    $Res Function(AppConfigModel) then,
  ) = _$AppConfigModelCopyWithImpl<$Res, AppConfigModel>;
  @useResult
  $Res call({
    String supportEmail,
    String placeholderImageUrl,
    String apiBaseUrl,
    String chatUrl,
    String paymentApiUrl,
    String? razorpayKeyId,
    String? razorpayKeySecret,
  });
}

/// @nodoc
class _$AppConfigModelCopyWithImpl<$Res, $Val extends AppConfigModel>
    implements $AppConfigModelCopyWith<$Res> {
  _$AppConfigModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? supportEmail = null,
    Object? placeholderImageUrl = null,
    Object? apiBaseUrl = null,
    Object? chatUrl = null,
    Object? paymentApiUrl = null,
    Object? razorpayKeyId = freezed,
    Object? razorpayKeySecret = freezed,
  }) {
    return _then(
      _value.copyWith(
            supportEmail: null == supportEmail
                ? _value.supportEmail
                : supportEmail // ignore: cast_nullable_to_non_nullable
                      as String,
            placeholderImageUrl: null == placeholderImageUrl
                ? _value.placeholderImageUrl
                : placeholderImageUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            apiBaseUrl: null == apiBaseUrl
                ? _value.apiBaseUrl
                : apiBaseUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            chatUrl: null == chatUrl
                ? _value.chatUrl
                : chatUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            paymentApiUrl: null == paymentApiUrl
                ? _value.paymentApiUrl
                : paymentApiUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            razorpayKeyId: freezed == razorpayKeyId
                ? _value.razorpayKeyId
                : razorpayKeyId // ignore: cast_nullable_to_non_nullable
                      as String?,
            razorpayKeySecret: freezed == razorpayKeySecret
                ? _value.razorpayKeySecret
                : razorpayKeySecret // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AppConfigModelImplCopyWith<$Res>
    implements $AppConfigModelCopyWith<$Res> {
  factory _$$AppConfigModelImplCopyWith(
    _$AppConfigModelImpl value,
    $Res Function(_$AppConfigModelImpl) then,
  ) = __$$AppConfigModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String supportEmail,
    String placeholderImageUrl,
    String apiBaseUrl,
    String chatUrl,
    String paymentApiUrl,
    String? razorpayKeyId,
    String? razorpayKeySecret,
  });
}

/// @nodoc
class __$$AppConfigModelImplCopyWithImpl<$Res>
    extends _$AppConfigModelCopyWithImpl<$Res, _$AppConfigModelImpl>
    implements _$$AppConfigModelImplCopyWith<$Res> {
  __$$AppConfigModelImplCopyWithImpl(
    _$AppConfigModelImpl _value,
    $Res Function(_$AppConfigModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AppConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? supportEmail = null,
    Object? placeholderImageUrl = null,
    Object? apiBaseUrl = null,
    Object? chatUrl = null,
    Object? paymentApiUrl = null,
    Object? razorpayKeyId = freezed,
    Object? razorpayKeySecret = freezed,
  }) {
    return _then(
      _$AppConfigModelImpl(
        supportEmail: null == supportEmail
            ? _value.supportEmail
            : supportEmail // ignore: cast_nullable_to_non_nullable
                  as String,
        placeholderImageUrl: null == placeholderImageUrl
            ? _value.placeholderImageUrl
            : placeholderImageUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        apiBaseUrl: null == apiBaseUrl
            ? _value.apiBaseUrl
            : apiBaseUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        chatUrl: null == chatUrl
            ? _value.chatUrl
            : chatUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentApiUrl: null == paymentApiUrl
            ? _value.paymentApiUrl
            : paymentApiUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        razorpayKeyId: freezed == razorpayKeyId
            ? _value.razorpayKeyId
            : razorpayKeyId // ignore: cast_nullable_to_non_nullable
                  as String?,
        razorpayKeySecret: freezed == razorpayKeySecret
            ? _value.razorpayKeySecret
            : razorpayKeySecret // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AppConfigModelImpl implements _AppConfigModel {
  const _$AppConfigModelImpl({
    required this.supportEmail,
    required this.placeholderImageUrl,
    required this.apiBaseUrl,
    required this.chatUrl,
    required this.paymentApiUrl,
    this.razorpayKeyId,
    this.razorpayKeySecret,
  });

  factory _$AppConfigModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppConfigModelImplFromJson(json);

  @override
  final String supportEmail;
  @override
  final String placeholderImageUrl;
  @override
  final String apiBaseUrl;
  @override
  final String chatUrl;
  @override
  final String paymentApiUrl;
  @override
  final String? razorpayKeyId;
  @override
  final String? razorpayKeySecret;

  @override
  String toString() {
    return 'AppConfigModel(supportEmail: $supportEmail, placeholderImageUrl: $placeholderImageUrl, apiBaseUrl: $apiBaseUrl, chatUrl: $chatUrl, paymentApiUrl: $paymentApiUrl, razorpayKeyId: $razorpayKeyId, razorpayKeySecret: $razorpayKeySecret)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppConfigModelImpl &&
            (identical(other.supportEmail, supportEmail) ||
                other.supportEmail == supportEmail) &&
            (identical(other.placeholderImageUrl, placeholderImageUrl) ||
                other.placeholderImageUrl == placeholderImageUrl) &&
            (identical(other.apiBaseUrl, apiBaseUrl) ||
                other.apiBaseUrl == apiBaseUrl) &&
            (identical(other.chatUrl, chatUrl) || other.chatUrl == chatUrl) &&
            (identical(other.paymentApiUrl, paymentApiUrl) ||
                other.paymentApiUrl == paymentApiUrl) &&
            (identical(other.razorpayKeyId, razorpayKeyId) ||
                other.razorpayKeyId == razorpayKeyId) &&
            (identical(other.razorpayKeySecret, razorpayKeySecret) ||
                other.razorpayKeySecret == razorpayKeySecret));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    supportEmail,
    placeholderImageUrl,
    apiBaseUrl,
    chatUrl,
    paymentApiUrl,
    razorpayKeyId,
    razorpayKeySecret,
  );

  /// Create a copy of AppConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppConfigModelImplCopyWith<_$AppConfigModelImpl> get copyWith =>
      __$$AppConfigModelImplCopyWithImpl<_$AppConfigModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$AppConfigModelImplToJson(this);
  }
}

abstract class _AppConfigModel implements AppConfigModel {
  const factory _AppConfigModel({
    required final String supportEmail,
    required final String placeholderImageUrl,
    required final String apiBaseUrl,
    required final String chatUrl,
    required final String paymentApiUrl,
    final String? razorpayKeyId,
    final String? razorpayKeySecret,
  }) = _$AppConfigModelImpl;

  factory _AppConfigModel.fromJson(Map<String, dynamic> json) =
      _$AppConfigModelImpl.fromJson;

  @override
  String get supportEmail;
  @override
  String get placeholderImageUrl;
  @override
  String get apiBaseUrl;
  @override
  String get chatUrl;
  @override
  String get paymentApiUrl;
  @override
  String? get razorpayKeyId;
  @override
  String? get razorpayKeySecret;

  /// Create a copy of AppConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppConfigModelImplCopyWith<_$AppConfigModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
