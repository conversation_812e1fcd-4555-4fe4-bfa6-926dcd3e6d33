// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MediaVariantImpl _$$MediaVariantImplFromJson(Map<String, dynamic> json) =>
    _$MediaVariantImpl(
      url: json['url'] as String,
      quality: json['quality'] as String,
      width: (json['width'] as num).toInt(),
      height: (json['height'] as num).toInt(),
      bitrate: (json['bitrate'] as num).toInt(),
      format: json['format'] as String?,
    );

Map<String, dynamic> _$$MediaVariantImplToJson(_$MediaVariantImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
      'quality': instance.quality,
      'width': instance.width,
      'height': instance.height,
      'bitrate': instance.bitrate,
      'format': instance.format,
    };
