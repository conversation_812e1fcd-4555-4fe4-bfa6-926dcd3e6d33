[{"type": {"import": "package:shivish/apps/admin/bloc/executor/executor_bloc.dart", "name": "ExecutorBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/admin/bloc/executor/executor_bloc.dart", "name": "ExecutorBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/shared/services/executor_service.dart", "name": "ExecutorService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_executorService", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]