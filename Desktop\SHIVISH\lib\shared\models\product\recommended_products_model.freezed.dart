// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recommended_products_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

RecommendedProductsModel _$RecommendedProductsModelFromJson(
  Map<String, dynamic> json,
) {
  return _RecommendedProductsModel.fromJson(json);
}

/// @nodoc
mixin _$RecommendedProductsModel {
  String get productId => throw _privateConstructorUsedError;
  List<String> get recommendedProductIds => throw _privateConstructorUsedError;
  RecommendationType get type => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this RecommendedProductsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecommendedProductsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecommendedProductsModelCopyWith<RecommendedProductsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecommendedProductsModelCopyWith<$Res> {
  factory $RecommendedProductsModelCopyWith(
    RecommendedProductsModel value,
    $Res Function(RecommendedProductsModel) then,
  ) = _$RecommendedProductsModelCopyWithImpl<$Res, RecommendedProductsModel>;
  @useResult
  $Res call({
    String productId,
    List<String> recommendedProductIds,
    RecommendationType type,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$RecommendedProductsModelCopyWithImpl<
  $Res,
  $Val extends RecommendedProductsModel
>
    implements $RecommendedProductsModelCopyWith<$Res> {
  _$RecommendedProductsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecommendedProductsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? recommendedProductIds = null,
    Object? type = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            productId: null == productId
                ? _value.productId
                : productId // ignore: cast_nullable_to_non_nullable
                      as String,
            recommendedProductIds: null == recommendedProductIds
                ? _value.recommendedProductIds
                : recommendedProductIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as RecommendationType,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$RecommendedProductsModelImplCopyWith<$Res>
    implements $RecommendedProductsModelCopyWith<$Res> {
  factory _$$RecommendedProductsModelImplCopyWith(
    _$RecommendedProductsModelImpl value,
    $Res Function(_$RecommendedProductsModelImpl) then,
  ) = __$$RecommendedProductsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String productId,
    List<String> recommendedProductIds,
    RecommendationType type,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$RecommendedProductsModelImplCopyWithImpl<$Res>
    extends
        _$RecommendedProductsModelCopyWithImpl<
          $Res,
          _$RecommendedProductsModelImpl
        >
    implements _$$RecommendedProductsModelImplCopyWith<$Res> {
  __$$RecommendedProductsModelImplCopyWithImpl(
    _$RecommendedProductsModelImpl _value,
    $Res Function(_$RecommendedProductsModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of RecommendedProductsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? recommendedProductIds = null,
    Object? type = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$RecommendedProductsModelImpl(
        productId: null == productId
            ? _value.productId
            : productId // ignore: cast_nullable_to_non_nullable
                  as String,
        recommendedProductIds: null == recommendedProductIds
            ? _value._recommendedProductIds
            : recommendedProductIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as RecommendationType,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$RecommendedProductsModelImpl implements _RecommendedProductsModel {
  const _$RecommendedProductsModelImpl({
    required this.productId,
    required final List<String> recommendedProductIds,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  }) : _recommendedProductIds = recommendedProductIds;

  factory _$RecommendedProductsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecommendedProductsModelImplFromJson(json);

  @override
  final String productId;
  final List<String> _recommendedProductIds;
  @override
  List<String> get recommendedProductIds {
    if (_recommendedProductIds is EqualUnmodifiableListView)
      return _recommendedProductIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recommendedProductIds);
  }

  @override
  final RecommendationType type;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'RecommendedProductsModel(productId: $productId, recommendedProductIds: $recommendedProductIds, type: $type, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecommendedProductsModelImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            const DeepCollectionEquality().equals(
              other._recommendedProductIds,
              _recommendedProductIds,
            ) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    productId,
    const DeepCollectionEquality().hash(_recommendedProductIds),
    type,
    createdAt,
    updatedAt,
  );

  /// Create a copy of RecommendedProductsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecommendedProductsModelImplCopyWith<_$RecommendedProductsModelImpl>
  get copyWith =>
      __$$RecommendedProductsModelImplCopyWithImpl<
        _$RecommendedProductsModelImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecommendedProductsModelImplToJson(this);
  }
}

abstract class _RecommendedProductsModel implements RecommendedProductsModel {
  const factory _RecommendedProductsModel({
    required final String productId,
    required final List<String> recommendedProductIds,
    required final RecommendationType type,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$RecommendedProductsModelImpl;

  factory _RecommendedProductsModel.fromJson(Map<String, dynamic> json) =
      _$RecommendedProductsModelImpl.fromJson;

  @override
  String get productId;
  @override
  List<String> get recommendedProductIds;
  @override
  RecommendationType get type;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of RecommendedProductsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecommendedProductsModelImplCopyWith<_$RecommendedProductsModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
