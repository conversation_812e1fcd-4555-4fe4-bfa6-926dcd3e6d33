// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BookingAddressImpl _$$BookingAddressImplFromJson(Map<String, dynamic> json) =>
    _$BookingAddressImpl(
      street: json['street'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      country: json['country'] as String,
      postalCode: json['postalCode'] as String,
      landmark: json['landmark'] as String?,
      contactName: json['contactName'] as String,
      contactPhone: json['contactPhone'] as String,
      contactEmail: json['contactEmail'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$BookingAddressImplToJson(
  _$BookingAddressImpl instance,
) => <String, dynamic>{
  'street': instance.street,
  'city': instance.city,
  'state': instance.state,
  'country': instance.country,
  'postalCode': instance.postalCode,
  'landmark': instance.landmark,
  'contactName': instance.contactName,
  'contactPhone': instance.contactPhone,
  'contactEmail': instance.contactEmail,
  'latitude': instance.latitude,
  'longitude': instance.longitude,
};

_$BookingModelImpl _$$BookingModelImplFromJson(Map<String, dynamic> json) =>
    _$BookingModelImpl(
      id: json['id'] as String,
      bookingNumber: json['bookingNumber'] as String,
      customerId: json['customerId'] as String,
      providerId: json['providerId'] as String,
      type: $enumDecode(_$BookingTypeEnumMap, json['type']),
      status: $enumDecode(_$BookingStatusEnumMap, json['status']),
      paymentStatus: $enumDecode(_$PaymentStatusEnumMap, json['paymentStatus']),
      bookingDate: DateTime.parse(json['bookingDate'] as String),
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      serviceLocation: BookingAddress.fromJson(
        json['serviceLocation'] as Map<String, dynamic>,
      ),
      services: (json['services'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      subtotalAmount: (json['subtotalAmount'] as num).toDouble(),
      taxAmount: (json['taxAmount'] as num).toDouble(),
      discountAmount: (json['discountAmount'] as num).toDouble(),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      paymentMethod: json['paymentMethod'] as String?,
      paymentId: json['paymentId'] as String?,
      transactionId: json['transactionId'] as String?,
      refundId: json['refundId'] as String?,
      notes: json['notes'] as String?,
      customerNotes: json['customerNotes'] as String?,
      providerNotes: json['providerNotes'] as String?,
      cancellationReason: json['cancellationReason'] as String?,
      refundReason: json['refundReason'] as String?,
      customerName: json['customerName'] as String?,
      customerPhone: json['customerPhone'] as String?,
      customerEmail: json['customerEmail'] as String?,
      providerName: json['providerName'] as String?,
      providerPhone: json['providerPhone'] as String?,
      providerEmail: json['providerEmail'] as String?,
      reminderSent: json['reminderSent'] as bool? ?? false,
      reminderSentAt: json['reminderSentAt'] == null
          ? null
          : DateTime.parse(json['reminderSentAt'] as String),
      confirmationSent: json['confirmationSent'] as bool? ?? false,
      confirmationSentAt: json['confirmationSentAt'] == null
          ? null
          : DateTime.parse(json['confirmationSentAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      rating: (json['rating'] as num?)?.toDouble(),
      review: json['review'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$BookingModelImplToJson(_$BookingModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'bookingNumber': instance.bookingNumber,
      'customerId': instance.customerId,
      'providerId': instance.providerId,
      'type': _$BookingTypeEnumMap[instance.type]!,
      'status': _$BookingStatusEnumMap[instance.status]!,
      'paymentStatus': _$PaymentStatusEnumMap[instance.paymentStatus]!,
      'bookingDate': instance.bookingDate.toIso8601String(),
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'serviceLocation': instance.serviceLocation.toJson(),
      'services': instance.services,
      'subtotalAmount': instance.subtotalAmount,
      'taxAmount': instance.taxAmount,
      'discountAmount': instance.discountAmount,
      'totalAmount': instance.totalAmount,
      'paymentMethod': instance.paymentMethod,
      'paymentId': instance.paymentId,
      'transactionId': instance.transactionId,
      'refundId': instance.refundId,
      'notes': instance.notes,
      'customerNotes': instance.customerNotes,
      'providerNotes': instance.providerNotes,
      'cancellationReason': instance.cancellationReason,
      'refundReason': instance.refundReason,
      'customerName': instance.customerName,
      'customerPhone': instance.customerPhone,
      'customerEmail': instance.customerEmail,
      'providerName': instance.providerName,
      'providerPhone': instance.providerPhone,
      'providerEmail': instance.providerEmail,
      'reminderSent': instance.reminderSent,
      'reminderSentAt': instance.reminderSentAt?.toIso8601String(),
      'confirmationSent': instance.confirmationSent,
      'confirmationSentAt': instance.confirmationSentAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'rating': instance.rating,
      'review': instance.review,
      'attachments': instance.attachments,
    };

const _$BookingTypeEnumMap = {BookingType.priest: 0, BookingType.technician: 1};

const _$BookingStatusEnumMap = {
  BookingStatus.pending: 0,
  BookingStatus.confirmed: 1,
  BookingStatus.inProgress: 2,
  BookingStatus.completed: 3,
  BookingStatus.cancelled: 4,
  BookingStatus.rescheduled: 5,
  BookingStatus.noShow: 6,
  BookingStatus.rejected: 7,
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 0,
  PaymentStatus.completed: 1,
  PaymentStatus.failed: 2,
  PaymentStatus.refunded: 3,
  PaymentStatus.partiallyRefunded: 4,
};
