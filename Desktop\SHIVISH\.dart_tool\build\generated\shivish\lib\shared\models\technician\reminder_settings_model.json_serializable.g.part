// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReminderSettingsModelImpl _$$ReminderSettingsModelImplFromJson(
  Map<String, dynamic> json,
) => _$ReminderSettingsModelImpl(
  enableReminders: json['enableReminders'] as bool,
  enableEmailReminders: json['enableEmailReminders'] as bool,
  enablePushReminders: json['enablePushReminders'] as bool,
  enableSMSReminders: json['enableSMSReminders'] as bool,
  defaultReminderTime: (json['defaultReminderTime'] as num).toInt(),
  enableCustomReminders: json['enableCustomReminders'] as bool,
  customReminders: (json['customReminders'] as List<dynamic>)
      .map((e) => CustomReminderModel.fromJson(e as Map<String, dynamic>))
      .toList(),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$ReminderSettingsModelImplToJson(
  _$ReminderSettingsModelImpl instance,
) => <String, dynamic>{
  'enableReminders': instance.enableReminders,
  'enableEmailReminders': instance.enableEmailReminders,
  'enablePushReminders': instance.enablePushReminders,
  'enableSMSReminders': instance.enableSMSReminders,
  'defaultReminderTime': instance.defaultReminderTime,
  'enableCustomReminders': instance.enableCustomReminders,
  'customReminders': instance.customReminders.map((e) => e.toJson()).toList(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

_$CustomReminderModelImpl _$$CustomReminderModelImplFromJson(
  Map<String, dynamic> json,
) => _$CustomReminderModelImpl(
  minutesBefore: (json['minutesBefore'] as num).toInt(),
  isEnabled: json['isEnabled'] as bool,
);

Map<String, dynamic> _$$CustomReminderModelImplToJson(
  _$CustomReminderModelImpl instance,
) => <String, dynamic>{
  'minutesBefore': instance.minutesBefore,
  'isEnabled': instance.isEnabled,
};
