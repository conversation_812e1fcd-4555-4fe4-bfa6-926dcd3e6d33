// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_command_training.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

VoiceCommandTraining _$VoiceCommandTrainingFromJson(Map<String, dynamic> json) {
  return _VoiceCommandTraining.fromJson(json);
}

/// @nodoc
mixin _$VoiceCommandTraining {
  String get id => throw _privateConstructorUsedError;
  String get command => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get action => throw _privateConstructorUsedError;
  Map<String, dynamic> get parameters => throw _privateConstructorUsedError;
  bool get isEnabled => throw _privateConstructorUsedError;
  String get createdAt => throw _privateConstructorUsedError;
  String get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this VoiceCommandTraining to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoiceCommandTraining
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoiceCommandTrainingCopyWith<VoiceCommandTraining> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceCommandTrainingCopyWith<$Res> {
  factory $VoiceCommandTrainingCopyWith(
    VoiceCommandTraining value,
    $Res Function(VoiceCommandTraining) then,
  ) = _$VoiceCommandTrainingCopyWithImpl<$Res, VoiceCommandTraining>;
  @useResult
  $Res call({
    String id,
    String command,
    String description,
    String action,
    Map<String, dynamic> parameters,
    bool isEnabled,
    String createdAt,
    String updatedAt,
  });
}

/// @nodoc
class _$VoiceCommandTrainingCopyWithImpl<
  $Res,
  $Val extends VoiceCommandTraining
>
    implements $VoiceCommandTrainingCopyWith<$Res> {
  _$VoiceCommandTrainingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceCommandTraining
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? command = null,
    Object? description = null,
    Object? action = null,
    Object? parameters = null,
    Object? isEnabled = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            command: null == command
                ? _value.command
                : command // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            action: null == action
                ? _value.action
                : action // ignore: cast_nullable_to_non_nullable
                      as String,
            parameters: null == parameters
                ? _value.parameters
                : parameters // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            isEnabled: null == isEnabled
                ? _value.isEnabled
                : isEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as String,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VoiceCommandTrainingImplCopyWith<$Res>
    implements $VoiceCommandTrainingCopyWith<$Res> {
  factory _$$VoiceCommandTrainingImplCopyWith(
    _$VoiceCommandTrainingImpl value,
    $Res Function(_$VoiceCommandTrainingImpl) then,
  ) = __$$VoiceCommandTrainingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String command,
    String description,
    String action,
    Map<String, dynamic> parameters,
    bool isEnabled,
    String createdAt,
    String updatedAt,
  });
}

/// @nodoc
class __$$VoiceCommandTrainingImplCopyWithImpl<$Res>
    extends _$VoiceCommandTrainingCopyWithImpl<$Res, _$VoiceCommandTrainingImpl>
    implements _$$VoiceCommandTrainingImplCopyWith<$Res> {
  __$$VoiceCommandTrainingImplCopyWithImpl(
    _$VoiceCommandTrainingImpl _value,
    $Res Function(_$VoiceCommandTrainingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VoiceCommandTraining
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? command = null,
    Object? description = null,
    Object? action = null,
    Object? parameters = null,
    Object? isEnabled = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$VoiceCommandTrainingImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        command: null == command
            ? _value.command
            : command // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        action: null == action
            ? _value.action
            : action // ignore: cast_nullable_to_non_nullable
                  as String,
        parameters: null == parameters
            ? _value._parameters
            : parameters // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        isEnabled: null == isEnabled
            ? _value.isEnabled
            : isEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as String,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceCommandTrainingImpl implements _VoiceCommandTraining {
  const _$VoiceCommandTrainingImpl({
    required this.id,
    required this.command,
    required this.description,
    required this.action,
    required final Map<String, dynamic> parameters,
    required this.isEnabled,
    required this.createdAt,
    required this.updatedAt,
  }) : _parameters = parameters;

  factory _$VoiceCommandTrainingImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceCommandTrainingImplFromJson(json);

  @override
  final String id;
  @override
  final String command;
  @override
  final String description;
  @override
  final String action;
  final Map<String, dynamic> _parameters;
  @override
  Map<String, dynamic> get parameters {
    if (_parameters is EqualUnmodifiableMapView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_parameters);
  }

  @override
  final bool isEnabled;
  @override
  final String createdAt;
  @override
  final String updatedAt;

  @override
  String toString() {
    return 'VoiceCommandTraining(id: $id, command: $command, description: $description, action: $action, parameters: $parameters, isEnabled: $isEnabled, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceCommandTrainingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.command, command) || other.command == command) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.action, action) || other.action == action) &&
            const DeepCollectionEquality().equals(
              other._parameters,
              _parameters,
            ) &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    command,
    description,
    action,
    const DeepCollectionEquality().hash(_parameters),
    isEnabled,
    createdAt,
    updatedAt,
  );

  /// Create a copy of VoiceCommandTraining
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceCommandTrainingImplCopyWith<_$VoiceCommandTrainingImpl>
  get copyWith =>
      __$$VoiceCommandTrainingImplCopyWithImpl<_$VoiceCommandTrainingImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceCommandTrainingImplToJson(this);
  }
}

abstract class _VoiceCommandTraining implements VoiceCommandTraining {
  const factory _VoiceCommandTraining({
    required final String id,
    required final String command,
    required final String description,
    required final String action,
    required final Map<String, dynamic> parameters,
    required final bool isEnabled,
    required final String createdAt,
    required final String updatedAt,
  }) = _$VoiceCommandTrainingImpl;

  factory _VoiceCommandTraining.fromJson(Map<String, dynamic> json) =
      _$VoiceCommandTrainingImpl.fromJson;

  @override
  String get id;
  @override
  String get command;
  @override
  String get description;
  @override
  String get action;
  @override
  Map<String, dynamic> get parameters;
  @override
  bool get isEnabled;
  @override
  String get createdAt;
  @override
  String get updatedAt;

  /// Create a copy of VoiceCommandTraining
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoiceCommandTrainingImplCopyWith<_$VoiceCommandTrainingImpl>
  get copyWith => throw _privateConstructorUsedError;
}
