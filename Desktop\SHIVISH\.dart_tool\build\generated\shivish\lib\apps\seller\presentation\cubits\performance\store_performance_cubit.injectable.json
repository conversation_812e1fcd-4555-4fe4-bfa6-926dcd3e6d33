[{"type": {"import": "package:shivish/apps/seller/presentation/cubits/performance/store_performance_cubit.dart", "name": "StorePerformanceCubit", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:shivish/apps/seller/presentation/cubits/performance/store_performance_cubit.dart", "name": "StorePerformanceCubit", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:shivish/apps/seller/domain/repositories/analytics_repository.dart", "name": "AnalyticsRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_analyticsRepository", "isFactoryParam": false, "isPositional": true}], "constructorName": "", "orderPosition": 0}]