// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$reviewServiceHash() => r'e441f4d1472c9086e9d9b8204148cf86fcc280a3';

/// See also [reviewService].
@ProviderFor(reviewService)
final reviewServiceProvider = AutoDisposeProvider<ReviewService>.internal(
  reviewService,
  name: r'reviewServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$reviewServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ReviewServiceRef = AutoDisposeProviderRef<ReviewService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
