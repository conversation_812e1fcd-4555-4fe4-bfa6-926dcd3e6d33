// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppStateImpl _$$AppStateImplFromJson(Map<String, dynamic> json) =>
    _$AppStateImpl(
      isAuthenticated: json['isAuthenticated'] as bool? ?? false,
      isDarkMode: json['isDarkMode'] as bool? ?? false,
      locale: json['locale'] as String? ?? 'en',
      isOffline: json['isOffline'] as bool? ?? false,
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
      userData: json['userData'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$AppStateImplToJson(_$AppStateImpl instance) =>
    <String, dynamic>{
      'isAuthenticated': instance.isAuthenticated,
      'isDarkMode': instance.isDarkMode,
      'locale': instance.locale,
      'isOffline': instance.isOffline,
      'isLoading': instance.isLoading,
      'error': instance.error,
      'userData': instance.userData,
    };
