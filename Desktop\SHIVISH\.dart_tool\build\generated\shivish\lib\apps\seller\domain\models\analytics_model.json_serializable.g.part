// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SalesAnalyticsImpl _$$SalesAnalyticsImplFromJson(Map<String, dynamic> json) =>
    _$SalesAnalyticsImpl(
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      totalOrders: (json['totalOrders'] as num).toInt(),
      averageOrderValue: (json['averageOrderValue'] as num).toDouble(),
      growthRate: (json['growthRate'] as num).toDouble(),
      dailySales: (json['dailySales'] as List<dynamic>)
          .map((e) => SalesPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      salesByCategory: (json['salesByCategory'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$$SalesAnalyticsImplToJson(
  _$SalesAnalyticsImpl instance,
) => <String, dynamic>{
  'totalRevenue': instance.totalRevenue,
  'totalOrders': instance.totalOrders,
  'averageOrderValue': instance.averageOrderValue,
  'growthRate': instance.growthRate,
  'dailySales': instance.dailySales.map((e) => e.toJson()).toList(),
  'salesByCategory': instance.salesByCategory,
};

_$SalesPointImpl _$$SalesPointImplFromJson(Map<String, dynamic> json) =>
    _$SalesPointImpl(
      date: DateTime.parse(json['date'] as String),
      amount: (json['amount'] as num).toDouble(),
    );

Map<String, dynamic> _$$SalesPointImplToJson(_$SalesPointImpl instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'amount': instance.amount,
    };

_$ProductAnalyticsImpl _$$ProductAnalyticsImplFromJson(
  Map<String, dynamic> json,
) => _$ProductAnalyticsImpl(
  totalProducts: (json['totalProducts'] as num).toInt(),
  outOfStockProducts: (json['outOfStockProducts'] as num).toInt(),
  averageRating: (json['averageRating'] as num).toDouble(),
  topProducts: (json['topProducts'] as List<dynamic>)
      .map((e) => ProductPerformance.fromJson(e as Map<String, dynamic>))
      .toList(),
  lowStockProducts: (json['lowStockProducts'] as List<dynamic>)
      .map((e) => ProductStock.fromJson(e as Map<String, dynamic>))
      .toList(),
  productsByCategory: Map<String, int>.from(json['productsByCategory'] as Map),
);

Map<String, dynamic> _$$ProductAnalyticsImplToJson(
  _$ProductAnalyticsImpl instance,
) => <String, dynamic>{
  'totalProducts': instance.totalProducts,
  'outOfStockProducts': instance.outOfStockProducts,
  'averageRating': instance.averageRating,
  'topProducts': instance.topProducts.map((e) => e.toJson()).toList(),
  'lowStockProducts': instance.lowStockProducts.map((e) => e.toJson()).toList(),
  'productsByCategory': instance.productsByCategory,
};

_$ProductPerformanceImpl _$$ProductPerformanceImplFromJson(
  Map<String, dynamic> json,
) => _$ProductPerformanceImpl(
  name: json['name'] as String,
  quantity: (json['quantity'] as num).toInt(),
  revenue: (json['revenue'] as num).toDouble(),
  rating: (json['rating'] as num).toDouble(),
  imageUrl: json['imageUrl'] as String?,
  soldCount: (json['soldCount'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$$ProductPerformanceImplToJson(
  _$ProductPerformanceImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'quantity': instance.quantity,
  'revenue': instance.revenue,
  'rating': instance.rating,
  'imageUrl': instance.imageUrl,
  'soldCount': instance.soldCount,
};

_$ProductStockImpl _$$ProductStockImplFromJson(Map<String, dynamic> json) =>
    _$ProductStockImpl(
      name: json['name'] as String,
      quantity: (json['quantity'] as num).toInt(),
    );

Map<String, dynamic> _$$ProductStockImplToJson(_$ProductStockImpl instance) =>
    <String, dynamic>{'name': instance.name, 'quantity': instance.quantity};

_$CustomerAnalyticsImpl _$$CustomerAnalyticsImplFromJson(
  Map<String, dynamic> json,
) => _$CustomerAnalyticsImpl(
  totalCustomers: (json['totalCustomers'] as num).toInt(),
  newCustomers: (json['newCustomers'] as num).toInt(),
  customerRetentionRate: (json['customerRetentionRate'] as num).toDouble(),
  averageCustomerLifetimeValue: (json['averageCustomerLifetimeValue'] as num)
      .toDouble(),
  segments: (json['segments'] as List<dynamic>)
      .map((e) => CustomerSegment.fromJson(e as Map<String, dynamic>))
      .toList(),
  customersByLocation: Map<String, int>.from(
    json['customersByLocation'] as Map,
  ),
  recentActivity: (json['recentActivity'] as List<dynamic>)
      .map((e) => CustomerActivity.fromJson(e as Map<String, dynamic>))
      .toList(),
  growthData:
      (json['growthData'] as List<dynamic>?)
          ?.map((e) => CustomerGrowthPoint.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$$CustomerAnalyticsImplToJson(
  _$CustomerAnalyticsImpl instance,
) => <String, dynamic>{
  'totalCustomers': instance.totalCustomers,
  'newCustomers': instance.newCustomers,
  'customerRetentionRate': instance.customerRetentionRate,
  'averageCustomerLifetimeValue': instance.averageCustomerLifetimeValue,
  'segments': instance.segments.map((e) => e.toJson()).toList(),
  'customersByLocation': instance.customersByLocation,
  'recentActivity': instance.recentActivity.map((e) => e.toJson()).toList(),
  'growthData': instance.growthData.map((e) => e.toJson()).toList(),
};

_$CustomerSegmentImpl _$$CustomerSegmentImplFromJson(
  Map<String, dynamic> json,
) => _$CustomerSegmentImpl(
  name: json['name'] as String,
  count: (json['count'] as num).toInt(),
  averageOrderValue: (json['averageOrderValue'] as num).toDouble(),
  revenue: (json['revenue'] as num).toDouble(),
);

Map<String, dynamic> _$$CustomerSegmentImplToJson(
  _$CustomerSegmentImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'count': instance.count,
  'averageOrderValue': instance.averageOrderValue,
  'revenue': instance.revenue,
};

_$CustomerActivityImpl _$$CustomerActivityImplFromJson(
  Map<String, dynamic> json,
) => _$CustomerActivityImpl(
  customerId: json['customerId'] as String,
  action: json['action'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
);

Map<String, dynamic> _$$CustomerActivityImplToJson(
  _$CustomerActivityImpl instance,
) => <String, dynamic>{
  'customerId': instance.customerId,
  'action': instance.action,
  'timestamp': instance.timestamp.toIso8601String(),
};

_$CustomerGrowthPointImpl _$$CustomerGrowthPointImplFromJson(
  Map<String, dynamic> json,
) => _$CustomerGrowthPointImpl(
  date: DateTime.parse(json['date'] as String),
  count: (json['count'] as num).toInt(),
);

Map<String, dynamic> _$$CustomerGrowthPointImplToJson(
  _$CustomerGrowthPointImpl instance,
) => <String, dynamic>{
  'date': instance.date.toIso8601String(),
  'count': instance.count,
};

_$PerformanceMetricsImpl _$$PerformanceMetricsImplFromJson(
  Map<String, dynamic> json,
) => _$PerformanceMetricsImpl(
  conversionRate: (json['conversionRate'] as num).toDouble(),
  returnRate: (json['returnRate'] as num).toDouble(),
  averageProcessingTime: (json['averageProcessingTime'] as num).toDouble(),
  averageDeliveryTime: (json['averageDeliveryTime'] as num).toDouble(),
  performanceByDay: (json['performanceByDay'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(
      DateTime.parse(k),
      MetricPoint.fromJson(e as Map<String, dynamic>),
    ),
  ),
  improvements: (json['improvements'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$$PerformanceMetricsImplToJson(
  _$PerformanceMetricsImpl instance,
) => <String, dynamic>{
  'conversionRate': instance.conversionRate,
  'returnRate': instance.returnRate,
  'averageProcessingTime': instance.averageProcessingTime,
  'averageDeliveryTime': instance.averageDeliveryTime,
  'performanceByDay': instance.performanceByDay.map(
    (k, e) => MapEntry(k.toIso8601String(), e.toJson()),
  ),
  'improvements': instance.improvements,
};

_$MetricPointImpl _$$MetricPointImplFromJson(Map<String, dynamic> json) =>
    _$MetricPointImpl(value: (json['value'] as num).toDouble());

Map<String, dynamic> _$$MetricPointImplToJson(_$MetricPointImpl instance) =>
    <String, dynamic>{'value': instance.value};
