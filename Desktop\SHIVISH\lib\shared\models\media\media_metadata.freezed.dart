// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_metadata.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

MediaMetadata _$MediaMetadataFromJson(Map<String, dynamic> json) {
  return _MediaMetadata.fromJson(json);
}

/// @nodoc
mixin _$MediaMetadata {
  String? get contentType => throw _privateConstructorUsedError;
  int? get size => throw _privateConstructorUsedError;
  DateTime? get timeCreated => throw _privateConstructorUsedError;
  DateTime? get updated => throw _privateConstructorUsedError;
  String? get md5Hash => throw _privateConstructorUsedError;
  String? get generation => throw _privateConstructorUsedError;
  String? get metageneration => throw _privateConstructorUsedError;

  /// Serializes this MediaMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MediaMetadataCopyWith<MediaMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaMetadataCopyWith<$Res> {
  factory $MediaMetadataCopyWith(
    MediaMetadata value,
    $Res Function(MediaMetadata) then,
  ) = _$MediaMetadataCopyWithImpl<$Res, MediaMetadata>;
  @useResult
  $Res call({
    String? contentType,
    int? size,
    DateTime? timeCreated,
    DateTime? updated,
    String? md5Hash,
    String? generation,
    String? metageneration,
  });
}

/// @nodoc
class _$MediaMetadataCopyWithImpl<$Res, $Val extends MediaMetadata>
    implements $MediaMetadataCopyWith<$Res> {
  _$MediaMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contentType = freezed,
    Object? size = freezed,
    Object? timeCreated = freezed,
    Object? updated = freezed,
    Object? md5Hash = freezed,
    Object? generation = freezed,
    Object? metageneration = freezed,
  }) {
    return _then(
      _value.copyWith(
            contentType: freezed == contentType
                ? _value.contentType
                : contentType // ignore: cast_nullable_to_non_nullable
                      as String?,
            size: freezed == size
                ? _value.size
                : size // ignore: cast_nullable_to_non_nullable
                      as int?,
            timeCreated: freezed == timeCreated
                ? _value.timeCreated
                : timeCreated // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updated: freezed == updated
                ? _value.updated
                : updated // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            md5Hash: freezed == md5Hash
                ? _value.md5Hash
                : md5Hash // ignore: cast_nullable_to_non_nullable
                      as String?,
            generation: freezed == generation
                ? _value.generation
                : generation // ignore: cast_nullable_to_non_nullable
                      as String?,
            metageneration: freezed == metageneration
                ? _value.metageneration
                : metageneration // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MediaMetadataImplCopyWith<$Res>
    implements $MediaMetadataCopyWith<$Res> {
  factory _$$MediaMetadataImplCopyWith(
    _$MediaMetadataImpl value,
    $Res Function(_$MediaMetadataImpl) then,
  ) = __$$MediaMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? contentType,
    int? size,
    DateTime? timeCreated,
    DateTime? updated,
    String? md5Hash,
    String? generation,
    String? metageneration,
  });
}

/// @nodoc
class __$$MediaMetadataImplCopyWithImpl<$Res>
    extends _$MediaMetadataCopyWithImpl<$Res, _$MediaMetadataImpl>
    implements _$$MediaMetadataImplCopyWith<$Res> {
  __$$MediaMetadataImplCopyWithImpl(
    _$MediaMetadataImpl _value,
    $Res Function(_$MediaMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contentType = freezed,
    Object? size = freezed,
    Object? timeCreated = freezed,
    Object? updated = freezed,
    Object? md5Hash = freezed,
    Object? generation = freezed,
    Object? metageneration = freezed,
  }) {
    return _then(
      _$MediaMetadataImpl(
        contentType: freezed == contentType
            ? _value.contentType
            : contentType // ignore: cast_nullable_to_non_nullable
                  as String?,
        size: freezed == size
            ? _value.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int?,
        timeCreated: freezed == timeCreated
            ? _value.timeCreated
            : timeCreated // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updated: freezed == updated
            ? _value.updated
            : updated // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        md5Hash: freezed == md5Hash
            ? _value.md5Hash
            : md5Hash // ignore: cast_nullable_to_non_nullable
                  as String?,
        generation: freezed == generation
            ? _value.generation
            : generation // ignore: cast_nullable_to_non_nullable
                  as String?,
        metageneration: freezed == metageneration
            ? _value.metageneration
            : metageneration // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaMetadataImpl implements _MediaMetadata {
  const _$MediaMetadataImpl({
    this.contentType,
    this.size,
    this.timeCreated,
    this.updated,
    this.md5Hash,
    this.generation,
    this.metageneration,
  });

  factory _$MediaMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaMetadataImplFromJson(json);

  @override
  final String? contentType;
  @override
  final int? size;
  @override
  final DateTime? timeCreated;
  @override
  final DateTime? updated;
  @override
  final String? md5Hash;
  @override
  final String? generation;
  @override
  final String? metageneration;

  @override
  String toString() {
    return 'MediaMetadata(contentType: $contentType, size: $size, timeCreated: $timeCreated, updated: $updated, md5Hash: $md5Hash, generation: $generation, metageneration: $metageneration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaMetadataImpl &&
            (identical(other.contentType, contentType) ||
                other.contentType == contentType) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.timeCreated, timeCreated) ||
                other.timeCreated == timeCreated) &&
            (identical(other.updated, updated) || other.updated == updated) &&
            (identical(other.md5Hash, md5Hash) || other.md5Hash == md5Hash) &&
            (identical(other.generation, generation) ||
                other.generation == generation) &&
            (identical(other.metageneration, metageneration) ||
                other.metageneration == metageneration));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    contentType,
    size,
    timeCreated,
    updated,
    md5Hash,
    generation,
    metageneration,
  );

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaMetadataImplCopyWith<_$MediaMetadataImpl> get copyWith =>
      __$$MediaMetadataImplCopyWithImpl<_$MediaMetadataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaMetadataImplToJson(this);
  }
}

abstract class _MediaMetadata implements MediaMetadata {
  const factory _MediaMetadata({
    final String? contentType,
    final int? size,
    final DateTime? timeCreated,
    final DateTime? updated,
    final String? md5Hash,
    final String? generation,
    final String? metageneration,
  }) = _$MediaMetadataImpl;

  factory _MediaMetadata.fromJson(Map<String, dynamic> json) =
      _$MediaMetadataImpl.fromJson;

  @override
  String? get contentType;
  @override
  int? get size;
  @override
  DateTime? get timeCreated;
  @override
  DateTime? get updated;
  @override
  String? get md5Hash;
  @override
  String? get generation;
  @override
  String? get metageneration;

  /// Create a copy of MediaMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaMetadataImplCopyWith<_$MediaMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
