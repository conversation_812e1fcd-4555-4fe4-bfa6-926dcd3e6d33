// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeviceModelImpl _$$DeviceModelImplFromJson(Map<String, dynamic> json) =>
    _$DeviceModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      lastActive: DateTime.parse(json['lastActive'] as String),
      isCurrentDevice: json['isCurrentDevice'] as bool,
      platform: json['platform'] as String?,
      version: json['version'] as String?,
      location: json['location'] as String?,
    );

Map<String, dynamic> _$$DeviceModelImplToJson(_$DeviceModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'lastActive': instance.lastActive.toIso8601String(),
      'isCurrentDevice': instance.isCurrentDevice,
      'platform': instance.platform,
      'version': instance.version,
      'location': instance.location,
    };
