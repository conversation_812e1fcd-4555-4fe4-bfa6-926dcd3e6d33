// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$whatsAppContactServiceHash() =>
    r'29ee19d07ddee9c1b6c7ae60cb546ac05ac33c34';

/// See also [whatsAppContactService].
@ProviderFor(whatsAppContactService)
final whatsAppContactServiceProvider =
    AutoDisposeProvider<WhatsAppContactService>.internal(
      whatsAppContactService,
      name: r'whatsAppContactServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$whatsAppContactServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WhatsAppContactServiceRef =
    AutoDisposeProviderRef<WhatsAppContactService>;
String _$whatsAppMessageServiceHash() =>
    r'3d0ab74846132c89f0e7f515a1351ef52e04a8fd';

/// See also [whatsAppMessageService].
@ProviderFor(whatsAppMessageService)
final whatsAppMessageServiceProvider =
    AutoDisposeProvider<WhatsAppMessageService>.internal(
      whatsAppMessageService,
      name: r'whatsAppMessageServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$whatsAppMessageServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WhatsAppMessageServiceRef =
    AutoDisposeProviderRef<WhatsAppMessageService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
