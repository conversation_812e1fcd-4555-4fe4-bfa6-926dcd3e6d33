// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'priest_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PriestModelImpl _$$PriestModelImplFromJson(Map<String, dynamic> json) =>
    _$PriestModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      specialization: json['specialization'] as String,
      description: json['description'] as String,
      rating: (json['rating'] as num).toDouble(),
      totalReviews: (json['totalReviews'] as num).toInt(),
      services: (json['services'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      languages: (json['languages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      certifications: (json['certifications'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      experience: (json['experience'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      profileImage: json['profileImage'] as String,
      coverImage: json['coverImage'] as String,
      contactInfo: json['contactInfo'] as Map<String, dynamic>,
      availability: json['availability'] as Map<String, dynamic>,
      pricing: json['pricing'] as Map<String, dynamic>,
      location: json['location'] as Map<String, dynamic>,
      isAvailable: json['isAvailable'] as bool,
      isVerified: json['isVerified'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$PriestModelImplToJson(_$PriestModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'specialization': instance.specialization,
      'description': instance.description,
      'rating': instance.rating,
      'totalReviews': instance.totalReviews,
      'services': instance.services,
      'languages': instance.languages,
      'certifications': instance.certifications,
      'experience': instance.experience,
      'profileImage': instance.profileImage,
      'coverImage': instance.coverImage,
      'contactInfo': instance.contactInfo,
      'availability': instance.availability,
      'pricing': instance.pricing,
      'location': instance.location,
      'isAvailable': instance.isAvailable,
      'isVerified': instance.isVerified,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };
